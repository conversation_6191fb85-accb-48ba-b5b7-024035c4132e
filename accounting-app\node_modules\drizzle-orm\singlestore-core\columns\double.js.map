{"version": 3, "sources": ["../../../src/singlestore-core/columns/double.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { SingleStoreColumnBuilderWithAutoIncrement, SingleStoreColumnWithAutoIncrement } from './common.ts';\n\nexport type SingleStoreDoubleBuilderInitial<TName extends string> = SingleStoreDoubleBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SingleStoreDouble';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreDoubleBuilder<T extends ColumnBuilderBaseConfig<'number', 'SingleStoreDouble'>>\n\textends SingleStoreColumnBuilderWithAutoIncrement<T, SingleStoreDoubleConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreDoubleBuilder';\n\n\tconstructor(name: T['name'], config: SingleStoreDoubleConfig | undefined) {\n\t\tsuper(name, 'number', 'SingleStoreDouble');\n\t\tthis.config.precision = config?.precision;\n\t\tthis.config.scale = config?.scale;\n\t\tthis.config.unsigned = config?.unsigned;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreDouble<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreDouble<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreDouble<T extends ColumnBaseConfig<'number', 'SingleStoreDouble'>>\n\textends SingleStoreColumnWithAutoIncrement<T, SingleStoreDoubleConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreDouble';\n\n\treadonly precision: number | undefined = this.config.precision;\n\treadonly scale: number | undefined = this.config.scale;\n\treadonly unsigned: boolean | undefined = this.config.unsigned;\n\n\tgetSQLType(): string {\n\t\tlet type = '';\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\ttype += `double(${this.precision},${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\ttype += 'double';\n\t\t} else {\n\t\t\ttype += `double(${this.precision})`;\n\t\t}\n\t\treturn this.unsigned ? `${type} unsigned` : type;\n\t}\n}\n\nexport interface SingleStoreDoubleConfig {\n\tprecision?: number;\n\tscale?: number;\n\tunsigned?: boolean;\n}\n\nexport function double(): SingleStoreDoubleBuilderInitial<''>;\nexport function double(\n\tconfig?: SingleStoreDoubleConfig,\n): SingleStoreDoubleBuilderInitial<''>;\nexport function double<TName extends string>(\n\tname: TName,\n\tconfig?: SingleStoreDoubleConfig,\n): SingleStoreDoubleBuilderInitial<TName>;\nexport function double(a?: string | SingleStoreDoubleConfig, b?: SingleStoreDoubleConfig) {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreDoubleConfig>(a, b);\n\treturn new SingleStoreDoubleBuilder(name, config);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,8BAA8B;AACvC,SAAS,2CAA2C,0CAA0C;AAYvF,MAAM,iCACJ,0CACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAA6C;AACzE,UAAM,MAAM,UAAU,mBAAmB;AACzC,SAAK,OAAO,YAAY,QAAQ;AAChC,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,OAAO,WAAW,QAAQ;AAAA,EAChC;AAAA;AAAA,EAGS,MACR,OACqD;AACrD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,0BACJ,mCACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEvC,YAAgC,KAAK,OAAO;AAAA,EAC5C,QAA4B,KAAK,OAAO;AAAA,EACxC,WAAgC,KAAK,OAAO;AAAA,EAErD,aAAqB;AACpB,QAAI,OAAO;AACX,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,cAAQ,UAAU,KAAK,SAAS,IAAI,KAAK,KAAK;AAAA,IAC/C,WAAW,KAAK,cAAc,QAAW;AACxC,cAAQ;AAAA,IACT,OAAO;AACN,cAAQ,UAAU,KAAK,SAAS;AAAA,IACjC;AACA,WAAO,KAAK,WAAW,GAAG,IAAI,cAAc;AAAA,EAC7C;AACD;AAgBO,SAAS,OAAO,GAAsC,GAA6B;AACzF,QAAM,EAAE,MAAM,OAAO,IAAI,uBAAgD,GAAG,CAAC;AAC7E,SAAO,IAAI,yBAAyB,MAAM,MAAM;AACjD;", "names": []}