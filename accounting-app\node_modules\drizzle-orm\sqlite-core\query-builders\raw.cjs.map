{"version": 3, "sources": ["../../../src/sqlite-core/query-builders/raw.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport type { SQL, SQLWrapper } from '~/sql/sql.ts';\nimport type { SQLiteAsyncDialect } from '../dialect.ts';\n\ntype SQLiteRawAction = 'all' | 'get' | 'values' | 'run';\nexport interface SQLiteRawConfig {\n\taction: SQLiteRawAction;\n}\n\nexport interface SQLiteRaw<TResult> extends QueryPromise<TResult>, RunnableQuery<TResult, 'sqlite'>, SQLWrapper {}\n\nexport class SQLiteRaw<TResult> extends QueryPromise<TResult>\n\timplements RunnableQuery<TResult, 'sqlite'>, SQLWrapper, PreparedQuery\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteRaw';\n\n\tdeclare readonly _: {\n\t\treadonly dialect: 'sqlite';\n\t\treadonly result: TResult;\n\t};\n\n\t/** @internal */\n\tconfig: SQLiteRawConfig;\n\n\tconstructor(\n\t\tpublic execute: () => Promise<TResult>,\n\t\t/** @internal */\n\t\tpublic getSQL: () => SQL,\n\t\taction: SQLiteRawAction,\n\t\tprivate dialect: SQLiteAsyncDialect,\n\t\tprivate mapBatchResult: (result: unknown) => unknown,\n\t) {\n\t\tsuper();\n\t\tthis.config = { action };\n\t}\n\n\tgetQuery() {\n\t\treturn { ...this.dialect.sqlToQuery(this.getSQL()), method: this.config.action };\n\t}\n\n\tmapResult(result: unknown, isFromBatch?: boolean) {\n\t\treturn isFromBatch ? this.mapBatchResult(result) : result;\n\t}\n\n\t_prepare(): PreparedQuery {\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn false;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAC3B,2BAA6B;AAatB,MAAM,kBAA2B,kCAExC;AAAA,EAWC,YACQ,SAEA,QACP,QACQ,SACA,gBACP;AACD,UAAM;AAPC;AAEA;AAEC;AACA;AAGR,SAAK,SAAS,EAAE,OAAO;AAAA,EACxB;AAAA,EApBA,QAA0B,wBAAU,IAAY;AAAA;AAAA,EAQhD;AAAA,EAcA,WAAW;AACV,WAAO,EAAE,GAAG,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,QAAQ,KAAK,OAAO,OAAO;AAAA,EAChF;AAAA,EAEA,UAAU,QAAiB,aAAuB;AACjD,WAAO,cAAc,KAAK,eAAe,MAAM,IAAI;AAAA,EACpD;AAAA,EAEA,WAA0B;AACzB,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO;AAAA,EACR;AACD;", "names": []}