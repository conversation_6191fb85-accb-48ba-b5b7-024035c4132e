'use client';

import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
} from '@mui/material';
import {
  Business as BusinessIcon,
  AccountBalance as AccountBalanceIcon,
  TrendingUp as TrendingUpIcon,
  Receipt as ReceiptIcon,
  Add as AddIcon,
  CalendarToday as CalendarIcon,
} from '@mui/icons-material';
import { AdminLayout } from '@/components/layout/AdminLayout';
import { useAuth } from '@/contexts/AuthContext';
import { useApp } from '@/contexts/AppContext';
import { useRouter } from 'next/navigation';

export default function DashboardPage() {
  const { user } = useAuth();
  const { companies, currentCompany, financialYears, currentFinancialYear } = useApp();
  const router = useRouter();

  const stats = [
    {
      title: 'Total Companies',
      value: companies.length,
      icon: <BusinessIcon sx={{ fontSize: 40, color: '#3498db' }} />,
      color: '#3498db',
    },
    {
      title: 'Active Financial Years',
      value: financialYears.filter(year => year.is_active).length,
      icon: <CalendarIcon sx={{ fontSize: 40, color: '#2ecc71' }} />,
      color: '#2ecc71',
    },
    {
      title: 'Total Accounts',
      value: 0, // This would come from actual data
      icon: <AccountBalanceIcon sx={{ fontSize: 40, color: '#e74c3c' }} />,
      color: '#e74c3c',
    },
    {
      title: 'Journal Entries',
      value: 0, // This would come from actual data
      icon: <ReceiptIcon sx={{ fontSize: 40, color: '#f39c12' }} />,
      color: '#f39c12',
    },
  ];

  const recentActivities = [
    {
      title: 'Company Created',
      description: 'New company "ABC Corp" was created',
      time: '2 hours ago',
      type: 'company',
    },
    {
      title: 'Financial Year Added',
      description: 'FY 2024-25 added to ABC Corp',
      time: '3 hours ago',
      type: 'year',
    },
    {
      title: 'Account Created',
      description: 'Cash account added to chart of accounts',
      time: '1 day ago',
      type: 'account',
    },
  ];

  const quickActions = [
    {
      title: 'Create Company',
      description: 'Add a new company to manage',
      icon: <BusinessIcon />,
      action: () => router.push('/companies'),
      color: '#3498db',
    },
    {
      title: 'Add Financial Year',
      description: 'Create a new financial year',
      icon: <CalendarIcon />,
      action: () => router.push('/companies'),
      color: '#2ecc71',
    },
    {
      title: 'Chart of Accounts',
      description: 'Manage your accounts',
      icon: <AccountBalanceIcon />,
      action: () => router.push('/accounting/accounts'),
      color: '#e74c3c',
    },
    {
      title: 'Journal Entry',
      description: 'Record a new transaction',
      icon: <ReceiptIcon />,
      action: () => router.push('/accounting/journal'),
      color: '#f39c12',
    },
  ];

  return (
    <AdminLayout title="Dashboard">
      <Box>
        {/* Welcome Section */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            Welcome back, {user?.name}!
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Here's what's happening with your accounting today.
          </Typography>
        </Box>

        {/* Current Context */}
        {currentCompany && (
          <Paper sx={{ p: 3, mb: 4, backgroundColor: '#e3f2fd' }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
              Current Context
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Chip
                icon={<BusinessIcon />}
                label={`Company: ${currentCompany.name}`}
                color="primary"
                variant="filled"
              />
              {currentFinancialYear && (
                <Chip
                  icon={<CalendarIcon />}
                  label={`FY: ${currentFinancialYear.name}`}
                  color="secondary"
                  variant="filled"
                />
              )}
            </Box>
          </Paper>
        )}

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {stats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    {stat.icon}
                    <Box sx={{ ml: 2 }}>
                      <Typography variant="h4" sx={{ fontWeight: 'bold', color: stat.color }}>
                        {stat.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {stat.title}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Grid container spacing={3}>
          {/* Quick Actions */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                  Quick Actions
                </Typography>
                <Grid container spacing={2}>
                  {quickActions.map((action, index) => (
                    <Grid item xs={12} sm={6} key={index}>
                      <Paper
                        sx={{
                          p: 2,
                          cursor: 'pointer',
                          transition: 'all 0.2s',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: 3,
                          },
                        }}
                        onClick={action.action}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Box sx={{ color: action.color, mr: 1 }}>
                            {action.icon}
                          </Box>
                          <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                            {action.title}
                          </Typography>
                        </Box>
                        <Typography variant="caption" color="text.secondary">
                          {action.description}
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Recent Activities */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                  Recent Activities
                </Typography>
                <List>
                  {recentActivities.map((activity, index) => (
                    <ListItem key={index} sx={{ px: 0 }}>
                      <ListItemIcon>
                        <Box
                          sx={{
                            width: 40,
                            height: 40,
                            borderRadius: '50%',
                            backgroundColor: '#f5f5f5',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          {activity.type === 'company' && <BusinessIcon color="primary" />}
                          {activity.type === 'year' && <CalendarIcon color="secondary" />}
                          {activity.type === 'account' && <AccountBalanceIcon color="error" />}
                        </Box>
                      </ListItemIcon>
                      <ListItemText
                        primary={activity.title}
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {activity.description}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {activity.time}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </AdminLayout>
  );
}
