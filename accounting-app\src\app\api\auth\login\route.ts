/**
 * Login API Route
 *
 * Secure authentication endpoint with JWT token generation,
 * comprehensive validation, and proper error handling.
 *
 * Following DRY principle: Single login endpoint
 * Following YAGNI principle: Only essential login features
 */

import { NextRequest, NextResponse } from 'next/server';
import { userRepository } from '@/database/repository';
import { generateTokenPair, setAuthCookies } from '@/lib/jwt';

/**
 * Login request body interface
 */
interface LoginRequest {
  email: string;
  password: string;
}

/**
 * Login response interface
 */
interface LoginResponse {
  success: boolean;
  message: string;
  user?: {
    id: string;
    email: string;
    name: string;
  };
}

/**
 * POST /api/auth/login
 *
 * Authenticate user with email and password
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body: LoginRequest = await request.json();

    // Validate request data
    if (!body || typeof body !== 'object') {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid request body',
        },
        { status: 400 }
      );
    }

    const { email, password } = body;

    // Validate required fields
    if (!email || !password) {
      return NextResponse.json(
        {
          success: false,
          message: 'Email and password are required',
        },
        { status: 400 }
      );
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid email format',
        },
        { status: 400 }
      );
    }

    // Rate limiting check (basic implementation)
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
    console.log(`Login attempt from IP: ${clientIP} for email: ${email}`);

    // Authenticate user
    const user = await userRepository.authenticate(email, password);

    if (!user) {
      // Return generic error to prevent user enumeration
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid email or password',
        },
        { status: 401 }
      );
    }

    // Generate JWT tokens
    const tokens = generateTokenPair({
      userId: user.id,
      email: user.email,
      name: user.name,
    });

    // Create response
    const response = NextResponse.json<LoginResponse>(
      {
        success: true,
        message: 'Login successful',
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
        },
      },
      { status: 200 }
    );

    // Set authentication cookies
    setAuthCookies(response, tokens);

    // Log successful login
    console.log(`Successful login for user: ${user.email} (ID: ${user.id})`);

    return response;

  } catch (error) {
    console.error('Login API error:', error);

    // Return generic error message
    return NextResponse.json(
      {
        success: false,
        message: 'An internal server error occurred',
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/auth/login
 *
 * Return method not allowed for GET requests
 */
export async function GET(): Promise<NextResponse> {
  return NextResponse.json(
    {
      success: false,
      message: 'Method not allowed. Use POST to login.',
    },
    { status: 405 }
  );
}

/**
 * OPTIONS /api/auth/login
 *
 * Handle CORS preflight requests
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
