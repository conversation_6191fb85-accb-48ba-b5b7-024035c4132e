/**
 * User Repository
 * 
 * This file provides user-specific database operations with Odoo-like features
 * including pagination, filtering, sorting, and search capabilities.
 * 
 * Following DRY principle: Extends base repository
 * Following YAGNI principle: Only essential user operations
 */

import { BaseRepository, QueryOptions, FilterCondition } from './base';
import { users, SelectUser, InsertUser, PublicUser } from '../schemas';
import bcrypt from 'bcryptjs';
import { eq } from 'drizzle-orm';

/**
 * User creation input
 */
export interface CreateUserInput {
  email: string;
  name: string;
  password: string;
}

/**
 * User update input
 */
export interface UpdateUserInput {
  email?: string;
  name?: string;
  password?: string;
}

/**
 * User search and filter options
 */
export interface UserQueryOptions extends QueryOptions {
  search?: {
    query?: string;
    fields?: ('name' | 'email')[];
  };
}

/**
 * User repository with authentication and user management features
 */
export class UserRepository extends BaseRepository<SelectUser> {
  constructor() {
    super('users', users);
  }

  /**
   * Create a new user with hashed password
   */
  async createUser(userData: CreateUserInput): Promise<PublicUser> {
    // Check if user already exists
    const existingUser = await this.findByEmail(userData.email);
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    // Hash password
    const passwordHash = await bcrypt.hash(userData.password, 10);

    // Generate user ID
    const userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Create user data
    const userToCreate: InsertUser = {
      id: userId,
      email: userData.email.toLowerCase().trim(),
      name: userData.name.trim(),
      passwordHash,
    };

    // Insert user
    const createdUser = await this.create(userToCreate);

    // Return user without password hash
    return this.toPublicUser(createdUser);
  }

  /**
   * Update user with optional password change
   */
  async updateUser(id: string, userData: UpdateUserInput): Promise<PublicUser | null> {
    const updateData: Partial<SelectUser> = {};

    if (userData.email) {
      // Check if email is already taken by another user
      const existingUser = await this.findByEmail(userData.email);
      if (existingUser && existingUser.id !== id) {
        throw new Error('Email is already taken by another user');
      }
      updateData.email = userData.email.toLowerCase().trim();
    }

    if (userData.name) {
      updateData.name = userData.name.trim();
    }

    if (userData.password) {
      updateData.passwordHash = await bcrypt.hash(userData.password, 10);
    }

    // Add updated timestamp
    updateData.updatedAt = new Date().toISOString();

    const updatedUser = await this.update(id, updateData);
    return updatedUser ? this.toPublicUser(updatedUser) : null;
  }

  /**
   * Find user by email
   */
  async findByEmail(email: string): Promise<SelectUser | null> {
    const conditions: FilterCondition[] = [
      { field: 'email', operator: 'equals', value: email.toLowerCase().trim() }
    ];
    
    return await this.findOne(conditions);
  }

  /**
   * Authenticate user with email and password
   */
  async authenticate(email: string, password: string): Promise<PublicUser | null> {
    const user = await this.findByEmail(email);
    
    if (!user) {
      return null;
    }

    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    
    if (!isPasswordValid) {
      return null;
    }

    return this.toPublicUser(user);
  }

  /**
   * Change user password
   */
  async changePassword(id: string, currentPassword: string, newPassword: string): Promise<boolean> {
    const user = await this.findById(id);
    
    if (!user) {
      throw new Error('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
    
    if (!isCurrentPasswordValid) {
      throw new Error('Current password is incorrect');
    }

    // Hash new password
    const newPasswordHash = await bcrypt.hash(newPassword, 10);

    // Update password
    const updated = await this.update(id, {
      passwordHash: newPasswordHash,
      updatedAt: new Date().toISOString(),
    });

    return updated !== null;
  }

  /**
   * Search users with enhanced options
   */
  async searchUsers(options: UserQueryOptions = {}) {
    // Set default search fields if not provided
    if (options.search && !options.search.fields) {
      options.search.fields = ['name', 'email'];
    }

    const result = await this.findAll(options);

    // Convert users to public format
    return {
      ...result,
      data: result.data.map(user => this.toPublicUser(user)),
    };
  }

  /**
   * Get user statistics
   */
  async getUserStats(): Promise<{
    totalUsers: number;
    usersCreatedToday: number;
    usersCreatedThisWeek: number;
    usersCreatedThisMonth: number;
  }> {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
    const monthAgo = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();

    const [
      totalUsers,
      usersCreatedToday,
      usersCreatedThisWeek,
      usersCreatedThisMonth,
    ] = await Promise.all([
      this.count(),
      this.count([{ field: 'createdAt', operator: 'greater_than_or_equal', value: today }]),
      this.count([{ field: 'createdAt', operator: 'greater_than_or_equal', value: weekAgo }]),
      this.count([{ field: 'createdAt', operator: 'greater_than_or_equal', value: monthAgo }]),
    ]);

    return {
      totalUsers,
      usersCreatedToday,
      usersCreatedThisWeek,
      usersCreatedThisMonth,
    };
  }

  /**
   * Get recently created users
   */
  async getRecentUsers(limit: number = 10) {
    const options: UserQueryOptions = {
      pagination: { pageSize: limit },
      sort: [{ field: 'createdAt', order: 'desc' }],
    };

    const result = await this.searchUsers(options);
    return result.data;
  }

  /**
   * Check if email is available
   */
  async isEmailAvailable(email: string, excludeUserId?: string): Promise<boolean> {
    const conditions: FilterCondition[] = [
      { field: 'email', operator: 'equals', value: email.toLowerCase().trim() }
    ];

    if (excludeUserId) {
      conditions.push({ field: 'id', operator: 'not_equals', value: excludeUserId });
    }

    const existingUser = await this.findOne(conditions);
    return existingUser === null;
  }

  /**
   * Bulk create users
   */
  async bulkCreateUsers(usersData: CreateUserInput[]): Promise<PublicUser[]> {
    const usersToCreate: InsertUser[] = [];

    for (const userData of usersData) {
      // Check if user already exists
      const existingUser = await this.findByEmail(userData.email);
      if (existingUser) {
        throw new Error(`User with email ${userData.email} already exists`);
      }

      // Hash password
      const passwordHash = await bcrypt.hash(userData.password, 10);

      // Generate user ID
      const userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      usersToCreate.push({
        id: userId,
        email: userData.email.toLowerCase().trim(),
        name: userData.name.trim(),
        passwordHash,
      });
    }

    const createdUsers = await this.bulkCreate(usersToCreate);
    return createdUsers.map(user => this.toPublicUser(user));
  }

  /**
   * Convert user to public format (without password hash)
   */
  private toPublicUser(user: SelectUser): PublicUser {
    const { passwordHash, ...publicUser } = user;
    return publicUser;
  }

  /**
   * Validate user data
   */
  validateUserData(userData: CreateUserInput | UpdateUserInput): string[] {
    const errors: string[] = [];

    if ('email' in userData && userData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(userData.email)) {
        errors.push('Invalid email format');
      }
    }

    if ('name' in userData && userData.name) {
      if (userData.name.trim().length < 1) {
        errors.push('Name is required');
      }
      if (userData.name.trim().length > 100) {
        errors.push('Name must be less than 100 characters');
      }
    }

    if ('password' in userData && userData.password) {
      if (userData.password.length < 6) {
        errors.push('Password must be at least 6 characters long');
      }
      if (userData.password.length > 128) {
        errors.push('Password must be less than 128 characters');
      }
    }

    return errors;
  }
}
