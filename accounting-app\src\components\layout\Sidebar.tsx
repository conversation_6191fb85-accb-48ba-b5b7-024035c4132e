'use client';

import React, { useState } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Divider,
  Collapse,
  Avatar,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Business as BusinessIcon,
  AccountBalance as AccountBalanceIcon,
  Receipt as ReceiptIcon,
  Assessment as AssessmentIcon,
  Settings as SettingsIcon,
  ExpandLess,
  ExpandMore,
  CalendarToday as CalendarIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useApp } from '@/contexts/AppContext';

interface SidebarProps {
  onClose?: () => void;
}

export function Sidebar({ onClose }: SidebarProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user } = useAuth();
  const { 
    companies, 
    currentCompany, 
    financialYears, 
    currentFinancialYear,
    setCurrentCompany,
    setCurrentFinancialYear 
  } = useApp();

  const [openAccounting, setOpenAccounting] = useState(true);
  const [openReports, setOpenReports] = useState(false);

  const handleNavigation = (path: string) => {
    router.push(path);
    onClose?.();
  };

  const menuItems = [
    {
      text: 'Dashboard',
      icon: <DashboardIcon />,
      path: '/dashboard',
    },
    {
      text: 'Companies',
      icon: <BusinessIcon />,
      path: '/companies',
    },
  ];

  const accountingItems = [
    {
      text: 'Chart of Accounts',
      icon: <AccountBalanceIcon />,
      path: '/accounting/accounts',
    },
    {
      text: 'Journal Entries',
      icon: <ReceiptIcon />,
      path: '/accounting/journal',
    },
    {
      text: 'Ledger',
      icon: <AccountBalanceIcon />,
      path: '/accounting/ledger',
    },
  ];

  const reportItems = [
    {
      text: 'Trial Balance',
      icon: <AssessmentIcon />,
      path: '/reports/trial-balance',
    },
    {
      text: 'Profit & Loss',
      icon: <AssessmentIcon />,
      path: '/reports/profit-loss',
    },
    {
      text: 'Balance Sheet',
      icon: <AssessmentIcon />,
      path: '/reports/balance-sheet',
    },
  ];

  return (
    <Box sx={{ height: '100%', backgroundColor: '#2c3e50', color: '#ecf0f1' }}>
      {/* Logo/Brand */}
      <Box sx={{ p: 2, textAlign: 'center', borderBottom: '1px solid #34495e' }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#3498db' }}>
          AccountingApp
        </Typography>
      </Box>

      {/* User Info */}
      {user && (
        <Box sx={{ p: 2, borderBottom: '1px solid #34495e' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar sx={{ bgcolor: '#3498db', mr: 2 }}>
              {user.name.charAt(0).toUpperCase()}
            </Avatar>
            <Box>
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                {user.name}
              </Typography>
              <Typography variant="caption" sx={{ color: '#bdc3c7' }}>
                {user.email}
              </Typography>
            </Box>
          </Box>

          {/* Company Selector */}
          {companies.length > 0 && (
            <FormControl fullWidth size="small" sx={{ mb: 1 }}>
              <InputLabel sx={{ color: '#bdc3c7' }}>Company</InputLabel>
              <Select
                value={currentCompany?.id || ''}
                onChange={(e) => {
                  const company = companies.find(c => c.id === e.target.value);
                  setCurrentCompany(company || null);
                }}
                sx={{
                  color: '#ecf0f1',
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#34495e',
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#3498db',
                  },
                }}
              >
                {companies.map((company) => (
                  <MenuItem key={company.id} value={company.id}>
                    {company.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}

          {/* Financial Year Selector */}
          {financialYears.length > 0 && (
            <FormControl fullWidth size="small">
              <InputLabel sx={{ color: '#bdc3c7' }}>Financial Year</InputLabel>
              <Select
                value={currentFinancialYear?.id || ''}
                onChange={(e) => {
                  const year = financialYears.find(y => y.id === e.target.value);
                  setCurrentFinancialYear(year || null);
                }}
                sx={{
                  color: '#ecf0f1',
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#34495e',
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#3498db',
                  },
                }}
              >
                {financialYears.map((year) => (
                  <MenuItem key={year.id} value={year.id}>
                    {year.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
        </Box>
      )}

      {/* Navigation Menu */}
      <List sx={{ flexGrow: 1 }}>
        {menuItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              onClick={() => handleNavigation(item.path)}
              selected={pathname === item.path}
              sx={{
                '&.Mui-selected': {
                  backgroundColor: '#3498db',
                  '&:hover': {
                    backgroundColor: '#2980b9',
                  },
                },
                '&:hover': {
                  backgroundColor: '#34495e',
                },
              }}
            >
              <ListItemIcon sx={{ color: 'inherit' }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}

        <Divider sx={{ my: 1, borderColor: '#34495e' }} />

        {/* Accounting Section */}
        <ListItem disablePadding>
          <ListItemButton
            onClick={() => setOpenAccounting(!openAccounting)}
            sx={{
              '&:hover': {
                backgroundColor: '#34495e',
              },
            }}
          >
            <ListItemIcon sx={{ color: 'inherit' }}>
              <AccountBalanceIcon />
            </ListItemIcon>
            <ListItemText primary="Accounting" />
            {openAccounting ? <ExpandLess /> : <ExpandMore />}
          </ListItemButton>
        </ListItem>
        <Collapse in={openAccounting} timeout="auto" unmountOnExit>
          <List component="div" disablePadding>
            {accountingItems.map((item) => (
              <ListItem key={item.text} disablePadding>
                <ListItemButton
                  onClick={() => handleNavigation(item.path)}
                  selected={pathname === item.path}
                  sx={{
                    pl: 4,
                    '&.Mui-selected': {
                      backgroundColor: '#3498db',
                      '&:hover': {
                        backgroundColor: '#2980b9',
                      },
                    },
                    '&:hover': {
                      backgroundColor: '#34495e',
                    },
                  }}
                >
                  <ListItemIcon sx={{ color: 'inherit', minWidth: 36 }}>
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText primary={item.text} />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        </Collapse>

        {/* Reports Section */}
        <ListItem disablePadding>
          <ListItemButton
            onClick={() => setOpenReports(!openReports)}
            sx={{
              '&:hover': {
                backgroundColor: '#34495e',
              },
            }}
          >
            <ListItemIcon sx={{ color: 'inherit' }}>
              <AssessmentIcon />
            </ListItemIcon>
            <ListItemText primary="Reports" />
            {openReports ? <ExpandLess /> : <ExpandMore />}
          </ListItemButton>
        </ListItem>
        <Collapse in={openReports} timeout="auto" unmountOnExit>
          <List component="div" disablePadding>
            {reportItems.map((item) => (
              <ListItem key={item.text} disablePadding>
                <ListItemButton
                  onClick={() => handleNavigation(item.path)}
                  selected={pathname === item.path}
                  sx={{
                    pl: 4,
                    '&.Mui-selected': {
                      backgroundColor: '#3498db',
                      '&:hover': {
                        backgroundColor: '#2980b9',
                      },
                    },
                    '&:hover': {
                      backgroundColor: '#34495e',
                    },
                  }}
                >
                  <ListItemIcon sx={{ color: 'inherit', minWidth: 36 }}>
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText primary={item.text} />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        </Collapse>

        <Divider sx={{ my: 1, borderColor: '#34495e' }} />

        {/* Settings */}
        <ListItem disablePadding>
          <ListItemButton
            onClick={() => handleNavigation('/settings')}
            selected={pathname === '/settings'}
            sx={{
              '&.Mui-selected': {
                backgroundColor: '#3498db',
                '&:hover': {
                  backgroundColor: '#2980b9',
                },
              },
              '&:hover': {
                backgroundColor: '#34495e',
              },
            }}
          >
            <ListItemIcon sx={{ color: 'inherit' }}>
              <SettingsIcon />
            </ListItemIcon>
            <ListItemText primary="Settings" />
          </ListItemButton>
        </ListItem>
      </List>
    </Box>
  );
}
