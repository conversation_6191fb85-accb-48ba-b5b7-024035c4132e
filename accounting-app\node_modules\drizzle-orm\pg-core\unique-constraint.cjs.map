{"version": 3, "sources": ["../../src/pg-core/unique-constraint.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { TableName } from '~/table.utils.ts';\nimport type { PgColumn } from './columns/index.ts';\nimport type { PgTable } from './table.ts';\n\nexport function unique(name?: string): UniqueOnConstraintBuilder {\n\treturn new UniqueOnConstraintBuilder(name);\n}\n\nexport function uniqueKeyName(table: PgTable, columns: string[]) {\n\treturn `${table[TableName]}_${columns.join('_')}_unique`;\n}\n\nexport class UniqueConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'PgUniqueConstraintBuilder';\n\n\t/** @internal */\n\tcolumns: PgColumn[];\n\t/** @internal */\n\tnullsNotDistinctConfig = false;\n\n\tconstructor(\n\t\tcolumns: PgColumn[],\n\t\tprivate name?: string,\n\t) {\n\t\tthis.columns = columns;\n\t}\n\n\tnullsNotDistinct() {\n\t\tthis.nullsNotDistinctConfig = true;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: PgTable): UniqueConstraint {\n\t\treturn new UniqueConstraint(table, this.columns, this.nullsNotDistinctConfig, this.name);\n\t}\n}\n\nexport class UniqueOnConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'PgUniqueOnConstraintBuilder';\n\n\t/** @internal */\n\tname?: string;\n\n\tconstructor(\n\t\tname?: string,\n\t) {\n\t\tthis.name = name;\n\t}\n\n\ton(...columns: [PgColumn, ...PgColumn[]]) {\n\t\treturn new UniqueConstraintBuilder(columns, this.name);\n\t}\n}\n\nexport class UniqueConstraint {\n\tstatic readonly [entityKind]: string = 'PgUniqueConstraint';\n\n\treadonly columns: PgColumn[];\n\treadonly name?: string;\n\treadonly nullsNotDistinct: boolean = false;\n\n\tconstructor(readonly table: PgTable, columns: PgColumn[], nullsNotDistinct: boolean, name?: string) {\n\t\tthis.columns = columns;\n\t\tthis.name = name ?? uniqueKeyName(this.table, this.columns.map((column) => column.name));\n\t\tthis.nullsNotDistinct = nullsNotDistinct;\n\t}\n\n\tgetName() {\n\t\treturn this.name;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAC3B,yBAA0B;AAInB,SAAS,OAAO,MAA0C;AAChE,SAAO,IAAI,0BAA0B,IAAI;AAC1C;AAEO,SAAS,cAAc,OAAgB,SAAmB;AAChE,SAAO,GAAG,MAAM,4BAAS,CAAC,IAAI,QAAQ,KAAK,GAAG,CAAC;AAChD;AAEO,MAAM,wBAAwB;AAAA,EAQpC,YACC,SACQ,MACP;AADO;AAER,SAAK,UAAU;AAAA,EAChB;AAAA,EAZA,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC;AAAA;AAAA,EAEA,yBAAyB;AAAA,EASzB,mBAAmB;AAClB,SAAK,yBAAyB;AAC9B,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,MAAM,OAAkC;AACvC,WAAO,IAAI,iBAAiB,OAAO,KAAK,SAAS,KAAK,wBAAwB,KAAK,IAAI;AAAA,EACxF;AACD;AAEO,MAAM,0BAA0B;AAAA,EACtC,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC;AAAA,EAEA,YACC,MACC;AACD,SAAK,OAAO;AAAA,EACb;AAAA,EAEA,MAAM,SAAoC;AACzC,WAAO,IAAI,wBAAwB,SAAS,KAAK,IAAI;AAAA,EACtD;AACD;AAEO,MAAM,iBAAiB;AAAA,EAO7B,YAAqB,OAAgB,SAAqB,kBAA2B,MAAe;AAA/E;AACpB,SAAK,UAAU;AACf,SAAK,OAAO,QAAQ,cAAc,KAAK,OAAO,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC;AACvF,SAAK,mBAAmB;AAAA,EACzB;AAAA,EAVA,QAAiB,wBAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAAA,EACA,mBAA4B;AAAA,EAQrC,UAAU;AACT,WAAO,KAAK;AAAA,EACb;AACD;", "names": []}