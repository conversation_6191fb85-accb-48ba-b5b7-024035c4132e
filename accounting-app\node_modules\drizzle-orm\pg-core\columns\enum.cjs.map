{"version": 3, "sources": ["../../../src/pg-core/columns/enum.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport type { NonArray, Writable } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\n// Enum as ts enum\n\nexport type PgEnumObjectColumnBuilderInitial<TName extends string, TValues extends object> = PgEnumObjectColumnBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgEnumObjectColumn';\n\tdata: TValues[keyof TValues];\n\tenumValues: string[];\n\tdriverParam: string;\n}>;\n\nexport interface PgEnumObject<TValues extends object> {\n\t(): PgEnumObjectColumnBuilderInitial<'', TValues>;\n\t<TName extends string>(name: TName): PgEnumObjectColumnBuilderInitial<TName, TValues>;\n\t<TName extends string>(name?: TName): PgEnumObjectColumnBuilderInitial<TName, TValues>;\n\n\treadonly enumName: string;\n\treadonly enumValues: string[];\n\treadonly schema: string | undefined;\n\t/** @internal */\n\t[isPgEnumSym]: true;\n}\n\nexport class PgEnumObjectColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'PgEnumObjectColumn'> & { enumValues: string[] },\n> extends PgColumnBuilder<T, { enum: PgEnumObject<any> }> {\n\tstatic override readonly [entityKind]: string = 'PgEnumObjectColumnBuilder';\n\n\tconstructor(name: T['name'], enumInstance: PgEnumObject<any>) {\n\t\tsuper(name, 'string', 'PgEnumObjectColumn');\n\t\tthis.config.enum = enumInstance;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgEnumObjectColumn<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgEnumObjectColumn<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgEnumObjectColumn<T extends ColumnBaseConfig<'string', 'PgEnumObjectColumn'> & { enumValues: object }>\n\textends PgColumn<T, { enum: PgEnumObject<object> }>\n{\n\tstatic override readonly [entityKind]: string = 'PgEnumObjectColumn';\n\n\treadonly enum;\n\toverride readonly enumValues = this.config.enum.enumValues;\n\n\tconstructor(\n\t\ttable: AnyPgTable<{ name: T['tableName'] }>,\n\t\tconfig: PgEnumObjectColumnBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.enum = config.enum;\n\t}\n\n\tgetSQLType(): string {\n\t\treturn this.enum.enumName;\n\t}\n}\n\n// Enum as string union\n\nexport type PgEnumColumnBuilderInitial<TName extends string, TValues extends [string, ...string[]]> =\n\tPgEnumColumnBuilder<{\n\t\tname: TName;\n\t\tdataType: 'string';\n\t\tcolumnType: 'PgEnumColumn';\n\t\tdata: TValues[number];\n\t\tenumValues: TValues;\n\t\tdriverParam: string;\n\t}>;\n\nconst isPgEnumSym = Symbol.for('drizzle:isPgEnum');\nexport interface PgEnum<TValues extends [string, ...string[]]> {\n\t(): PgEnumColumnBuilderInitial<'', TValues>;\n\t<TName extends string>(name: TName): PgEnumColumnBuilderInitial<TName, TValues>;\n\t<TName extends string>(name?: TName): PgEnumColumnBuilderInitial<TName, TValues>;\n\n\treadonly enumName: string;\n\treadonly enumValues: TValues;\n\treadonly schema: string | undefined;\n\t/** @internal */\n\t[isPgEnumSym]: true;\n}\n\nexport function isPgEnum(obj: unknown): obj is PgEnum<[string, ...string[]]> {\n\treturn !!obj && typeof obj === 'function' && isPgEnumSym in obj && obj[isPgEnumSym] === true;\n}\n\nexport class PgEnumColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'PgEnumColumn'> & { enumValues: [string, ...string[]] },\n> extends PgColumnBuilder<T, { enum: PgEnum<T['enumValues']> }> {\n\tstatic override readonly [entityKind]: string = 'PgEnumColumnBuilder';\n\n\tconstructor(name: T['name'], enumInstance: PgEnum<T['enumValues']>) {\n\t\tsuper(name, 'string', 'PgEnumColumn');\n\t\tthis.config.enum = enumInstance;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgEnumColumn<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgEnumColumn<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgEnumColumn<T extends ColumnBaseConfig<'string', 'PgEnumColumn'> & { enumValues: [string, ...string[]] }>\n\textends PgColumn<T, { enum: PgEnum<T['enumValues']> }>\n{\n\tstatic override readonly [entityKind]: string = 'PgEnumColumn';\n\n\treadonly enum = this.config.enum;\n\toverride readonly enumValues = this.config.enum.enumValues;\n\n\tconstructor(\n\t\ttable: AnyPgTable<{ name: T['tableName'] }>,\n\t\tconfig: PgEnumColumnBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.enum = config.enum;\n\t}\n\n\tgetSQLType(): string {\n\t\treturn this.enum.enumName;\n\t}\n}\n\nexport function pgEnum<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tenumName: string,\n\tvalues: T | Writable<T>,\n): PgEnum<Writable<T>>;\n\nexport function pgEnum<E extends Record<string, string>>(\n\tenumName: string,\n\tenumObj: NonArray<E>,\n): PgEnumObject<E>;\n\nexport function pgEnum(\n\tenumName: any,\n\tinput: any,\n): any {\n\treturn Array.isArray(input)\n\t\t? pgEnumWithSchema(enumName, [...input] as [string, ...string[]], undefined)\n\t\t: pgEnumObjectWithSchema(enumName, input, undefined);\n}\n\n/** @internal */\nexport function pgEnumWithSchema<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tenumName: string,\n\tvalues: T | Writable<T>,\n\tschema?: string,\n): PgEnum<Writable<T>> {\n\tconst enumInstance: PgEnum<Writable<T>> = Object.assign(\n\t\t<TName extends string>(name?: TName): PgEnumColumnBuilderInitial<TName, Writable<T>> =>\n\t\t\tnew PgEnumColumnBuilder(name ?? '' as TName, enumInstance),\n\t\t{\n\t\t\tenumName,\n\t\t\tenumValues: values,\n\t\t\tschema,\n\t\t\t[isPgEnumSym]: true,\n\t\t} as const,\n\t);\n\n\treturn enumInstance;\n}\n\n/** @internal */\nexport function pgEnumObjectWithSchema<T extends object>(\n\tenumName: string,\n\tvalues: T,\n\tschema?: string,\n): PgEnumObject<T> {\n\tconst enumInstance: PgEnumObject<T> = Object.assign(\n\t\t<TName extends string>(name?: TName): PgEnumObjectColumnBuilderInitial<TName, T> =>\n\t\t\tnew PgEnumObjectColumnBuilder(name ?? '' as TName, enumInstance),\n\t\t{\n\t\t\tenumName,\n\t\t\tenumValues: Object.values(values),\n\t\t\tschema,\n\t\t\t[isPgEnumSym]: true,\n\t\t} as const,\n\t);\n\n\treturn enumInstance;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAG3B,oBAA0C;AAyBnC,MAAM,kCAEH,8BAAgD;AAAA,EACzD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,cAAiC;AAC7D,UAAM,MAAM,UAAU,oBAAoB;AAC1C,SAAK,OAAO,OAAO;AAAA,EACpB;AAAA;AAAA,EAGS,MACR,OACsD;AACtD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,2BACJ,uBACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC;AAAA,EACS,aAAa,KAAK,OAAO,KAAK;AAAA,EAEhD,YACC,OACA,QACC;AACD,UAAM,OAAO,MAAM;AACnB,SAAK,OAAO,OAAO;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO,KAAK,KAAK;AAAA,EAClB;AACD;AAcA,MAAM,cAAc,OAAO,IAAI,kBAAkB;AAa1C,SAAS,SAAS,KAAoD;AAC5E,SAAO,CAAC,CAAC,OAAO,OAAO,QAAQ,cAAc,eAAe,OAAO,IAAI,WAAW,MAAM;AACzF;AAEO,MAAM,4BAEH,8BAAsD;AAAA,EAC/D,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,cAAuC;AACnE,UAAM,MAAM,UAAU,cAAc;AACpC,SAAK,OAAO,OAAO;AAAA,EACpB;AAAA;AAAA,EAGS,MACR,OACgD;AAChD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,qBACJ,uBACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,OAAO,KAAK,OAAO;AAAA,EACV,aAAa,KAAK,OAAO,KAAK;AAAA,EAEhD,YACC,OACA,QACC;AACD,UAAM,OAAO,MAAM;AACnB,SAAK,OAAO,OAAO;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO,KAAK,KAAK;AAAA,EAClB;AACD;AAYO,SAAS,OACf,UACA,OACM;AACN,SAAO,MAAM,QAAQ,KAAK,IACvB,iBAAiB,UAAU,CAAC,GAAG,KAAK,GAA4B,MAAS,IACzE,uBAAuB,UAAU,OAAO,MAAS;AACrD;AAGO,SAAS,iBACf,UACA,QACA,QACsB;AACtB,QAAM,eAAoC,OAAO;AAAA,IAChD,CAAuB,SACtB,IAAI,oBAAoB,QAAQ,IAAa,YAAY;AAAA,IAC1D;AAAA,MACC;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,MACA,CAAC,WAAW,GAAG;AAAA,IAChB;AAAA,EACD;AAEA,SAAO;AACR;AAGO,SAAS,uBACf,UACA,QACA,QACkB;AAClB,QAAM,eAAgC,OAAO;AAAA,IAC5C,CAAuB,SACtB,IAAI,0BAA0B,QAAQ,IAAa,YAAY;AAAA,IAChE;AAAA,MACC;AAAA,MACA,YAAY,OAAO,OAAO,MAAM;AAAA,MAChC;AAAA,MACA,CAAC,WAAW,GAAG;AAAA,IAChB;AAAA,EACD;AAEA,SAAO;AACR;", "names": []}