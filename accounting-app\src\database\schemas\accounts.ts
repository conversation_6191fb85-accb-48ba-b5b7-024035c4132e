/**
 * Accounts Schema Definition
 * 
 * This file defines the accounts table schema using Drizzle ORM.
 * It handles the chart of accounts for each company/financial year.
 * 
 * Following DRY principle: Single schema per entity
 * Following YAGNI principle: Only essential account fields
 */

import { sqliteTable, text, integer, index } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';
import { accountTypes } from './accountTypes';
import { companies } from './companies';
import { financialYears } from './financialYears';

/**
 * Accounts table - Chart of accounts for each company/financial year
 * These are the actual accounts used for recording transactions
 * 
 * Following proper ORM patterns: Self-referencing foreign keys for hierarchy
 */
export const accounts = sqliteTable('accounts', {
  /** Unique identifier for the account */
  id: text('id').primaryKey(),
  
  /** Account code for easy reference (e.g., "1001", "2001") */
  code: text('code').notNull(),
  
  /** Account name (e.g., "Cash", "Accounts Receivable") */
  name: text('name').notNull(),
  
  /** Reference to the account type this account belongs to */
  accountTypeId: text('account_type_id').notNull().references(() => accountTypes.id),
  
  /** Optional parent account ID for hierarchical account structure */
  parentAccountId: text('parent_account_id').references(() => accounts.id),
  
  /** Whether this account is currently active and can be used */
  isActive: integer('is_active', { mode: 'boolean' }).default(true).notNull(),
  
  /** Reference to the company this account belongs to */
  companyId: text('company_id').notNull().references(() => companies.id, { onDelete: 'cascade' }),
  
  /** Reference to the financial year this account is valid for */
  financialYearId: text('financial_year_id').notNull().references(() => financialYears.id, { onDelete: 'cascade' }),
  
  /** Timestamp when the account was created */
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  
  /** Timestamp when the account was last updated */
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  /** Index on company_id for fast lookups of company's accounts */
  companyIdIdx: index('accounts_company_id_idx').on(table.companyId),
  
  /** Index on financial_year_id for fast lookups of year's accounts */
  financialYearIdIdx: index('accounts_financial_year_id_idx').on(table.financialYearId),
  
  /** Index on code for fast lookups by account code */
  codeIdx: index('accounts_code_idx').on(table.code),
  
  /** Index on account_type_id for filtering by account type */
  accountTypeIdIdx: index('accounts_account_type_id_idx').on(table.accountTypeId),
  
  /** Index on parent_account_id for hierarchical queries */
  parentAccountIdIdx: index('accounts_parent_account_id_idx').on(table.parentAccountId),
  
  /** Index on is_active for filtering active accounts */
  isActiveIdx: index('accounts_is_active_idx').on(table.isActive),
  
  /** Composite index for unique account codes within company/year */
  uniqueCodeIdx: index('accounts_unique_code_idx').on(table.companyId, table.financialYearId, table.code),
  
  /** Composite index for company and account type queries */
  companyTypeIdx: index('accounts_company_type_idx').on(table.companyId, table.accountTypeId),
  
  /** Composite index for active accounts within company/year */
  companyYearActiveIdx: index('accounts_company_year_active_idx').on(table.companyId, table.financialYearId, table.isActive),
}));

/**
 * Type definitions for accounts table operations
 * These provide type safety for insert, select, and update operations
 */

/** Type for inserting a new account */
export type InsertAccount = typeof accounts.$inferInsert;

/** Type for selecting account data */
export type SelectAccount = typeof accounts.$inferSelect;

/** Type for updating account data (excludes id and timestamps) */
export type UpdateAccount = Partial<Pick<SelectAccount, 'code' | 'name' | 'accountTypeId' | 'parentAccountId' | 'isActive'>>;

/**
 * Account validation constraints
 * Following DRY principle: Centralized validation rules
 */
export const AccountConstraints = {
  /** Minimum length for account codes */
  CODE_MIN_LENGTH: 1,
  /** Maximum length for account codes */
  CODE_MAX_LENGTH: 20,
  /** Minimum length for account names */
  NAME_MIN_LENGTH: 1,
  /** Maximum length for account names */
  NAME_MAX_LENGTH: 100,
  /** Maximum depth for account hierarchy */
  MAX_HIERARCHY_DEPTH: 5,
} as const;

/**
 * Account utility types
 * Following proper ORM patterns: Type-safe relationships and utilities
 */

/** Account with account type information */
export type AccountWithType = SelectAccount & {
  accountType: {
    id: string;
    name: string;
    category: string;
    description: string | null;
  };
};

/** Account with parent account information */
export type AccountWithParent = SelectAccount & {
  parentAccount: {
    id: string;
    code: string;
    name: string;
  } | null;
};

/** Account with child accounts */
export type AccountWithChildren = SelectAccount & {
  childAccounts: SelectAccount[];
};

/** Complete account with all relationships */
export type AccountWithRelations = SelectAccount & {
  accountType: {
    id: string;
    name: string;
    category: string;
    description: string | null;
  };
  parentAccount: {
    id: string;
    code: string;
    name: string;
  } | null;
  childAccounts: SelectAccount[];
  company: {
    id: string;
    name: string;
  };
  financialYear: {
    id: string;
    name: string;
  };
};

/** Account balance information */
export type AccountBalance = {
  accountId: string;
  debitBalance: number;
  creditBalance: number;
  netBalance: number;
};

/** Account statistics */
export type AccountStats = {
  totalAccounts: number;
  activeAccounts: number;
  accountsByType: Record<string, number>;
  accountsWithChildren: number;
  orphanAccounts: number;
};

/**
 * Account helper functions types
 * Following DRY principle: Reusable utility function types
 */

/** Type for account creation input */
export type CreateAccountInput = {
  code: string;
  name: string;
  accountTypeId: string;
  parentAccountId?: string;
  isActive?: boolean;
  companyId: string;
  financialYearId: string;
};

/** Type for account update input */
export type UpdateAccountInput = {
  code?: string;
  name?: string;
  accountTypeId?: string;
  parentAccountId?: string;
  isActive?: boolean;
};

/** Type for account filter options */
export type AccountFilters = {
  companyId?: string;
  financialYearId?: string;
  accountTypeId?: string;
  parentAccountId?: string;
  isActive?: boolean;
  search?: string;
  codes?: string[];
};

/** Type for account hierarchy node */
export type AccountHierarchyNode = SelectAccount & {
  children: AccountHierarchyNode[];
  level: number;
  hasChildren: boolean;
};

/** Type for chart of accounts structure */
export type ChartOfAccounts = {
  assets: AccountHierarchyNode[];
  liabilities: AccountHierarchyNode[];
  equity: AccountHierarchyNode[];
  revenue: AccountHierarchyNode[];
  expenses: AccountHierarchyNode[];
};
