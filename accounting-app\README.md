# Accounting App

A modern, full-featured accounting application built with Next.js, React, MUI Pro, and SQLite. Features a hierarchical structure with users, companies, and financial years, designed with an AdminLTE-style interface.

## Architecture Principles

This application follows **DRY (Don't Repeat Yourself)** and **YAGNI (You Aren't Gonna Need It)** principles with proper **backend/frontend separation**:

- **Modular Design**: Each component, service, and utility has a single responsibility
- **DRY Implementation**: Common functionality is centralized and reused
- **YAGNI Approach**: Only essential features are implemented
- **Clear Separation**: Backend API routes are separate from frontend components
- **Type Safety**: Comprehensive TypeScript interfaces and types

## Features

- **User Authentication**: Secure signup/login with <PERSON>pabase Auth
- **Multi-Company Support**: Users can manage multiple companies
- **Financial Year Management**: Each company can have multiple financial years
- **AdminLTE-Style Interface**: Professional dashboard with MUI Pro components
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Real-time Updates**: Live data synchronization with Supabase
- **Role-Based Access**: Secure data access with Row Level Security

## Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **UI Library**: Material-UI (MUI) Pro
- **Backend**: Next.js API Routes, SQLite Database
- **Authentication**: JWT with HTTP-only cookies
- **Styling**: MUI Theme System with AdminLTE design
- **State Management**: React Context API
- **Database ORM**: Better-SQLite3
- **Architecture**: Modular backend/frontend separation

## Prerequisites

- Node.js 18+ and npm
- MUI Pro license (optional - will work with free version)

## Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd accounting-app
npm run setup
```

This will:
- Install all dependencies
- Initialize the SQLite database
- Create the admin user (<EMAIL> / 123456)

### 2. Start Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) and login with:
- **Email**: <EMAIL>
- **Password**: 123456

### 3. Create Additional Admin Users (Optional)

```bash
# Create <EMAIL> with password 123
npm run create-admin

# Or with custom credentials
npm run create-admin <EMAIL> 123 "Admin User"

# Or run directly
node scripts/create-admin.js <EMAIL> mypassword "John Doe"
```

## Manual Setup (Alternative)

### 1. Install Dependencies

```bash
npm install
```

### 2. Initialize Database

```bash
npm run init-db
```

### 3. Start Development

```bash
npm run dev
```

## Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run start           # Start production server

# Database
npm run init-db         # Initialize database schema
npm run create-admin    # Create <EMAIL> user

# Setup
npm run setup           # Full setup (install + init-db)
npm run clean           # Clean build files and database

# Code Quality
npm run lint            # Run ESLint
npm run type-check      # Run TypeScript compiler check
```

## Project Structure

```
accounting-app/
├── src/
│   ├── app/                           # Next.js App Router
│   │   ├── api/                      # Backend API Routes
│   │   │   ├── auth/                 # Authentication endpoints
│   │   │   └── companies/            # Company endpoints
│   │   ├── auth/                     # Frontend auth pages
│   │   ├── dashboard/                # Dashboard page
│   │   ├── companies/                # Company management pages
│   │   └── layout.tsx                # Root layout with providers
│   ├── components/                   # Reusable UI components
│   │   └── layout/                   # Layout components (AdminLTE style)
│   ├── contexts/                     # React Context providers
│   │   ├── AuthContext.tsx           # Authentication state
│   │   └── AppContext.tsx            # Application state
│   ├── services/                     # Service layer (DRY principle)
│   │   ├── frontend/                 # Frontend services
│   │   │   ├── authService.ts        # Frontend auth operations
│   │   │   └── companyService.ts     # Frontend company operations
│   │   ├── userService.ts            # Backend user operations
│   │   ├── companyService.ts         # Backend company operations
│   │   └── api.ts                    # API client configuration
│   ├── lib/                          # Core utilities
│   │   ├── database.ts               # SQLite database setup
│   │   └── auth.ts                   # Authentication utilities
│   ├── middleware/                   # API middleware
│   │   └── auth.ts                   # Authentication middleware
│   ├── hooks/                        # Custom React hooks
│   │   ├── useLocalStorage.ts        # Local storage hook
│   │   ├── useDebounce.ts           # Debounce hook
│   │   └── useApi.ts                # API request hook
│   ├── utils/                        # Utility functions
│   │   └── index.ts                  # Common utilities
│   ├── types/                        # TypeScript definitions
│   │   └── index.ts                  # All type definitions
│   └── constants/                    # Application constants
│       └── index.ts                  # Configuration constants
├── scripts/                          # Build and setup scripts
│   └── init-database.js             # Database initialization
├── data/                            # SQLite database files
└── public/                          # Static assets
```
