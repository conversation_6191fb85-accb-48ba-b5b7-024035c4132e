# Accounting App

A modern, full-featured accounting application built with Next.js, React, MUI Pro, and Supabase. Features a hierarchical structure with users, companies, and financial years, designed with an AdminLTE-style interface.

## Features

- **User Authentication**: Secure signup/login with Supabase Auth
- **Multi-Company Support**: Users can manage multiple companies
- **Financial Year Management**: Each company can have multiple financial years
- **AdminLTE-Style Interface**: Professional dashboard with MUI Pro components
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Real-time Updates**: Live data synchronization with Supabase
- **Role-Based Access**: Secure data access with Row Level Security

## Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **UI Library**: Material-UI (MUI) Pro
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **Styling**: MUI Theme System
- **State Management**: React Context API

## Prerequisites

- Node.js 18+ and npm
- Supabase account
- MUI Pro license

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd accounting-app
npm install
```

### 2. Set up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and anon key
3. Go to SQL Editor and run the schema from `database/schema.sql`

### 3. Configure Environment Variables

Copy `.env.local` and update with your Supabase credentials:

```bash
cp .env.local .env.local.example
```

Update `.env.local`:
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret
```

### 4. Run the Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
accounting-app/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── auth/              # Authentication pages
│   │   ├── dashboard/         # Main dashboard
│   │   ├── companies/         # Company management
│   │   └── layout.tsx         # Root layout
│   ├── components/            # Reusable components
│   │   └── layout/           # Layout components
│   ├── contexts/             # React contexts
│   │   ├── AuthContext.tsx   # Authentication state
│   │   └── AppContext.tsx    # Application state
│   └── lib/                  # Utility libraries
│       └── supabase.ts       # Supabase client
├── database/
│   └── schema.sql            # Database schema
└── public/                   # Static assets
```
