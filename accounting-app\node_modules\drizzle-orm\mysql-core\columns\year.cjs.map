{"version": 3, "sources": ["../../../src/mysql-core/columns/year.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlYearBuilderInitial<TName extends string> = MySqlYearBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'MySqlYear';\n\tdata: number;\n\tdriverParam: number;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlYearBuilder<T extends ColumnBuilderBaseConfig<'number', 'MySqlYear'>> extends MySqlColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'MySqlYearBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'MySqlYear');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlYear<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlYear<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class MySqlYear<\n\tT extends ColumnBaseConfig<'number', 'MySqlYear'>,\n> extends MySqlColumn<T> {\n\tstatic override readonly [entityKind]: string = 'MySqlYear';\n\n\tgetSQLType(): string {\n\t\treturn `year`;\n\t}\n}\n\nexport function year(): MySqlYearBuilderInitial<''>;\nexport function year<TName extends string>(name: TName): MySqlYearBuilderInitial<TName>;\nexport function year(name?: string) {\n\treturn new MySqlYearBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAAgD;AAWzC,MAAM,yBAAmF,iCAAsB;AAAA,EACrH,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,WAAW;AAAA,EAClC;AAAA;AAAA,EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;AAAA,EACjH;AACD;AAEO,MAAM,kBAEH,0BAAe;AAAA,EACxB,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,KAAK,MAAe;AACnC,SAAO,IAAI,iBAAiB,QAAQ,EAAE;AACvC;", "names": []}