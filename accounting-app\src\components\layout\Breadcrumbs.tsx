'use client';

import React from 'react';
import {
  Breadcrumbs as MuiBreadcrum<PERSON>,
  Link,
  Typography,
  Box,
} from '@mui/material';
import {
  Home as HomeIcon,
  NavigateNext as NavigateNextIcon,
} from '@mui/icons-material';
import { usePathname, useRouter } from 'next/navigation';

export function Breadcrumbs() {
  const pathname = usePathname();
  const router = useRouter();

  // Generate breadcrumb items from pathname
  const pathSegments = pathname.split('/').filter(Boolean);
  
  const breadcrumbItems = [
    {
      label: 'Home',
      path: '/dashboard',
      icon: <HomeIcon sx={{ mr: 0.5, fontSize: 16 }} />,
    },
  ];

  // Map path segments to readable labels
  const segmentLabels: { [key: string]: string } = {
    dashboard: 'Dashboard',
    companies: 'Companies',
    accounting: 'Accounting',
    accounts: 'Chart of Accounts',
    journal: 'Journal Entries',
    ledger: 'Ledger',
    reports: 'Reports',
    'trial-balance': 'Trial Balance',
    'profit-loss': 'Profit & Loss',
    'balance-sheet': 'Balance Sheet',
    settings: 'Settings',
    profile: 'Profile',
    auth: 'Authentication',
    login: 'Login',
    register: 'Register',
  };

  // Build breadcrumb path
  let currentPath = '';
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    const label = segmentLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
    
    breadcrumbItems.push({
      label,
      path: currentPath,
      icon: null,
    });
  });

  // Don't show breadcrumbs on auth pages
  if (pathname.startsWith('/auth')) {
    return null;
  }

  return (
    <Box sx={{ mb: 2 }}>
      <MuiBreadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
        sx={{
          '& .MuiBreadcrumbs-separator': {
            color: '#666',
          },
        }}
      >
        {breadcrumbItems.map((item, index) => {
          const isLast = index === breadcrumbItems.length - 1;
          
          if (isLast) {
            return (
              <Typography
                key={item.path}
                color="text.primary"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  fontWeight: 'bold',
                }}
              >
                {item.icon}
                {item.label}
              </Typography>
            );
          }

          return (
            <Link
              key={item.path}
              underline="hover"
              color="inherit"
              onClick={() => router.push(item.path)}
              sx={{
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
                '&:hover': {
                  color: '#3498db',
                },
              }}
            >
              {item.icon}
              {item.label}
            </Link>
          );
        })}
      </MuiBreadcrumbs>
    </Box>
  );
}
