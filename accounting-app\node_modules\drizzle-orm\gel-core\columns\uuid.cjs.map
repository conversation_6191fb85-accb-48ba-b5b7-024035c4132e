{"version": 3, "sources": ["../../../src/gel-core/columns/uuid.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn, GelColumnBuilder } from './common.ts';\n\nexport type GelUUIDBuilderInitial<TName extends string> = GelUUIDBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'GelUUID';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class GelUUIDBuilder<T extends ColumnBuilderBaseConfig<'string', 'GelUUID'>> extends GelColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'GelUUIDBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'GelUUID');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelUUID<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelUUID<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class GelUUID<T extends ColumnBaseConfig<'string', 'GelUUID'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelUUID';\n\n\tgetSQLType(): string {\n\t\treturn 'uuid';\n\t}\n}\n\nexport function uuid(): GelUUIDBuilderInitial<''>;\nexport function uuid<TName extends string>(name: TName): GelUUIDBuilderInitial<TName>;\nexport function uuid(name?: string) {\n\treturn new GelUUIDBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA4C;AAWrC,MAAM,uBAA+E,+BAAoB;AAAA,EAC/G,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,SAAS;AAAA,EAChC;AAAA;AAAA,EAGS,MACR,OAC2C;AAC3C,WAAO,IAAI,QAAyC,OAAO,KAAK,MAA8C;AAAA,EAC/G;AACD;AAEO,MAAM,gBAAiE,wBAAa;AAAA,EAC1F,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,KAAK,MAAe;AACnC,SAAO,IAAI,eAAe,QAAQ,EAAE;AACrC;", "names": []}