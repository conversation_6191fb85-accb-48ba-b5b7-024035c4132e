{"version": 3, "sources": ["../../src/sql/sql.ts"], "sourcesContent": ["import type { CasingCache } from '~/casing.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport { isPgEnum } from '~/pg-core/columns/enum.ts';\nimport type { SelectResult } from '~/query-builders/select.types.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { tracer } from '~/tracing.ts';\nimport type { Assume, Equal } from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type { AnyColumn } from '../column.ts';\nimport { Column } from '../column.ts';\nimport { IsAlias, Table } from '../table.ts';\n\n/**\n * This class is used to indicate a primitive param value that is used in `sql` tag.\n * It is only used on type level and is never instantiated at runtime.\n * If you see a value of this type in the code, its runtime value is actually the primitive param value.\n */\nexport class FakePrimitiveParam {\n\tstatic readonly [entityKind]: string = 'FakePrimitiveParam';\n}\n\nexport type Chunk =\n\t| string\n\t| Table\n\t| View\n\t| AnyColumn\n\t| Name\n\t| Param\n\t| Placeholder\n\t| SQL;\n\nexport interface BuildQueryConfig {\n\tcasing: CasingCache;\n\tescapeName(name: string): string;\n\tescapeParam(num: number, value: unknown): string;\n\tescapeString(str: string): string;\n\tprepareTyping?: (encoder: DriverValueEncoder<unknown, unknown>) => QueryTypingsValue;\n\tparamStartIndex?: { value: number };\n\tinlineParams?: boolean;\n\tinvokeSource?: 'indexes' | undefined;\n}\n\nexport type QueryTypingsValue = 'json' | 'decimal' | 'time' | 'timestamp' | 'uuid' | 'date' | 'none';\n\nexport interface Query {\n\tsql: string;\n\tparams: unknown[];\n}\n\nexport interface QueryWithTypings extends Query {\n\ttypings?: QueryTypingsValue[];\n}\n\n/**\n * Any value that implements the `getSQL` method. The implementations include:\n * - `Table`\n * - `Column`\n * - `View`\n * - `Subquery`\n * - `SQL`\n * - `SQL.Aliased`\n * - `Placeholder`\n * - `Param`\n */\nexport interface SQLWrapper {\n\tgetSQL(): SQL;\n\tshouldOmitSQLParens?(): boolean;\n}\n\nexport function isSQLWrapper(value: unknown): value is SQLWrapper {\n\treturn value !== null && value !== undefined && typeof (value as any).getSQL === 'function';\n}\n\nfunction mergeQueries(queries: QueryWithTypings[]): QueryWithTypings {\n\tconst result: QueryWithTypings = { sql: '', params: [] };\n\tfor (const query of queries) {\n\t\tresult.sql += query.sql;\n\t\tresult.params.push(...query.params);\n\t\tif (query.typings?.length) {\n\t\t\tif (!result.typings) {\n\t\t\t\tresult.typings = [];\n\t\t\t}\n\t\t\tresult.typings.push(...query.typings);\n\t\t}\n\t}\n\treturn result;\n}\n\nexport class StringChunk implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'StringChunk';\n\n\treadonly value: string[];\n\n\tconstructor(value: string | string[]) {\n\t\tthis.value = Array.isArray(value) ? value : [value];\n\t}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\nexport class SQL<T = unknown> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'SQL';\n\n\tdeclare _: {\n\t\tbrand: 'SQL';\n\t\ttype: T;\n\t};\n\n\t/** @internal */\n\tdecoder: DriverValueDecoder<T, any> = noopDecoder;\n\tprivate shouldInlineParams = false;\n\n\tconstructor(readonly queryChunks: SQLChunk[]) {}\n\n\tappend(query: SQL): this {\n\t\tthis.queryChunks.push(...query.queryChunks);\n\t\treturn this;\n\t}\n\n\ttoQuery(config: BuildQueryConfig): QueryWithTypings {\n\t\treturn tracer.startActiveSpan('drizzle.buildSQL', (span) => {\n\t\t\tconst query = this.buildQueryFromSourceParams(this.queryChunks, config);\n\t\t\tspan?.setAttributes({\n\t\t\t\t'drizzle.query.text': query.sql,\n\t\t\t\t'drizzle.query.params': JSON.stringify(query.params),\n\t\t\t});\n\t\t\treturn query;\n\t\t});\n\t}\n\n\tbuildQueryFromSourceParams(chunks: SQLChunk[], _config: BuildQueryConfig): Query {\n\t\tconst config = Object.assign({}, _config, {\n\t\t\tinlineParams: _config.inlineParams || this.shouldInlineParams,\n\t\t\tparamStartIndex: _config.paramStartIndex || { value: 0 },\n\t\t});\n\n\t\tconst {\n\t\t\tcasing,\n\t\t\tescapeName,\n\t\t\tescapeParam,\n\t\t\tprepareTyping,\n\t\t\tinlineParams,\n\t\t\tparamStartIndex,\n\t\t} = config;\n\n\t\treturn mergeQueries(chunks.map((chunk): QueryWithTypings => {\n\t\t\tif (is(chunk, StringChunk)) {\n\t\t\t\treturn { sql: chunk.value.join(''), params: [] };\n\t\t\t}\n\n\t\t\tif (is(chunk, Name)) {\n\t\t\t\treturn { sql: escapeName(chunk.value), params: [] };\n\t\t\t}\n\n\t\t\tif (chunk === undefined) {\n\t\t\t\treturn { sql: '', params: [] };\n\t\t\t}\n\n\t\t\tif (Array.isArray(chunk)) {\n\t\t\t\tconst result: SQLChunk[] = [new StringChunk('(')];\n\t\t\t\tfor (const [i, p] of chunk.entries()) {\n\t\t\t\t\tresult.push(p);\n\t\t\t\t\tif (i < chunk.length - 1) {\n\t\t\t\t\t\tresult.push(new StringChunk(', '));\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tresult.push(new StringChunk(')'));\n\t\t\t\treturn this.buildQueryFromSourceParams(result, config);\n\t\t\t}\n\n\t\t\tif (is(chunk, SQL)) {\n\t\t\t\treturn this.buildQueryFromSourceParams(chunk.queryChunks, {\n\t\t\t\t\t...config,\n\t\t\t\t\tinlineParams: inlineParams || chunk.shouldInlineParams,\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tif (is(chunk, Table)) {\n\t\t\t\tconst schemaName = chunk[Table.Symbol.Schema];\n\t\t\t\tconst tableName = chunk[Table.Symbol.Name];\n\t\t\t\treturn {\n\t\t\t\t\tsql: schemaName === undefined || chunk[IsAlias]\n\t\t\t\t\t\t? escapeName(tableName)\n\t\t\t\t\t\t: escapeName(schemaName) + '.' + escapeName(tableName),\n\t\t\t\t\tparams: [],\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (is(chunk, Column)) {\n\t\t\t\tconst columnName = casing.getColumnCasing(chunk);\n\t\t\t\tif (_config.invokeSource === 'indexes') {\n\t\t\t\t\treturn { sql: escapeName(columnName), params: [] };\n\t\t\t\t}\n\n\t\t\t\tconst schemaName = chunk.table[Table.Symbol.Schema];\n\t\t\t\treturn {\n\t\t\t\t\tsql: chunk.table[IsAlias] || schemaName === undefined\n\t\t\t\t\t\t? escapeName(chunk.table[Table.Symbol.Name]) + '.' + escapeName(columnName)\n\t\t\t\t\t\t: escapeName(schemaName) + '.' + escapeName(chunk.table[Table.Symbol.Name]) + '.'\n\t\t\t\t\t\t\t+ escapeName(columnName),\n\t\t\t\t\tparams: [],\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (is(chunk, View)) {\n\t\t\t\tconst schemaName = chunk[ViewBaseConfig].schema;\n\t\t\t\tconst viewName = chunk[ViewBaseConfig].name;\n\t\t\t\treturn {\n\t\t\t\t\tsql: schemaName === undefined || chunk[ViewBaseConfig].isAlias\n\t\t\t\t\t\t? escapeName(viewName)\n\t\t\t\t\t\t: escapeName(schemaName) + '.' + escapeName(viewName),\n\t\t\t\t\tparams: [],\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (is(chunk, Param)) {\n\t\t\t\tif (is(chunk.value, Placeholder)) {\n\t\t\t\t\treturn { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: ['none'] };\n\t\t\t\t}\n\n\t\t\t\tconst mappedValue = chunk.value === null ? null : chunk.encoder.mapToDriverValue(chunk.value);\n\n\t\t\t\tif (is(mappedValue, SQL)) {\n\t\t\t\t\treturn this.buildQueryFromSourceParams([mappedValue], config);\n\t\t\t\t}\n\n\t\t\t\tif (inlineParams) {\n\t\t\t\t\treturn { sql: this.mapInlineParam(mappedValue, config), params: [] };\n\t\t\t\t}\n\n\t\t\t\tlet typings: QueryTypingsValue[] = ['none'];\n\t\t\t\tif (prepareTyping) {\n\t\t\t\t\ttypings = [prepareTyping(chunk.encoder)];\n\t\t\t\t}\n\n\t\t\t\treturn { sql: escapeParam(paramStartIndex.value++, mappedValue), params: [mappedValue], typings };\n\t\t\t}\n\n\t\t\tif (is(chunk, Placeholder)) {\n\t\t\t\treturn { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: ['none'] };\n\t\t\t}\n\n\t\t\tif (is(chunk, SQL.Aliased) && chunk.fieldAlias !== undefined) {\n\t\t\t\treturn { sql: escapeName(chunk.fieldAlias), params: [] };\n\t\t\t}\n\n\t\t\tif (is(chunk, Subquery)) {\n\t\t\t\tif (chunk._.isWith) {\n\t\t\t\t\treturn { sql: escapeName(chunk._.alias), params: [] };\n\t\t\t\t}\n\t\t\t\treturn this.buildQueryFromSourceParams([\n\t\t\t\t\tnew StringChunk('('),\n\t\t\t\t\tchunk._.sql,\n\t\t\t\t\tnew StringChunk(') '),\n\t\t\t\t\tnew Name(chunk._.alias),\n\t\t\t\t], config);\n\t\t\t}\n\n\t\t\tif (isPgEnum(chunk)) {\n\t\t\t\tif (chunk.schema) {\n\t\t\t\t\treturn { sql: escapeName(chunk.schema) + '.' + escapeName(chunk.enumName), params: [] };\n\t\t\t\t}\n\t\t\t\treturn { sql: escapeName(chunk.enumName), params: [] };\n\t\t\t}\n\n\t\t\tif (isSQLWrapper(chunk)) {\n\t\t\t\tif (chunk.shouldOmitSQLParens?.()) {\n\t\t\t\t\treturn this.buildQueryFromSourceParams([chunk.getSQL()], config);\n\t\t\t\t}\n\t\t\t\treturn this.buildQueryFromSourceParams([\n\t\t\t\t\tnew StringChunk('('),\n\t\t\t\t\tchunk.getSQL(),\n\t\t\t\t\tnew StringChunk(')'),\n\t\t\t\t], config);\n\t\t\t}\n\n\t\t\tif (inlineParams) {\n\t\t\t\treturn { sql: this.mapInlineParam(chunk, config), params: [] };\n\t\t\t}\n\n\t\t\treturn { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: ['none'] };\n\t\t}));\n\t}\n\n\tprivate mapInlineParam(\n\t\tchunk: unknown,\n\t\t{ escapeString }: BuildQueryConfig,\n\t): string {\n\t\tif (chunk === null) {\n\t\t\treturn 'null';\n\t\t}\n\t\tif (typeof chunk === 'number' || typeof chunk === 'boolean') {\n\t\t\treturn chunk.toString();\n\t\t}\n\t\tif (typeof chunk === 'string') {\n\t\t\treturn escapeString(chunk);\n\t\t}\n\t\tif (typeof chunk === 'object') {\n\t\t\tconst mappedValueAsString = chunk.toString();\n\t\t\tif (mappedValueAsString === '[object Object]') {\n\t\t\t\treturn escapeString(JSON.stringify(chunk));\n\t\t\t}\n\t\t\treturn escapeString(mappedValueAsString);\n\t\t}\n\t\tthrow new Error('Unexpected param value: ' + chunk);\n\t}\n\n\tgetSQL(): SQL {\n\t\treturn this;\n\t}\n\n\tas(alias: string): SQL.Aliased<T>;\n\t/**\n\t * @deprecated\n\t * Use ``sql<DataType>`query`.as(alias)`` instead.\n\t */\n\tas<TData>(): SQL<TData>;\n\t/**\n\t * @deprecated\n\t * Use ``sql<DataType>`query`.as(alias)`` instead.\n\t */\n\tas<TData>(alias: string): SQL.Aliased<TData>;\n\tas(alias?: string): SQL<T> | SQL.Aliased<T> {\n\t\t// TODO: remove with deprecated overloads\n\t\tif (alias === undefined) {\n\t\t\treturn this;\n\t\t}\n\n\t\treturn new SQL.Aliased(this, alias);\n\t}\n\n\tmapWith<\n\t\tTDecoder extends\n\t\t\t| DriverValueDecoder<any, any>\n\t\t\t| DriverValueDecoder<any, any>['mapFromDriverValue'],\n\t>(decoder: TDecoder): SQL<GetDecoderResult<TDecoder>> {\n\t\tthis.decoder = typeof decoder === 'function' ? { mapFromDriverValue: decoder } : decoder;\n\t\treturn this as SQL<GetDecoderResult<TDecoder>>;\n\t}\n\n\tinlineParams(): this {\n\t\tthis.shouldInlineParams = true;\n\t\treturn this;\n\t}\n\n\t/**\n\t * This method is used to conditionally include a part of the query.\n\t *\n\t * @param condition - Condition to check\n\t * @returns itself if the condition is `true`, otherwise `undefined`\n\t */\n\tif(condition: any | undefined): this | undefined {\n\t\treturn condition ? this : undefined;\n\t}\n}\n\nexport type GetDecoderResult<T> = T extends Column ? T['_']['data'] : T extends\n\t| DriverValueDecoder<infer TData, any>\n\t| DriverValueDecoder<infer TData, any>['mapFromDriverValue'] ? TData\n: never;\n\n/**\n * Any DB name (table, column, index etc.)\n */\nexport class Name implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Name';\n\n\tprotected brand!: 'Name';\n\n\tconstructor(readonly value: string) {}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\n/**\n * Any DB name (table, column, index etc.)\n * @deprecated Use `sql.identifier` instead.\n */\nexport function name(value: string): Name {\n\treturn new Name(value);\n}\n\nexport interface DriverValueDecoder<TData, TDriverParam> {\n\tmapFromDriverValue(value: TDriverParam): TData;\n}\n\nexport interface DriverValueEncoder<TData, TDriverParam> {\n\tmapToDriverValue(value: TData): TDriverParam | SQL;\n}\n\nexport function isDriverValueEncoder(value: unknown): value is DriverValueEncoder<any, any> {\n\treturn typeof value === 'object' && value !== null && 'mapToDriverValue' in value\n\t\t&& typeof (value as any).mapToDriverValue === 'function';\n}\n\nexport const noopDecoder: DriverValueDecoder<any, any> = {\n\tmapFromDriverValue: (value) => value,\n};\n\nexport const noopEncoder: DriverValueEncoder<any, any> = {\n\tmapToDriverValue: (value) => value,\n};\n\nexport interface DriverValueMapper<TData, TDriverParam>\n\textends DriverValueDecoder<TData, TDriverParam>, DriverValueEncoder<TData, TDriverParam>\n{}\n\nexport const noopMapper: DriverValueMapper<any, any> = {\n\t...noopDecoder,\n\t...noopEncoder,\n};\n\n/** Parameter value that is optionally bound to an encoder (for example, a column). */\nexport class Param<TDataType = unknown, TDriverParamType = TDataType> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Param';\n\n\tprotected brand!: 'BoundParamValue';\n\n\t/**\n\t * @param value - Parameter value\n\t * @param encoder - Encoder to convert the value to a driver parameter\n\t */\n\tconstructor(\n\t\treadonly value: TDataType,\n\t\treadonly encoder: DriverValueEncoder<TDataType, TDriverParamType> = noopEncoder,\n\t) {}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\n/** @deprecated Use `sql.param` instead. */\nexport function param<TData, TDriver>(\n\tvalue: TData,\n\tencoder?: DriverValueEncoder<TData, TDriver>,\n): Param<TData, TDriver> {\n\treturn new Param(value, encoder);\n}\n\n/**\n * Anything that can be passed to the `` sql`...` `` tagged function.\n */\nexport type SQLChunk =\n\t| StringChunk\n\t| SQLChunk[]\n\t| SQLWrapper\n\t| SQL\n\t| Table\n\t| View\n\t| Subquery\n\t| AnyColumn\n\t| Param\n\t| Name\n\t| undefined\n\t| FakePrimitiveParam\n\t| Placeholder;\n\nexport function sql<T>(strings: TemplateStringsArray, ...params: any[]): SQL<T>;\n/*\n\tThe type of `params` is specified as `SQLChunk[]`, but that's slightly incorrect -\n\tin runtime, users won't pass `FakePrimitiveParam` instances as `params` - they will pass primitive values\n\twhich will be wrapped in `Param`. That's why the overload specifies `params` as `any[]` and not as `SQLSourceParam[]`.\n\tThis type is used to make our lives easier and the type checker happy.\n*/\nexport function sql(strings: TemplateStringsArray, ...params: SQLChunk[]): SQL {\n\tconst queryChunks: SQLChunk[] = [];\n\tif (params.length > 0 || (strings.length > 0 && strings[0] !== '')) {\n\t\tqueryChunks.push(new StringChunk(strings[0]!));\n\t}\n\tfor (const [paramIndex, param] of params.entries()) {\n\t\tqueryChunks.push(param, new StringChunk(strings[paramIndex + 1]!));\n\t}\n\n\treturn new SQL(queryChunks);\n}\n\nexport namespace sql {\n\texport function empty(): SQL {\n\t\treturn new SQL([]);\n\t}\n\n\t/** @deprecated - use `sql.join()` */\n\texport function fromList(list: SQLChunk[]): SQL {\n\t\treturn new SQL(list);\n\t}\n\n\t/**\n\t * Convenience function to create an SQL query from a raw string.\n\t * @param str The raw SQL query string.\n\t */\n\texport function raw(str: string): SQL {\n\t\treturn new SQL([new StringChunk(str)]);\n\t}\n\n\t/**\n\t * Join a list of SQL chunks with a separator.\n\t * @example\n\t * ```ts\n\t * const query = sql.join([sql`a`, sql`b`, sql`c`]);\n\t * // sql`abc`\n\t * ```\n\t * @example\n\t * ```ts\n\t * const query = sql.join([sql`a`, sql`b`, sql`c`], sql`, `);\n\t * // sql`a, b, c`\n\t * ```\n\t */\n\texport function join(chunks: SQLChunk[], separator?: SQLChunk): SQL {\n\t\tconst result: SQLChunk[] = [];\n\t\tfor (const [i, chunk] of chunks.entries()) {\n\t\t\tif (i > 0 && separator !== undefined) {\n\t\t\t\tresult.push(separator);\n\t\t\t}\n\t\t\tresult.push(chunk);\n\t\t}\n\t\treturn new SQL(result);\n\t}\n\n\t/**\n\t * Create a SQL chunk that represents a DB identifier (table, column, index etc.).\n\t * When used in a query, the identifier will be escaped based on the DB engine.\n\t * For example, in PostgreSQL, identifiers are escaped with double quotes.\n\t *\n\t * **WARNING: This function does not offer any protection against SQL injections, so you must validate any user input beforehand.**\n\t *\n\t * @example ```ts\n\t * const query = sql`SELECT * FROM ${sql.identifier('my-table')}`;\n\t * // 'SELECT * FROM \"my-table\"'\n\t * ```\n\t */\n\texport function identifier(value: string): Name {\n\t\treturn new Name(value);\n\t}\n\n\texport function placeholder<TName extends string>(name: TName): Placeholder<TName> {\n\t\treturn new Placeholder(name);\n\t}\n\n\texport function param<TData, TDriver>(\n\t\tvalue: TData,\n\t\tencoder?: DriverValueEncoder<TData, TDriver>,\n\t): Param<TData, TDriver> {\n\t\treturn new Param(value, encoder);\n\t}\n}\n\nexport namespace SQL {\n\texport class Aliased<T = unknown> implements SQLWrapper {\n\t\tstatic readonly [entityKind]: string = 'SQL.Aliased';\n\n\t\tdeclare _: {\n\t\t\tbrand: 'SQL.Aliased';\n\t\t\ttype: T;\n\t\t};\n\n\t\t/** @internal */\n\t\tisSelectionField = false;\n\n\t\tconstructor(\n\t\t\treadonly sql: SQL,\n\t\t\treadonly fieldAlias: string,\n\t\t) {}\n\n\t\tgetSQL(): SQL {\n\t\t\treturn this.sql;\n\t\t}\n\n\t\t/** @internal */\n\t\tclone() {\n\t\t\treturn new Aliased(this.sql, this.fieldAlias);\n\t\t}\n\t}\n}\n\nexport class Placeholder<TName extends string = string, TValue = any> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'Placeholder';\n\n\tdeclare protected: TValue;\n\n\tconstructor(readonly name: TName) {}\n\n\tgetSQL(): SQL {\n\t\treturn new SQL([this]);\n\t}\n}\n\n/** @deprecated Use `sql.placeholder` instead. */\nexport function placeholder<TName extends string>(name: TName): Placeholder<TName> {\n\treturn new Placeholder(name);\n}\n\nexport function fillPlaceholders(params: unknown[], values: Record<string, unknown>): unknown[] {\n\treturn params.map((p) => {\n\t\tif (is(p, Placeholder)) {\n\t\t\tif (!(p.name in values)) {\n\t\t\t\tthrow new Error(`No value for placeholder \"${p.name}\" was provided`);\n\t\t\t}\n\n\t\t\treturn values[p.name];\n\t\t}\n\n\t\tif (is(p, Param) && is(p.value, Placeholder)) {\n\t\t\tif (!(p.value.name in values)) {\n\t\t\t\tthrow new Error(`No value for placeholder \"${p.value.name}\" was provided`);\n\t\t\t}\n\n\t\t\treturn p.encoder.mapToDriverValue(values[p.value.name]);\n\t\t}\n\n\t\treturn p;\n\t});\n}\n\nexport type ColumnsSelection = Record<string, unknown>;\n\nconst IsDrizzleView = Symbol.for('drizzle:IsDrizzleView');\n\nexport abstract class View<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelection extends ColumnsSelection = ColumnsSelection,\n> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'View';\n\n\tdeclare _: {\n\t\tbrand: 'View';\n\t\tviewBrand: string;\n\t\tname: TName;\n\t\texisting: TExisting;\n\t\tselectedFields: TSelection;\n\t};\n\n\t/** @internal */\n\t[ViewBaseConfig]: {\n\t\tname: TName;\n\t\toriginalName: TName;\n\t\tschema: string | undefined;\n\t\tselectedFields: ColumnsSelection;\n\t\tisExisting: TExisting;\n\t\tquery: TExisting extends true ? undefined : SQL;\n\t\tisAlias: boolean;\n\t};\n\n\t/** @internal */\n\t[IsDrizzleView] = true;\n\n\tdeclare readonly $inferSelect: InferSelectViewModel<View<Assume<TName, string>, TExisting, TSelection>>;\n\n\tconstructor(\n\t\t{ name, schema, selectedFields, query }: {\n\t\t\tname: TName;\n\t\t\tschema: string | undefined;\n\t\t\tselectedFields: ColumnsSelection;\n\t\t\tquery: SQL | undefined;\n\t\t},\n\t) {\n\t\tthis[ViewBaseConfig] = {\n\t\t\tname,\n\t\t\toriginalName: name,\n\t\t\tschema,\n\t\t\tselectedFields,\n\t\t\tquery: query as (TExisting extends true ? undefined : SQL),\n\t\t\tisExisting: !query as TExisting,\n\t\t\tisAlias: false,\n\t\t};\n\t}\n\n\tgetSQL(): SQL<unknown> {\n\t\treturn new SQL([this]);\n\t}\n}\n\nexport function isView(view: unknown): view is View {\n\treturn typeof view === 'object' && view !== null && IsDrizzleView in view;\n}\n\nexport function getViewName<T extends View>(view: T): T['_']['name'] {\n\treturn view[ViewBaseConfig].name;\n}\n\nexport type InferSelectViewModel<TView extends View> =\n\tEqual<TView['_']['selectedFields'], { [x: string]: unknown }> extends true ? { [x: string]: unknown }\n\t\t: SelectResult<\n\t\t\tTView['_']['selectedFields'],\n\t\t\t'single',\n\t\t\tRecord<TView['_']['name'], 'not-null'>\n\t\t>;\n\n// Defined separately from the Column class to resolve circular dependency\nColumn.prototype.getSQL = function() {\n\treturn new SQL([this]);\n};\n\n// Defined separately from the Table class to resolve circular dependency\nTable.prototype.getSQL = function() {\n\treturn new SQL([this]);\n};\n\n// Defined separately from the Column class to resolve circular dependency\nSubquery.prototype.getSQL = function() {\n\treturn new SQL([this]);\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA+B;AAC/B,kBAAyB;AAEzB,sBAAyB;AACzB,qBAAuB;AAEvB,yBAA+B;AAE/B,oBAAuB;AACvB,mBAA+B;AAOxB,MAAM,mBAAmB;AAAA,EAC/B,QAAiB,wBAAU,IAAY;AACxC;AAkDO,SAAS,aAAa,OAAqC;AACjE,SAAO,UAAU,QAAQ,UAAU,UAAa,OAAQ,MAAc,WAAW;AAClF;AAEA,SAAS,aAAa,SAA+C;AACpE,QAAM,SAA2B,EAAE,KAAK,IAAI,QAAQ,CAAC,EAAE;AACvD,aAAW,SAAS,SAAS;AAC5B,WAAO,OAAO,MAAM;AACpB,WAAO,OAAO,KAAK,GAAG,MAAM,MAAM;AAClC,QAAI,MAAM,SAAS,QAAQ;AAC1B,UAAI,CAAC,OAAO,SAAS;AACpB,eAAO,UAAU,CAAC;AAAA,MACnB;AACA,aAAO,QAAQ,KAAK,GAAG,MAAM,OAAO;AAAA,IACrC;AAAA,EACD;AACA,SAAO;AACR;AAEO,MAAM,YAAkC;AAAA,EAC9C,QAAiB,wBAAU,IAAY;AAAA,EAE9B;AAAA,EAET,YAAY,OAA0B;AACrC,SAAK,QAAQ,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAAA,EACnD;AAAA,EAEA,SAAuB;AACtB,WAAO,IAAI,IAAI,CAAC,IAAI,CAAC;AAAA,EACtB;AACD;AAEO,MAAM,IAAuC;AAAA,EAYnD,YAAqB,aAAyB;AAAzB;AAAA,EAA0B;AAAA,EAX/C,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAQvC,UAAsC;AAAA,EAC9B,qBAAqB;AAAA,EAI7B,OAAO,OAAkB;AACxB,SAAK,YAAY,KAAK,GAAG,MAAM,WAAW;AAC1C,WAAO;AAAA,EACR;AAAA,EAEA,QAAQ,QAA4C;AACnD,WAAO,sBAAO,gBAAgB,oBAAoB,CAAC,SAAS;AAC3D,YAAM,QAAQ,KAAK,2BAA2B,KAAK,aAAa,MAAM;AACtE,YAAM,cAAc;AAAA,QACnB,sBAAsB,MAAM;AAAA,QAC5B,wBAAwB,KAAK,UAAU,MAAM,MAAM;AAAA,MACpD,CAAC;AACD,aAAO;AAAA,IACR,CAAC;AAAA,EACF;AAAA,EAEA,2BAA2B,QAAoB,SAAkC;AAChF,UAAM,SAAS,OAAO,OAAO,CAAC,GAAG,SAAS;AAAA,MACzC,cAAc,QAAQ,gBAAgB,KAAK;AAAA,MAC3C,iBAAiB,QAAQ,mBAAmB,EAAE,OAAO,EAAE;AAAA,IACxD,CAAC;AAED,UAAM;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD,IAAI;AAEJ,WAAO,aAAa,OAAO,IAAI,CAAC,UAA4B;AAC3D,cAAI,kBAAG,OAAO,WAAW,GAAG;AAC3B,eAAO,EAAE,KAAK,MAAM,MAAM,KAAK,EAAE,GAAG,QAAQ,CAAC,EAAE;AAAA,MAChD;AAEA,cAAI,kBAAG,OAAO,IAAI,GAAG;AACpB,eAAO,EAAE,KAAK,WAAW,MAAM,KAAK,GAAG,QAAQ,CAAC,EAAE;AAAA,MACnD;AAEA,UAAI,UAAU,QAAW;AACxB,eAAO,EAAE,KAAK,IAAI,QAAQ,CAAC,EAAE;AAAA,MAC9B;AAEA,UAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,cAAM,SAAqB,CAAC,IAAI,YAAY,GAAG,CAAC;AAChD,mBAAW,CAAC,GAAG,CAAC,KAAK,MAAM,QAAQ,GAAG;AACrC,iBAAO,KAAK,CAAC;AACb,cAAI,IAAI,MAAM,SAAS,GAAG;AACzB,mBAAO,KAAK,IAAI,YAAY,IAAI,CAAC;AAAA,UAClC;AAAA,QACD;AACA,eAAO,KAAK,IAAI,YAAY,GAAG,CAAC;AAChC,eAAO,KAAK,2BAA2B,QAAQ,MAAM;AAAA,MACtD;AAEA,cAAI,kBAAG,OAAO,GAAG,GAAG;AACnB,eAAO,KAAK,2BAA2B,MAAM,aAAa;AAAA,UACzD,GAAG;AAAA,UACH,cAAc,gBAAgB,MAAM;AAAA,QACrC,CAAC;AAAA,MACF;AAEA,cAAI,kBAAG,OAAO,kBAAK,GAAG;AACrB,cAAM,aAAa,MAAM,mBAAM,OAAO,MAAM;AAC5C,cAAM,YAAY,MAAM,mBAAM,OAAO,IAAI;AACzC,eAAO;AAAA,UACN,KAAK,eAAe,UAAa,MAAM,oBAAO,IAC3C,WAAW,SAAS,IACpB,WAAW,UAAU,IAAI,MAAM,WAAW,SAAS;AAAA,UACtD,QAAQ,CAAC;AAAA,QACV;AAAA,MACD;AAEA,cAAI,kBAAG,OAAO,oBAAM,GAAG;AACtB,cAAM,aAAa,OAAO,gBAAgB,KAAK;AAC/C,YAAI,QAAQ,iBAAiB,WAAW;AACvC,iBAAO,EAAE,KAAK,WAAW,UAAU,GAAG,QAAQ,CAAC,EAAE;AAAA,QAClD;AAEA,cAAM,aAAa,MAAM,MAAM,mBAAM,OAAO,MAAM;AAClD,eAAO;AAAA,UACN,KAAK,MAAM,MAAM,oBAAO,KAAK,eAAe,SACzC,WAAW,MAAM,MAAM,mBAAM,OAAO,IAAI,CAAC,IAAI,MAAM,WAAW,UAAU,IACxE,WAAW,UAAU,IAAI,MAAM,WAAW,MAAM,MAAM,mBAAM,OAAO,IAAI,CAAC,IAAI,MAC3E,WAAW,UAAU;AAAA,UACzB,QAAQ,CAAC;AAAA,QACV;AAAA,MACD;AAEA,cAAI,kBAAG,OAAO,IAAI,GAAG;AACpB,cAAM,aAAa,MAAM,iCAAc,EAAE;AACzC,cAAM,WAAW,MAAM,iCAAc,EAAE;AACvC,eAAO;AAAA,UACN,KAAK,eAAe,UAAa,MAAM,iCAAc,EAAE,UACpD,WAAW,QAAQ,IACnB,WAAW,UAAU,IAAI,MAAM,WAAW,QAAQ;AAAA,UACrD,QAAQ,CAAC;AAAA,QACV;AAAA,MACD;AAEA,cAAI,kBAAG,OAAO,KAAK,GAAG;AACrB,gBAAI,kBAAG,MAAM,OAAO,WAAW,GAAG;AACjC,iBAAO,EAAE,KAAK,YAAY,gBAAgB,SAAS,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE;AAAA,QAC/F;AAEA,cAAM,cAAc,MAAM,UAAU,OAAO,OAAO,MAAM,QAAQ,iBAAiB,MAAM,KAAK;AAE5F,gBAAI,kBAAG,aAAa,GAAG,GAAG;AACzB,iBAAO,KAAK,2BAA2B,CAAC,WAAW,GAAG,MAAM;AAAA,QAC7D;AAEA,YAAI,cAAc;AACjB,iBAAO,EAAE,KAAK,KAAK,eAAe,aAAa,MAAM,GAAG,QAAQ,CAAC,EAAE;AAAA,QACpE;AAEA,YAAI,UAA+B,CAAC,MAAM;AAC1C,YAAI,eAAe;AAClB,oBAAU,CAAC,cAAc,MAAM,OAAO,CAAC;AAAA,QACxC;AAEA,eAAO,EAAE,KAAK,YAAY,gBAAgB,SAAS,WAAW,GAAG,QAAQ,CAAC,WAAW,GAAG,QAAQ;AAAA,MACjG;AAEA,cAAI,kBAAG,OAAO,WAAW,GAAG;AAC3B,eAAO,EAAE,KAAK,YAAY,gBAAgB,SAAS,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE;AAAA,MAC/F;AAEA,cAAI,kBAAG,OAAO,IAAI,OAAO,KAAK,MAAM,eAAe,QAAW;AAC7D,eAAO,EAAE,KAAK,WAAW,MAAM,UAAU,GAAG,QAAQ,CAAC,EAAE;AAAA,MACxD;AAEA,cAAI,kBAAG,OAAO,wBAAQ,GAAG;AACxB,YAAI,MAAM,EAAE,QAAQ;AACnB,iBAAO,EAAE,KAAK,WAAW,MAAM,EAAE,KAAK,GAAG,QAAQ,CAAC,EAAE;AAAA,QACrD;AACA,eAAO,KAAK,2BAA2B;AAAA,UACtC,IAAI,YAAY,GAAG;AAAA,UACnB,MAAM,EAAE;AAAA,UACR,IAAI,YAAY,IAAI;AAAA,UACpB,IAAI,KAAK,MAAM,EAAE,KAAK;AAAA,QACvB,GAAG,MAAM;AAAA,MACV;AAEA,cAAI,sBAAS,KAAK,GAAG;AACpB,YAAI,MAAM,QAAQ;AACjB,iBAAO,EAAE,KAAK,WAAW,MAAM,MAAM,IAAI,MAAM,WAAW,MAAM,QAAQ,GAAG,QAAQ,CAAC,EAAE;AAAA,QACvF;AACA,eAAO,EAAE,KAAK,WAAW,MAAM,QAAQ,GAAG,QAAQ,CAAC,EAAE;AAAA,MACtD;AAEA,UAAI,aAAa,KAAK,GAAG;AACxB,YAAI,MAAM,sBAAsB,GAAG;AAClC,iBAAO,KAAK,2BAA2B,CAAC,MAAM,OAAO,CAAC,GAAG,MAAM;AAAA,QAChE;AACA,eAAO,KAAK,2BAA2B;AAAA,UACtC,IAAI,YAAY,GAAG;AAAA,UACnB,MAAM,OAAO;AAAA,UACb,IAAI,YAAY,GAAG;AAAA,QACpB,GAAG,MAAM;AAAA,MACV;AAEA,UAAI,cAAc;AACjB,eAAO,EAAE,KAAK,KAAK,eAAe,OAAO,MAAM,GAAG,QAAQ,CAAC,EAAE;AAAA,MAC9D;AAEA,aAAO,EAAE,KAAK,YAAY,gBAAgB,SAAS,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE;AAAA,IAC/F,CAAC,CAAC;AAAA,EACH;AAAA,EAEQ,eACP,OACA,EAAE,aAAa,GACN;AACT,QAAI,UAAU,MAAM;AACnB,aAAO;AAAA,IACR;AACA,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,WAAW;AAC5D,aAAO,MAAM,SAAS;AAAA,IACvB;AACA,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,aAAa,KAAK;AAAA,IAC1B;AACA,QAAI,OAAO,UAAU,UAAU;AAC9B,YAAM,sBAAsB,MAAM,SAAS;AAC3C,UAAI,wBAAwB,mBAAmB;AAC9C,eAAO,aAAa,KAAK,UAAU,KAAK,CAAC;AAAA,MAC1C;AACA,aAAO,aAAa,mBAAmB;AAAA,IACxC;AACA,UAAM,IAAI,MAAM,6BAA6B,KAAK;AAAA,EACnD;AAAA,EAEA,SAAc;AACb,WAAO;AAAA,EACR;AAAA,EAaA,GAAG,OAAyC;AAE3C,QAAI,UAAU,QAAW;AACxB,aAAO;AAAA,IACR;AAEA,WAAO,IAAI,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnC;AAAA,EAEA,QAIE,SAAoD;AACrD,SAAK,UAAU,OAAO,YAAY,aAAa,EAAE,oBAAoB,QAAQ,IAAI;AACjF,WAAO;AAAA,EACR;AAAA,EAEA,eAAqB;AACpB,SAAK,qBAAqB;AAC1B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,GAAG,WAA8C;AAChD,WAAO,YAAY,OAAO;AAAA,EAC3B;AACD;AAUO,MAAM,KAA2B;AAAA,EAKvC,YAAqB,OAAe;AAAf;AAAA,EAAgB;AAAA,EAJrC,QAAiB,wBAAU,IAAY;AAAA,EAE7B;AAAA,EAIV,SAAuB;AACtB,WAAO,IAAI,IAAI,CAAC,IAAI,CAAC;AAAA,EACtB;AACD;AAMO,SAAS,KAAK,OAAqB;AACzC,SAAO,IAAI,KAAK,KAAK;AACtB;AAUO,SAAS,qBAAqB,OAAuD;AAC3F,SAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,sBAAsB,SACxE,OAAQ,MAAc,qBAAqB;AAChD;AAEO,MAAM,cAA4C;AAAA,EACxD,oBAAoB,CAAC,UAAU;AAChC;AAEO,MAAM,cAA4C;AAAA,EACxD,kBAAkB,CAAC,UAAU;AAC9B;AAMO,MAAM,aAA0C;AAAA,EACtD,GAAG;AAAA,EACH,GAAG;AACJ;AAGO,MAAM,MAA+E;AAAA;AAAA;AAAA;AAAA;AAAA,EAS3F,YACU,OACA,UAA2D,aACnE;AAFQ;AACA;AAAA,EACP;AAAA,EAXH,QAAiB,wBAAU,IAAY;AAAA,EAE7B;AAAA,EAWV,SAAuB;AACtB,WAAO,IAAI,IAAI,CAAC,IAAI,CAAC;AAAA,EACtB;AACD;AAGO,SAAS,MACf,OACA,SACwB;AACxB,SAAO,IAAI,MAAM,OAAO,OAAO;AAChC;AA2BO,SAAS,IAAI,YAAkC,QAAyB;AAC9E,QAAM,cAA0B,CAAC;AACjC,MAAI,OAAO,SAAS,KAAM,QAAQ,SAAS,KAAK,QAAQ,CAAC,MAAM,IAAK;AACnE,gBAAY,KAAK,IAAI,YAAY,QAAQ,CAAC,CAAE,CAAC;AAAA,EAC9C;AACA,aAAW,CAAC,YAAYA,MAAK,KAAK,OAAO,QAAQ,GAAG;AACnD,gBAAY,KAAKA,QAAO,IAAI,YAAY,QAAQ,aAAa,CAAC,CAAE,CAAC;AAAA,EAClE;AAEA,SAAO,IAAI,IAAI,WAAW;AAC3B;AAAA,CAEO,CAAUC,SAAV;AACC,WAAS,QAAa;AAC5B,WAAO,IAAI,IAAI,CAAC,CAAC;AAAA,EAClB;AAFO,EAAAA,KAAS;AAKT,WAAS,SAAS,MAAuB;AAC/C,WAAO,IAAI,IAAI,IAAI;AAAA,EACpB;AAFO,EAAAA,KAAS;AAQT,WAAS,IAAI,KAAkB;AACrC,WAAO,IAAI,IAAI,CAAC,IAAI,YAAY,GAAG,CAAC,CAAC;AAAA,EACtC;AAFO,EAAAA,KAAS;AAiBT,WAAS,KAAK,QAAoB,WAA2B;AACnE,UAAM,SAAqB,CAAC;AAC5B,eAAW,CAAC,GAAG,KAAK,KAAK,OAAO,QAAQ,GAAG;AAC1C,UAAI,IAAI,KAAK,cAAc,QAAW;AACrC,eAAO,KAAK,SAAS;AAAA,MACtB;AACA,aAAO,KAAK,KAAK;AAAA,IAClB;AACA,WAAO,IAAI,IAAI,MAAM;AAAA,EACtB;AATO,EAAAA,KAAS;AAuBT,WAAS,WAAW,OAAqB;AAC/C,WAAO,IAAI,KAAK,KAAK;AAAA,EACtB;AAFO,EAAAA,KAAS;AAIT,WAASC,aAAkCC,OAAiC;AAClF,WAAO,IAAI,YAAYA,KAAI;AAAA,EAC5B;AAFO,EAAAF,KAAS,cAAAC;AAIT,WAASF,OACf,OACA,SACwB;AACxB,WAAO,IAAI,MAAM,OAAO,OAAO;AAAA,EAChC;AALO,EAAAC,KAAS,QAAAD;AAAA,GA9DA;AAAA,CAsEV,CAAUI,SAAV;AAAA,EACC,MAAM,QAA2C;AAAA,IAWvD,YACUH,MACA,YACR;AAFQ,iBAAAA;AACA;AAAA,IACP;AAAA,IAbH,QAAiB,wBAAU,IAAY;AAAA;AAAA,IAQvC,mBAAmB;AAAA,IAOnB,SAAc;AACb,aAAO,KAAK;AAAA,IACb;AAAA;AAAA,IAGA,QAAQ;AACP,aAAO,IAAI,QAAQ,KAAK,KAAK,KAAK,UAAU;AAAA,IAC7C;AAAA,EACD;AAxBO,EAAAG,KAAM;AAAA,GADG;AA4BV,MAAM,YAA+E;AAAA,EAK3F,YAAqBD,OAAa;AAAb,gBAAAA;AAAA,EAAc;AAAA,EAJnC,QAAiB,wBAAU,IAAY;AAAA,EAMvC,SAAc;AACb,WAAO,IAAI,IAAI,CAAC,IAAI,CAAC;AAAA,EACtB;AACD;AAGO,SAAS,YAAkCA,OAAiC;AAClF,SAAO,IAAI,YAAYA,KAAI;AAC5B;AAEO,SAAS,iBAAiB,QAAmB,QAA4C;AAC/F,SAAO,OAAO,IAAI,CAAC,MAAM;AACxB,YAAI,kBAAG,GAAG,WAAW,GAAG;AACvB,UAAI,EAAE,EAAE,QAAQ,SAAS;AACxB,cAAM,IAAI,MAAM,6BAA6B,EAAE,IAAI,gBAAgB;AAAA,MACpE;AAEA,aAAO,OAAO,EAAE,IAAI;AAAA,IACrB;AAEA,YAAI,kBAAG,GAAG,KAAK,SAAK,kBAAG,EAAE,OAAO,WAAW,GAAG;AAC7C,UAAI,EAAE,EAAE,MAAM,QAAQ,SAAS;AAC9B,cAAM,IAAI,MAAM,6BAA6B,EAAE,MAAM,IAAI,gBAAgB;AAAA,MAC1E;AAEA,aAAO,EAAE,QAAQ,iBAAiB,OAAO,EAAE,MAAM,IAAI,CAAC;AAAA,IACvD;AAEA,WAAO;AAAA,EACR,CAAC;AACF;AAIA,MAAM,gBAAgB,OAAO,IAAI,uBAAuB;AAEjD,MAAe,KAIE;AAAA,EACvB,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAWvC,CAAC,iCAAc;AAAA;AAAA,EAWf,CAAC,aAAa,IAAI;AAAA,EAIlB,YACC,EAAE,MAAAA,OAAM,QAAQ,gBAAgB,MAAM,GAMrC;AACD,SAAK,iCAAc,IAAI;AAAA,MACtB,MAAAA;AAAA,MACA,cAAcA;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,CAAC;AAAA,MACb,SAAS;AAAA,IACV;AAAA,EACD;AAAA,EAEA,SAAuB;AACtB,WAAO,IAAI,IAAI,CAAC,IAAI,CAAC;AAAA,EACtB;AACD;AAEO,SAAS,OAAO,MAA6B;AACnD,SAAO,OAAO,SAAS,YAAY,SAAS,QAAQ,iBAAiB;AACtE;AAEO,SAAS,YAA4B,MAAyB;AACpE,SAAO,KAAK,iCAAc,EAAE;AAC7B;AAWA,qBAAO,UAAU,SAAS,WAAW;AACpC,SAAO,IAAI,IAAI,CAAC,IAAI,CAAC;AACtB;AAGA,mBAAM,UAAU,SAAS,WAAW;AACnC,SAAO,IAAI,IAAI,CAAC,IAAI,CAAC;AACtB;AAGA,yBAAS,UAAU,SAAS,WAAW;AACtC,SAAO,IAAI,IAAI,CAAC,IAAI,CAAC;AACtB;", "names": ["param", "sql", "placeholder", "name", "SQL"]}