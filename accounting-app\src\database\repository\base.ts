/**
 * Base Repository Pattern
 *
 * This file provides a base repository with Odoo-like features including
 * pagination, filtering, sorting, and search capabilities.
 *
 * Following DRY principle: Single base repository for all entities
 * Following YAGNI principle: Only essential repository features
 */

import { getDatabase } from '../connection';
import { FilterOperator, SortOrder, paginationConfig, filterConfig } from '../config';
import { SQL, and, or, eq, ne, like, notLike, gt, gte, lt, lte, inArray, notInArray, isNull, isNotNull, between, sql } from 'drizzle-orm';

/**
 * Pagination parameters
 */
export interface PaginationParams {
  page?: number;
  pageSize?: number;
  offset?: number;
  limit?: number;
}

/**
 * Filter condition
 */
export interface FilterCondition {
  field: string;
  operator: FilterOperator;
  value: any;
  values?: any[]; // For 'in', 'not_in', 'between' operators
}

/**
 * Sort condition
 */
export interface SortCondition {
  field: string;
  order: SortOrder;
}

/**
 * Search parameters
 */
export interface SearchParams {
  query?: string;
  fields?: string[]; // Fields to search in
}

/**
 * Query options combining all parameters
 */
export interface QueryOptions {
  pagination?: PaginationParams;
  filters?: FilterCondition[];
  sort?: SortCondition[];
  search?: SearchParams;
  include?: string[]; // Relations to include
}

/**
 * Paginated result
 */
export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters?: FilterCondition[];
  sort?: SortCondition[];
  search?: SearchParams;
}

/**
 * Base repository class with Odoo-like features
 */
export abstract class BaseRepository<T = any> {
  protected db: any;
  protected tableName: string;
  protected table: any;

  constructor(tableName: string, table: any) {
    this.tableName = tableName;
    this.table = table;
  }

  /**
   * Initialize database connection
   */
  protected async initDb() {
    if (!this.db) {
      this.db = await getDatabase();
    }
    return this.db;
  }

  /**
   * Build filter conditions
   */
  protected buildFilterConditions(filters: FilterCondition[]): SQL[] {
    const conditions: SQL[] = [];

    for (const filter of filters) {
      const { field, operator, value, values } = filter;
      const column = this.table[field];

      if (!column) {
        console.warn(`Field '${field}' not found in table '${this.tableName}'`);
        continue;
      }

      switch (operator) {
        case 'equals':
          conditions.push(eq(column, value));
          break;
        case 'not_equals':
          conditions.push(ne(column, value));
          break;
        case 'contains':
          conditions.push(like(column, `%${value}%`));
          break;
        case 'not_contains':
          conditions.push(notLike(column, `%${value}%`));
          break;
        case 'starts_with':
          conditions.push(like(column, `${value}%`));
          break;
        case 'ends_with':
          conditions.push(like(column, `%${value}`));
          break;
        case 'greater_than':
          conditions.push(gt(column, value));
          break;
        case 'greater_than_or_equal':
          conditions.push(gte(column, value));
          break;
        case 'less_than':
          conditions.push(lt(column, value));
          break;
        case 'less_than_or_equal':
          conditions.push(lte(column, value));
          break;
        case 'in':
          if (values && values.length > 0) {
            conditions.push(inArray(column, values));
          }
          break;
        case 'not_in':
          if (values && values.length > 0) {
            conditions.push(notInArray(column, values));
          }
          break;
        case 'is_null':
          conditions.push(isNull(column));
          break;
        case 'is_not_null':
          conditions.push(isNotNull(column));
          break;
        case 'between':
          if (values && values.length === 2) {
            conditions.push(between(column, values[0], values[1]));
          }
          break;
        default:
          console.warn(`Unsupported filter operator: ${operator}`);
      }
    }

    return conditions;
  }

  /**
   * Build search conditions
   */
  protected buildSearchConditions(search: SearchParams): SQL[] {
    if (!search.query || !search.fields || search.fields.length === 0) {
      return [];
    }

    const searchConditions: SQL[] = [];
    const searchTerm = `%${search.query}%`;

    for (const fieldName of search.fields) {
      const column = this.table[fieldName];
      if (column) {
        searchConditions.push(like(column, searchTerm));
      }
    }

    return searchConditions;
  }

  /**
   * Build sort conditions
   */
  protected buildSortConditions(sort: SortCondition[]) {
    const orderBy: any[] = [];

    for (const sortCondition of sort) {
      const column = this.table[sortCondition.field];
      if (column) {
        orderBy.push(sortCondition.order === 'desc' ? column.desc() : column.asc());
      }
    }

    return orderBy;
  }

  /**
   * Calculate pagination parameters
   */
  protected calculatePagination(params: PaginationParams): {
    limit: number;
    offset: number;
    page: number;
    pageSize: number;
  } {
    let { page, pageSize, offset, limit } = params;

    // Use provided limit/offset or calculate from page/pageSize
    if (limit !== undefined && offset !== undefined) {
      pageSize = limit;
      page = Math.floor(offset / limit) + 1;
    } else {
      page = page || 1;
      pageSize = Math.min(pageSize || paginationConfig.defaultPageSize, paginationConfig.maxPageSize);
      offset = (page - 1) * pageSize;
      limit = pageSize;
    }

    return { limit, offset, page, pageSize };
  }

  /**
   * Find all records with pagination and filtering
   */
  async findAll(options: QueryOptions = {}): Promise<PaginatedResult<T>> {
    const db = await this.initDb();

    // Build query conditions
    const filterConditions = options.filters ? this.buildFilterConditions(options.filters) : [];
    const searchConditions = options.search ? this.buildSearchConditions(options.search) : [];
    const allConditions = [...filterConditions, ...searchConditions];

    // Build where clause
    const whereClause = allConditions.length > 0 ? and(...allConditions) : undefined;

    // Build sort conditions
    const orderBy = options.sort ? this.buildSortConditions(options.sort) : [];

    // Calculate pagination
    const pagination = this.calculatePagination(options.pagination || {});

    // Get total count
    const countQuery = db.select({ count: sql`COUNT(*)` }).from(this.table);
    if (whereClause) {
      countQuery.where(whereClause);
    }
    const [{ count }] = await countQuery;
    const total = Number(count);

    // Get paginated data
    let dataQuery = db.select().from(this.table);

    if (whereClause) {
      dataQuery = dataQuery.where(whereClause);
    }

    if (orderBy.length > 0) {
      dataQuery = dataQuery.orderBy(...orderBy);
    }

    dataQuery = dataQuery.limit(pagination.limit).offset(pagination.offset);

    const data = await dataQuery;

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / pagination.pageSize);
    const hasNext = pagination.page < totalPages;
    const hasPrev = pagination.page > 1;

    return {
      data,
      pagination: {
        page: pagination.page,
        pageSize: pagination.pageSize,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
      filters: options.filters,
      sort: options.sort,
      search: options.search,
    };
  }

  /**
   * Find one record by ID
   */
  async findById(id: string): Promise<T | null> {
    const db = await this.initDb();

    const result = await db.select()
      .from(this.table)
      .where(eq(this.table.id, id))
      .limit(1);

    return result[0] || null;
  }

  /**
   * Find one record by conditions
   */
  async findOne(conditions: FilterCondition[]): Promise<T | null> {
    const db = await this.initDb();

    const filterConditions = this.buildFilterConditions(conditions);
    const whereClause = filterConditions.length > 0 ? and(...filterConditions) : undefined;

    let query = db.select().from(this.table);

    if (whereClause) {
      query = query.where(whereClause);
    }

    const result = await query.limit(1);
    return result[0] || null;
  }

  /**
   * Create a new record
   */
  async create(data: Partial<T>): Promise<T> {
    const db = await this.initDb();

    const result = await db.insert(this.table).values(data).returning();
    return result[0];
  }

  /**
   * Update a record by ID
   */
  async update(id: string, data: Partial<T>): Promise<T | null> {
    const db = await this.initDb();

    const result = await db.update(this.table)
      .set(data)
      .where(eq(this.table.id, id))
      .returning();

    return result[0] || null;
  }

  /**
   * Delete a record by ID
   */
  async delete(id: string): Promise<boolean> {
    const db = await this.initDb();

    const result = await db.delete(this.table)
      .where(eq(this.table.id, id));

    return result.changes > 0;
  }

  /**
   * Count records with filters
   */
  async count(filters?: FilterCondition[]): Promise<number> {
    const db = await this.initDb();

    let query = db.select({ count: sql`COUNT(*)` }).from(this.table);

    if (filters && filters.length > 0) {
      const filterConditions = this.buildFilterConditions(filters);
      const whereClause = and(...filterConditions);
      query = query.where(whereClause);
    }

    const [{ count }] = await query;
    return Number(count);
  }

  /**
   * Check if record exists
   */
  async exists(id: string): Promise<boolean> {
    const record = await this.findById(id);
    return record !== null;
  }

  /**
   * Bulk create records
   */
  async bulkCreate(data: Partial<T>[]): Promise<T[]> {
    const db = await this.initDb();

    const result = await db.insert(this.table).values(data).returning();
    return result;
  }

  /**
   * Bulk update records
   */
  async bulkUpdate(conditions: FilterCondition[], data: Partial<T>): Promise<number> {
    const db = await this.initDb();

    const filterConditions = this.buildFilterConditions(conditions);
    const whereClause = and(...filterConditions);

    const result = await db.update(this.table)
      .set(data)
      .where(whereClause);

    return result.changes;
  }

  /**
   * Bulk delete records
   */
  async bulkDelete(conditions: FilterCondition[]): Promise<number> {
    const db = await this.initDb();

    const filterConditions = this.buildFilterConditions(conditions);
    const whereClause = and(...filterConditions);

    const result = await db.delete(this.table)
      .where(whereClause);

    return result.changes;
  }
}
