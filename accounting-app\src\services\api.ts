/**
 * API Service Layer
 * 
 * This file contains the core API service functions that handle all HTTP requests
 * to the backend. It provides a centralized way to manage API calls with consistent
 * error handling, request/response formatting, and authentication.
 */

import { API_CONFIG, ERROR_MESSAGES } from '@/constants';
import { ApiResponse, ErrorResponse } from '@/types';

/**
 * Configuration interface for API requests
 */
interface RequestConfig {
  /** HTTP method (GET, POST, PUT, DELETE, etc.) */
  method?: string;
  /** Request headers */
  headers?: Record<string, string>;
  /** Request body (will be JSON stringified if object) */
  body?: any;
  /** Request timeout in milliseconds */
  timeout?: number;
  /** Whether to include credentials (cookies) */
  credentials?: RequestCredentials;
}

/**
 * API Client class that handles all HTTP requests
 * This class provides a consistent interface for making API calls with
 * automatic error handling, request formatting, and response parsing.
 */
class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;
  private defaultTimeout: number;

  /**
   * Initialize the API client with default configuration
   */
  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.defaultTimeout = API_CONFIG.TIMEOUT;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  /**
   * Set the authentication token for all subsequent requests
   * @param token - The authentication token (JWT, Bearer token, etc.)
   */
  setAuthToken(token: string): void {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  /**
   * Remove the authentication token
   */
  clearAuthToken(): void {
    delete this.defaultHeaders['Authorization'];
  }

  /**
   * Set a default header for all requests
   * @param key - Header name
   * @param value - Header value
   */
  setDefaultHeader(key: string, value: string): void {
    this.defaultHeaders[key] = value;
  }

  /**
   * Remove a default header
   * @param key - Header name to remove
   */
  removeDefaultHeader(key: string): void {
    delete this.defaultHeaders[key];
  }

  /**
   * Build the full URL for a request
   * @param endpoint - The API endpoint (e.g., '/users', '/companies/123')
   * @returns The complete URL
   */
  private buildURL(endpoint: string): string {
    // Remove leading slash if present to avoid double slashes
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    
    // Ensure baseURL doesn't end with slash to avoid double slashes
    const cleanBaseURL = this.baseURL.endsWith('/') ? this.baseURL.slice(0, -1) : this.baseURL;
    
    return `${cleanBaseURL}/${cleanEndpoint}`;
  }

  /**
   * Prepare the request configuration
   * @param config - Request configuration
   * @returns Prepared fetch configuration
   */
  private prepareRequestConfig(config: RequestConfig = {}): RequestInit {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = this.defaultTimeout,
      credentials = 'same-origin',
    } = config;

    // Merge default headers with request-specific headers
    const mergedHeaders = {
      ...this.defaultHeaders,
      ...headers,
    };

    // Prepare the request configuration
    const requestConfig: RequestInit = {
      method: method.toUpperCase(),
      headers: mergedHeaders,
      credentials,
    };

    // Add body if provided (and not for GET requests)
    if (body && method.toUpperCase() !== 'GET') {
      if (typeof body === 'object' && !(body instanceof FormData)) {
        // JSON stringify objects (except FormData)
        requestConfig.body = JSON.stringify(body);
      } else {
        // Use body as-is for strings, FormData, etc.
        requestConfig.body = body;
      }
    }

    return requestConfig;
  }

  /**
   * Handle the response from a fetch request
   * @param response - The fetch Response object
   * @returns Parsed response data
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    // Check if response is ok (status 200-299)
    if (!response.ok) {
      // Try to parse error response
      let errorMessage = ERROR_MESSAGES.GENERIC;
      let errorDetails = '';

      try {
        const errorData: ErrorResponse = await response.json();
        errorMessage = errorData.error || errorMessage;
        errorDetails = errorData.details || '';
      } catch {
        // If parsing fails, use status text
        errorMessage = response.statusText || `HTTP ${response.status}`;
      }

      // Create error object with additional information
      const error = new Error(errorMessage) as Error & {
        status: number;
        details: string;
      };
      error.status = response.status;
      error.details = errorDetails;

      throw error;
    }

    // Check if response has content
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      // Return empty object for non-JSON responses
      return {} as T;
    }

    // Parse JSON response
    try {
      const data: ApiResponse<T> = await response.json();
      
      // Check if the API response indicates success
      if (data.success !== false && data.data !== undefined) {
        return data.data;
      } else {
        // API returned an error
        throw new Error(data.error || ERROR_MESSAGES.GENERIC);
      }
    } catch (error) {
      // JSON parsing failed
      if (error instanceof Error && error.message.includes('JSON')) {
        throw new Error('Invalid response format from server');
      }
      throw error;
    }
  }

  /**
   * Make a generic HTTP request
   * @param endpoint - The API endpoint
   * @param config - Request configuration
   * @returns Promise that resolves to the response data
   */
  async request<T = any>(endpoint: string, config: RequestConfig = {}): Promise<T> {
    const url = this.buildURL(endpoint);
    const requestConfig = this.prepareRequestConfig(config);

    try {
      // Make the fetch request
      const response = await fetch(url, requestConfig);
      
      // Handle the response
      return await this.handleResponse<T>(response);
    } catch (error) {
      // Handle network errors and other exceptions
      if (error instanceof TypeError) {
        // Network error (no internet, server down, etc.)
        throw new Error(ERROR_MESSAGES.NETWORK);
      }
      
      // Re-throw other errors as-is
      throw error;
    }
  }

  /**
   * Make a GET request
   * @param endpoint - The API endpoint
   * @param config - Additional request configuration
   * @returns Promise that resolves to the response data
   */
  async get<T = any>(endpoint: string, config: Omit<RequestConfig, 'method' | 'body'> = {}): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  /**
   * Make a POST request
   * @param endpoint - The API endpoint
   * @param data - Data to send in the request body
   * @param config - Additional request configuration
   * @returns Promise that resolves to the response data
   */
  async post<T = any>(endpoint: string, data?: any, config: Omit<RequestConfig, 'method'> = {}): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'POST', body: data });
  }

  /**
   * Make a PUT request
   * @param endpoint - The API endpoint
   * @param data - Data to send in the request body
   * @param config - Additional request configuration
   * @returns Promise that resolves to the response data
   */
  async put<T = any>(endpoint: string, data?: any, config: Omit<RequestConfig, 'method'> = {}): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'PUT', body: data });
  }

  /**
   * Make a PATCH request
   * @param endpoint - The API endpoint
   * @param data - Data to send in the request body
   * @param config - Additional request configuration
   * @returns Promise that resolves to the response data
   */
  async patch<T = any>(endpoint: string, data?: any, config: Omit<RequestConfig, 'method'> = {}): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'PATCH', body: data });
  }

  /**
   * Make a DELETE request
   * @param endpoint - The API endpoint
   * @param config - Additional request configuration
   * @returns Promise that resolves to the response data
   */
  async delete<T = any>(endpoint: string, config: Omit<RequestConfig, 'method' | 'body'> = {}): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }
}

/**
 * Default API client instance
 * This is the main instance used throughout the application
 */
export const apiClient = new ApiClient();

/**
 * Convenience functions that use the default API client
 * These provide a simpler interface for making API calls
 */

/**
 * Make a GET request using the default API client
 */
export const get = <T = any>(endpoint: string, config?: Omit<RequestConfig, 'method' | 'body'>) =>
  apiClient.get<T>(endpoint, config);

/**
 * Make a POST request using the default API client
 */
export const post = <T = any>(endpoint: string, data?: any, config?: Omit<RequestConfig, 'method'>) =>
  apiClient.post<T>(endpoint, data, config);

/**
 * Make a PUT request using the default API client
 */
export const put = <T = any>(endpoint: string, data?: any, config?: Omit<RequestConfig, 'method'>) =>
  apiClient.put<T>(endpoint, data, config);

/**
 * Make a PATCH request using the default API client
 */
export const patch = <T = any>(endpoint: string, data?: any, config?: Omit<RequestConfig, 'method'>) =>
  apiClient.patch<T>(endpoint, data, config);

/**
 * Make a DELETE request using the default API client
 */
export const del = <T = any>(endpoint: string, config?: Omit<RequestConfig, 'method' | 'body'>) =>
  apiClient.delete<T>(endpoint, config);

/**
 * Export the API client class for creating custom instances if needed
 */
export { ApiClient };

/**
 * Export the default API client instance
 */
export default apiClient;
