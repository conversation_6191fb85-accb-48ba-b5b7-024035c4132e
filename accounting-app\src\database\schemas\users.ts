/**
 * Users Schema Definition
 * 
 * This file defines the users table schema using Drizzle ORM.
 * It handles user authentication and profile information.
 * 
 * Following DRY principle: Single schema per entity
 * Following YAGNI principle: Only essential user fields
 */

import { sqliteTable, text, index } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

/**
 * Users table - Stores user authentication and profile information
 * 
 * Following proper ORM patterns: Clear table definition with constraints
 */
export const users = sqliteTable('users', {
  /** Unique identifier for the user */
  id: text('id').primaryKey(),
  
  /** User's email address (unique, used for authentication) */
  email: text('email').notNull().unique(),
  
  /** User's display name */
  name: text('name').notNull(),
  
  /** Hashed password for authentication */
  passwordHash: text('password_hash').notNull(),
  
  /** Timestamp when the user was created */
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  
  /** Timestamp when the user was last updated */
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  /** Index on email for fast lookups during authentication */
  emailIdx: index('users_email_idx').on(table.email),
  
  /** Index on created_at for sorting and pagination */
  createdAtIdx: index('users_created_at_idx').on(table.createdAt),
}));

/**
 * Type definitions for users table operations
 * These provide type safety for insert, select, and update operations
 */

/** Type for inserting a new user */
export type InsertUser = typeof users.$inferInsert;

/** Type for selecting user data */
export type SelectUser = typeof users.$inferSelect;

/** Type for user data without sensitive information (password) */
export type PublicUser = Omit<SelectUser, 'passwordHash'>;

/**
 * User validation constraints
 * Following DRY principle: Centralized validation rules
 */
export const UserConstraints = {
  /** Minimum length for user names */
  NAME_MIN_LENGTH: 1,
  /** Maximum length for user names */
  NAME_MAX_LENGTH: 100,
  /** Maximum length for email addresses */
  EMAIL_MAX_LENGTH: 255,
  /** Minimum password length */
  PASSWORD_MIN_LENGTH: 6,
  /** Maximum password length */
  PASSWORD_MAX_LENGTH: 128,
} as const;
