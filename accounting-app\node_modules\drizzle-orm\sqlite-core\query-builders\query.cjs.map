{"version": 3, "sources": ["../../../src/sqlite-core/query-builders/query.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport {\n\ttype BuildQueryResult,\n\ttype BuildRelationalQueryResult,\n\ttype DBQueryConfig,\n\tmapRelationalRow,\n\ttype TableRelationalConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Query, QueryWithTypings, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport type { KnownKeysOnly } from '~/utils.ts';\nimport type { SQLiteDialect } from '../dialect.ts';\nimport type { PreparedQueryConfig, SQLitePreparedQuery, SQLiteSession } from '../session.ts';\nimport type { SQLiteTable } from '../table.ts';\n\nexport type SQLiteRelationalQueryKind<TMode extends 'sync' | 'async', TResult> = TMode extends 'async'\n\t? SQLiteRelationalQuery<TMode, TResult>\n\t: SQLiteSyncRelationalQuery<TResult>;\n\nexport class RelationalQueryBuilder<\n\tTMode extends 'sync' | 'async',\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n\tTFields extends TableRelationalConfig,\n> {\n\tstatic readonly [entityKind]: string = 'SQLiteAsyncRelationalQueryBuilder';\n\n\tconstructor(\n\t\tprotected mode: TMode,\n\t\tprotected fullSchema: Record<string, unknown>,\n\t\tprotected schema: TSchema,\n\t\tprotected tableNamesMap: Record<string, string>,\n\t\tprotected table: SQLiteTable,\n\t\tprotected tableConfig: TableRelationalConfig,\n\t\tprotected dialect: SQLiteDialect,\n\t\tprotected session: SQLiteSession<'async', unknown, TFullSchema, TSchema>,\n\t) {}\n\n\tfindMany<TConfig extends DBQueryConfig<'many', true, TSchema, TFields>>(\n\t\tconfig?: KnownKeysOnly<TConfig, DBQueryConfig<'many', true, TSchema, TFields>>,\n\t): SQLiteRelationalQueryKind<TMode, BuildQueryResult<TSchema, TFields, TConfig>[]> {\n\t\treturn (this.mode === 'sync'\n\t\t\t? new SQLiteSyncRelationalQuery(\n\t\t\t\tthis.fullSchema,\n\t\t\t\tthis.schema,\n\t\t\t\tthis.tableNamesMap,\n\t\t\t\tthis.table,\n\t\t\t\tthis.tableConfig,\n\t\t\t\tthis.dialect,\n\t\t\t\tthis.session,\n\t\t\t\tconfig ? (config as DBQueryConfig<'many', true>) : {},\n\t\t\t\t'many',\n\t\t\t)\n\t\t\t: new SQLiteRelationalQuery(\n\t\t\t\tthis.fullSchema,\n\t\t\t\tthis.schema,\n\t\t\t\tthis.tableNamesMap,\n\t\t\t\tthis.table,\n\t\t\t\tthis.tableConfig,\n\t\t\t\tthis.dialect,\n\t\t\t\tthis.session,\n\t\t\t\tconfig ? (config as DBQueryConfig<'many', true>) : {},\n\t\t\t\t'many',\n\t\t\t)) as SQLiteRelationalQueryKind<TMode, BuildQueryResult<TSchema, TFields, TConfig>[]>;\n\t}\n\n\tfindFirst<TSelection extends Omit<DBQueryConfig<'many', true, TSchema, TFields>, 'limit'>>(\n\t\tconfig?: KnownKeysOnly<TSelection, Omit<DBQueryConfig<'many', true, TSchema, TFields>, 'limit'>>,\n\t): SQLiteRelationalQueryKind<TMode, BuildQueryResult<TSchema, TFields, TSelection> | undefined> {\n\t\treturn (this.mode === 'sync'\n\t\t\t? new SQLiteSyncRelationalQuery(\n\t\t\t\tthis.fullSchema,\n\t\t\t\tthis.schema,\n\t\t\t\tthis.tableNamesMap,\n\t\t\t\tthis.table,\n\t\t\t\tthis.tableConfig,\n\t\t\t\tthis.dialect,\n\t\t\t\tthis.session,\n\t\t\t\tconfig ? { ...(config as DBQueryConfig<'many', true> | undefined), limit: 1 } : { limit: 1 },\n\t\t\t\t'first',\n\t\t\t)\n\t\t\t: new SQLiteRelationalQuery(\n\t\t\t\tthis.fullSchema,\n\t\t\t\tthis.schema,\n\t\t\t\tthis.tableNamesMap,\n\t\t\t\tthis.table,\n\t\t\t\tthis.tableConfig,\n\t\t\t\tthis.dialect,\n\t\t\t\tthis.session,\n\t\t\t\tconfig ? { ...(config as DBQueryConfig<'many', true> | undefined), limit: 1 } : { limit: 1 },\n\t\t\t\t'first',\n\t\t\t)) as SQLiteRelationalQueryKind<TMode, BuildQueryResult<TSchema, TFields, TSelection> | undefined>;\n\t}\n}\n\nexport class SQLiteRelationalQuery<TType extends 'sync' | 'async', TResult> extends QueryPromise<TResult>\n\timplements RunnableQuery<TResult, 'sqlite'>, SQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteAsyncRelationalQuery';\n\n\tdeclare readonly _: {\n\t\treadonly dialect: 'sqlite';\n\t\treadonly type: TType;\n\t\treadonly result: TResult;\n\t};\n\n\t/** @internal */\n\tmode: 'many' | 'first';\n\n\tconstructor(\n\t\tprivate fullSchema: Record<string, unknown>,\n\t\tprivate schema: TablesRelationalConfig,\n\t\tprivate tableNamesMap: Record<string, string>,\n\t\t/** @internal */\n\t\tpublic table: SQLiteTable,\n\t\tprivate tableConfig: TableRelationalConfig,\n\t\tprivate dialect: SQLiteDialect,\n\t\tprivate session: SQLiteSession<'sync' | 'async', unknown, Record<string, unknown>, TablesRelationalConfig>,\n\t\tprivate config: DBQueryConfig<'many', true> | true,\n\t\tmode: 'many' | 'first',\n\t) {\n\t\tsuper();\n\t\tthis.mode = mode;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildRelationalQuery({\n\t\t\tfullSchema: this.fullSchema,\n\t\t\tschema: this.schema,\n\t\t\ttableNamesMap: this.tableNamesMap,\n\t\t\ttable: this.table,\n\t\t\ttableConfig: this.tableConfig,\n\t\t\tqueryConfig: this.config,\n\t\t\ttableAlias: this.tableConfig.tsName,\n\t\t}).sql as SQL;\n\t}\n\n\t/** @internal */\n\t_prepare(\n\t\tisOneTimeQuery = false,\n\t): SQLitePreparedQuery<PreparedQueryConfig & { type: TType; all: TResult; get: TResult; execute: TResult }> {\n\t\tconst { query, builtQuery } = this._toSQL();\n\n\t\treturn this.session[isOneTimeQuery ? 'prepareOneTimeQuery' : 'prepareQuery'](\n\t\t\tbuiltQuery,\n\t\t\tundefined,\n\t\t\tthis.mode === 'first' ? 'get' : 'all',\n\t\t\ttrue,\n\t\t\t(rawRows, mapColumnValue) => {\n\t\t\t\tconst rows = rawRows.map((row) =>\n\t\t\t\t\tmapRelationalRow(this.schema, this.tableConfig, row, query.selection, mapColumnValue)\n\t\t\t\t);\n\t\t\t\tif (this.mode === 'first') {\n\t\t\t\t\treturn rows[0] as TResult;\n\t\t\t\t}\n\t\t\t\treturn rows as TResult;\n\t\t\t},\n\t\t) as SQLitePreparedQuery<PreparedQueryConfig & { type: TType; all: TResult; get: TResult; execute: TResult }>;\n\t}\n\n\tprepare(): SQLitePreparedQuery<PreparedQueryConfig & { type: TType; all: TResult; get: TResult; execute: TResult }> {\n\t\treturn this._prepare(false);\n\t}\n\n\tprivate _toSQL(): { query: BuildRelationalQueryResult; builtQuery: QueryWithTypings } {\n\t\tconst query = this.dialect.buildRelationalQuery({\n\t\t\tfullSchema: this.fullSchema,\n\t\t\tschema: this.schema,\n\t\t\ttableNamesMap: this.tableNamesMap,\n\t\t\ttable: this.table,\n\t\t\ttableConfig: this.tableConfig,\n\t\t\tqueryConfig: this.config,\n\t\t\ttableAlias: this.tableConfig.tsName,\n\t\t});\n\n\t\tconst builtQuery = this.dialect.sqlToQuery(query.sql as SQL);\n\n\t\treturn { query, builtQuery };\n\t}\n\n\ttoSQL(): Query {\n\t\treturn this._toSQL().builtQuery;\n\t}\n\n\t/** @internal */\n\texecuteRaw(): TResult {\n\t\tif (this.mode === 'first') {\n\t\t\treturn this._prepare(false).get() as TResult;\n\t\t}\n\t\treturn this._prepare(false).all() as TResult;\n\t}\n\n\toverride async execute(): Promise<TResult> {\n\t\treturn this.executeRaw();\n\t}\n}\n\nexport class SQLiteSyncRelationalQuery<TResult> extends SQLiteRelationalQuery<'sync', TResult> {\n\tstatic override readonly [entityKind]: string = 'SQLiteSyncRelationalQuery';\n\n\tsync(): TResult {\n\t\treturn this.executeRaw();\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAC3B,2BAA6B;AAC7B,uBAOO;AAYA,MAAM,uBAKX;AAAA,EAGD,YACW,MACA,YACA,QACA,eACA,OACA,aACA,SACA,SACT;AARS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAXH,QAAiB,wBAAU,IAAY;AAAA,EAavC,SACC,QACkF;AAClF,WAAQ,KAAK,SAAS,SACnB,IAAI;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,SAAU,SAAyC,CAAC;AAAA,MACpD;AAAA,IACD,IACE,IAAI;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,SAAU,SAAyC,CAAC;AAAA,MACpD;AAAA,IACD;AAAA,EACF;AAAA,EAEA,UACC,QAC+F;AAC/F,WAAQ,KAAK,SAAS,SACnB,IAAI;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,SAAS,EAAE,GAAI,QAAoD,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;AAAA,MAC3F;AAAA,IACD,IACE,IAAI;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,SAAS,EAAE,GAAI,QAAoD,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;AAAA,MAC3F;AAAA,IACD;AAAA,EACF;AACD;AAEO,MAAM,8BAAuE,kCAEpF;AAAA,EAYC,YACS,YACA,QACA,eAED,OACC,aACA,SACA,SACA,QACR,MACC;AACD,UAAM;AAXE;AACA;AACA;AAED;AACC;AACA;AACA;AACA;AAIR,SAAK,OAAO;AAAA,EACb;AAAA,EAzBA,QAA0B,wBAAU,IAAY;AAAA;AAAA,EAShD;AAAA;AAAA,EAmBA,SAAc;AACb,WAAO,KAAK,QAAQ,qBAAqB;AAAA,MACxC,YAAY,KAAK;AAAA,MACjB,QAAQ,KAAK;AAAA,MACb,eAAe,KAAK;AAAA,MACpB,OAAO,KAAK;AAAA,MACZ,aAAa,KAAK;AAAA,MAClB,aAAa,KAAK;AAAA,MAClB,YAAY,KAAK,YAAY;AAAA,IAC9B,CAAC,EAAE;AAAA,EACJ;AAAA;AAAA,EAGA,SACC,iBAAiB,OAC0F;AAC3G,UAAM,EAAE,OAAO,WAAW,IAAI,KAAK,OAAO;AAE1C,WAAO,KAAK,QAAQ,iBAAiB,wBAAwB,cAAc;AAAA,MAC1E;AAAA,MACA;AAAA,MACA,KAAK,SAAS,UAAU,QAAQ;AAAA,MAChC;AAAA,MACA,CAAC,SAAS,mBAAmB;AAC5B,cAAM,OAAO,QAAQ;AAAA,UAAI,CAAC,YACzB,mCAAiB,KAAK,QAAQ,KAAK,aAAa,KAAK,MAAM,WAAW,cAAc;AAAA,QACrF;AACA,YAAI,KAAK,SAAS,SAAS;AAC1B,iBAAO,KAAK,CAAC;AAAA,QACd;AACA,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD;AAAA,EAEA,UAAoH;AACnH,WAAO,KAAK,SAAS,KAAK;AAAA,EAC3B;AAAA,EAEQ,SAA8E;AACrF,UAAM,QAAQ,KAAK,QAAQ,qBAAqB;AAAA,MAC/C,YAAY,KAAK;AAAA,MACjB,QAAQ,KAAK;AAAA,MACb,eAAe,KAAK;AAAA,MACpB,OAAO,KAAK;AAAA,MACZ,aAAa,KAAK;AAAA,MAClB,aAAa,KAAK;AAAA,MAClB,YAAY,KAAK,YAAY;AAAA,IAC9B,CAAC;AAED,UAAM,aAAa,KAAK,QAAQ,WAAW,MAAM,GAAU;AAE3D,WAAO,EAAE,OAAO,WAAW;AAAA,EAC5B;AAAA,EAEA,QAAe;AACd,WAAO,KAAK,OAAO,EAAE;AAAA,EACtB;AAAA;AAAA,EAGA,aAAsB;AACrB,QAAI,KAAK,SAAS,SAAS;AAC1B,aAAO,KAAK,SAAS,KAAK,EAAE,IAAI;AAAA,IACjC;AACA,WAAO,KAAK,SAAS,KAAK,EAAE,IAAI;AAAA,EACjC;AAAA,EAEA,MAAe,UAA4B;AAC1C,WAAO,KAAK,WAAW;AAAA,EACxB;AACD;AAEO,MAAM,kCAA2C,sBAAuC;AAAA,EAC9F,QAA0B,wBAAU,IAAY;AAAA,EAEhD,OAAgB;AACf,WAAO,KAAK,WAAW;AAAA,EACxB;AACD;", "names": []}