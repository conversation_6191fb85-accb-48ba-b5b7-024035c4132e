{"version": 3, "sources": ["../../src/gel-core/subquery.ts"], "sourcesContent": ["import type { AddAliasToSelection } from '~/query-builders/select.types.ts';\nimport type { ColumnsSelection } from '~/sql/sql.ts';\nimport type { Subquery, WithSubquery } from '~/subquery.ts';\n\nexport type SubqueryWithSelection<TSelection extends ColumnsSelection, T<PERSON>lias extends string> =\n\t& Subquery<TAlias, AddAliasToSelection<TSelection, TAlias, 'gel'>>\n\t& AddAliasToSelection<TSelection, TAlias, 'gel'>;\n\nexport type WithSubqueryWithSelection<TSelection extends ColumnsSelection, <PERSON><PERSON><PERSON>s extends string> =\n\t& WithSubquery<TAlias, AddAliasToSelection<TSelection, TAlias, 'gel'>>\n\t& AddAliasToSelection<TSelection, TAlias, 'gel'>;\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}