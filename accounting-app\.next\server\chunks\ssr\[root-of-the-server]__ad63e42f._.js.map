{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/src/app/auth/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  TextField,\n  Button,\n  Typography,\n  Link,\n  Alert,\n  Container,\n  Paper,\n} from '@mui/material';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\n\nexport default function LoginPage() {\n  const { signIn } = useAuth();\n  const router = useRouter();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n\n    try {\n      const result = await signIn(email, password);\n      if (result.error) {\n        setError(result.error);\n      } else {\n        router.push('/dashboard');\n      }\n    } catch (err) {\n      setError('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Box\n      sx={{\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        p: 2,\n      }}\n    >\n      <Container maxWidth=\"sm\">\n        <Paper\n          elevation={10}\n          sx={{\n            p: 4,\n            borderRadius: 2,\n            backgroundColor: 'rgba(255, 255, 255, 0.95)',\n            backdropFilter: 'blur(10px)',\n          }}\n        >\n          <Box sx={{ textAlign: 'center', mb: 4 }}>\n            <Typography\n              variant=\"h3\"\n              sx={{\n                fontWeight: 'bold',\n                color: '#2c3e50',\n                mb: 1,\n              }}\n            >\n              AccountingApp\n            </Typography>\n            <Typography variant=\"h5\" sx={{ color: '#7f8c8d', mb: 3 }}>\n              Sign in to your account\n            </Typography>\n          </Box>\n\n          {/* Demo Credentials Banner */}\n          <Alert severity=\"info\" sx={{ mb: 3 }}>\n            <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              Demo Credentials\n            </Typography>\n            <Typography variant=\"body2\">\n              <strong>Email:</strong> <EMAIL><br />\n              <strong>Password:</strong> 123456\n            </Typography>\n          </Alert>\n\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 3 }}>\n              {error}\n            </Alert>\n          )}\n\n          <form onSubmit={handleSubmit}>\n            <TextField\n              fullWidth\n              label=\"Email Address\"\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              required\n              sx={{ mb: 3 }}\n              variant=\"outlined\"\n            />\n\n            <TextField\n              fullWidth\n              label=\"Password\"\n              type=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              required\n              sx={{ mb: 3 }}\n              variant=\"outlined\"\n            />\n\n            <Button\n              type=\"submit\"\n              fullWidth\n              variant=\"contained\"\n              size=\"large\"\n              disabled={loading}\n              sx={{\n                mb: 3,\n                py: 1.5,\n                fontSize: '1.1rem',\n                fontWeight: 'bold',\n                background: 'linear-gradient(45deg, #3498db, #2980b9)',\n                '&:hover': {\n                  background: 'linear-gradient(45deg, #2980b9, #1f5f8b)',\n                },\n              }}\n            >\n              {loading ? 'Signing In...' : 'Sign In'}\n            </Button>\n\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"body2\" sx={{ color: '#7f8c8d' }}>\n                Don't have an account?{' '}\n                <Link\n                  href=\"/auth/register\"\n                  sx={{\n                    color: '#3498db',\n                    textDecoration: 'none',\n                    fontWeight: 'bold',\n                    '&:hover': {\n                      textDecoration: 'underline',\n                    },\n                  }}\n                >\n                  Sign up here\n                </Link>\n              </Typography>\n            </Box>\n          </form>\n        </Paper>\n      </Container>\n    </Box>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAhBA;;;;;;AAkBe,SAAS;IACtB,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,SAAS,MAAM,OAAO,OAAO;YACnC,IAAI,OAAO,KAAK,EAAE;gBAChB,SAAS,OAAO,KAAK;YACvB,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,wLAAA,CAAA,MAAG;QACF,IAAI;YACF,WAAW;YACX,YAAY;YACZ,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,GAAG;QACL;kBAEA,cAAA,8OAAC,0MAAA,CAAA,YAAS;YAAC,UAAS;sBAClB,cAAA,8OAAC,8LAAA,CAAA,QAAK;gBACJ,WAAW;gBACX,IAAI;oBACF,GAAG;oBACH,cAAc;oBACd,iBAAiB;oBACjB,gBAAgB;gBAClB;;kCAEA,8OAAC,wLAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE,WAAW;4BAAU,IAAI;wBAAE;;0CACpC,8OAAC,6MAAA,CAAA,aAAU;gCACT,SAAQ;gCACR,IAAI;oCACF,YAAY;oCACZ,OAAO;oCACP,IAAI;gCACN;0CACD;;;;;;0CAGD,8OAAC,6MAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAK,IAAI;oCAAE,OAAO;oCAAW,IAAI;gCAAE;0CAAG;;;;;;;;;;;;kCAM5D,8OAAC,8LAAA,CAAA,QAAK;wBAAC,UAAS;wBAAO,IAAI;4BAAE,IAAI;wBAAE;;0CACjC,8OAAC,6MAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAY,IAAI;oCAAE,YAAY;oCAAQ,IAAI;gCAAE;0CAAG;;;;;;0CAGnE,8OAAC,6MAAA,CAAA,aAAU;gCAAC,SAAQ;;kDAClB,8OAAC;kDAAO;;;;;;oCAAe;kDAAc,8OAAC;;;;;kDACtC,8OAAC;kDAAO;;;;;;oCAAkB;;;;;;;;;;;;;oBAI7B,uBACC,8OAAC,8LAAA,CAAA,QAAK;wBAAC,UAAS;wBAAQ,IAAI;4BAAE,IAAI;wBAAE;kCACjC;;;;;;kCAIL,8OAAC;wBAAK,UAAU;;0CACd,8OAAC,0MAAA,CAAA,YAAS;gCACR,SAAS;gCACT,OAAM;gCACN,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,QAAQ;gCACR,IAAI;oCAAE,IAAI;gCAAE;gCACZ,SAAQ;;;;;;0CAGV,8OAAC,0MAAA,CAAA,YAAS;gCACR,SAAS;gCACT,OAAM;gCACN,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,QAAQ;gCACR,IAAI;oCAAE,IAAI;gCAAE;gCACZ,SAAQ;;;;;;0CAGV,8OAAC,iMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS;gCACT,SAAQ;gCACR,MAAK;gCACL,UAAU;gCACV,IAAI;oCACF,IAAI;oCACJ,IAAI;oCACJ,UAAU;oCACV,YAAY;oCACZ,YAAY;oCACZ,WAAW;wCACT,YAAY;oCACd;gCACF;0CAEC,UAAU,kBAAkB;;;;;;0CAG/B,8OAAC,wLAAA,CAAA,MAAG;gCAAC,IAAI;oCAAE,WAAW;gCAAS;0CAC7B,cAAA,8OAAC,6MAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAQ,IAAI;wCAAE,OAAO;oCAAU;;wCAAG;wCAC7B;sDACvB,8OAAC,2LAAA,CAAA,OAAI;4CACH,MAAK;4CACL,IAAI;gDACF,OAAO;gDACP,gBAAgB;gDAChB,YAAY;gDACZ,WAAW;oDACT,gBAAgB;gDAClB;4CACF;sDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}