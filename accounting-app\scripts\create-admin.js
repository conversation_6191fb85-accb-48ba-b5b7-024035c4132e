/**
 * Create Admin User Script
 * 
 * This script creates a new admin user with specified credentials.
 * It can be used to add additional admin users to the system.
 * 
 * Following DRY principle: Reusable user creation script
 * Following YAGNI principle: Essential user creation only
 */

const bcrypt = require('bcryptjs');
const path = require('path');
const fs = require('fs');

// Import database functions
const getDatabase = require('../src/lib/database.ts').default;

/**
 * Generate a unique user ID
 * 
 * @returns Unique user ID string
 * 
 * Following DRY principle: Single ID generation method
 */
function generateUserId() {
  return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Create a new admin user
 * 
 * @param email - User's email address
 * @param password - User's password
 * @param name - User's display name
 * @returns Promise that resolves when user is created
 * 
 * Following backend/frontend separation: Backend user creation
 */
async function createAdminUser(email, password, name) {
  const db = getDatabase();

  try {
    console.log(`🔍 Checking if user ${email} already exists...`);
    
    // Check if user already exists
    const existingUser = db.prepare('SELECT id, email FROM users WHERE email = ?').get(email);
    
    if (existingUser) {
      console.log(`⚠️  User ${email} already exists with ID: ${existingUser.id}`);
      console.log('   No action taken.');
      return;
    }

    console.log(`🔐 Hashing password...`);
    
    // Hash the password
    const passwordHash = await bcrypt.hash(password, 10);
    
    // Generate user ID
    const userId = generateUserId();
    
    console.log(`👤 Creating user ${email}...`);
    
    // Insert admin user
    const insertUser = db.prepare(`
      INSERT INTO users (id, email, name, password_hash)
      VALUES (?, ?, ?, ?)
    `);
    
    const result = insertUser.run(userId, email, name, passwordHash);
    
    if (result.changes > 0) {
      console.log('✅ Admin user created successfully!');
      console.log('');
      console.log('📧 Login Credentials:');
      console.log(`   Email: ${email}`);
      console.log(`   Password: ${password}`);
      console.log(`   Name: ${name}`);
      console.log(`   User ID: ${userId}`);
      console.log('');
      console.log('🚀 You can now login with these credentials!');
    } else {
      throw new Error('Failed to create user - no rows affected');
    }
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
    throw error;
  }
}

/**
 * Validate email format
 * 
 * @param email - Email to validate
 * @returns True if email is valid
 * 
 * Following DRY principle: Single email validation
 */
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate password strength
 * 
 * @param password - Password to validate
 * @returns Validation result
 * 
 * Following DRY principle: Single password validation
 */
function validatePassword(password) {
  if (!password) {
    return { isValid: false, error: 'Password is required' };
  }
  
  if (password.length < 3) {
    return { isValid: false, error: 'Password must be at least 3 characters long' };
  }
  
  if (password.length > 128) {
    return { isValid: false, error: 'Password must be no more than 128 characters long' };
  }
  
  return { isValid: true };
}

/**
 * Main function to handle command line arguments and create user
 * 
 * Following YAGNI principle: Simple command line interface
 */
async function main() {
  try {
    // Get command line arguments
    const args = process.argv.slice(2);
    
    // Default values
    let email = '<EMAIL>';
    let password = '123';
    let name = 'Admin User';
    
    // Parse command line arguments
    if (args.length >= 1) email = args[0];
    if (args.length >= 2) password = args[1];
    if (args.length >= 3) name = args[2];
    
    console.log('🚀 Starting admin user creation...');
    console.log('');
    
    // Validate input
    if (!validateEmail(email)) {
      throw new Error('Invalid email format');
    }
    
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      throw new Error(passwordValidation.error);
    }
    
    if (!name || name.trim().length === 0) {
      throw new Error('Name is required');
    }
    
    // Ensure data directory exists
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
      console.log('📁 Created data directory');
    }
    
    // Create the admin user
    await createAdminUser(email.trim(), password, name.trim());
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
    console.log('');
    console.log('📖 Usage:');
    console.log('   node scripts/create-admin.js [email] [password] [name]');
    console.log('');
    console.log('📝 Examples:');
    console.log('   node scripts/create-admin.js');
    console.log('   node scripts/create-admin.js <EMAIL> 123');
    console.log('   node scripts/create-admin.js <EMAIL> 123 "Admin User"');
    console.log('   node scripts/create-admin.js <EMAIL> mypassword "John Doe"');
    
    process.exit(1);
  } finally {
    // Close database connection
    const { closeDatabase } = require('../src/lib/database.ts');
    closeDatabase();
  }
}

/**
 * Display help information
 */
function showHelp() {
  console.log('📖 Create Admin User Script');
  console.log('');
  console.log('This script creates a new admin user in the accounting application.');
  console.log('');
  console.log('📝 Usage:');
  console.log('   node scripts/create-admin.js [email] [password] [name]');
  console.log('');
  console.log('📋 Parameters:');
  console.log('   email    - User email address (default: <EMAIL>)');
  console.log('   password - User password (default: 123)');
  console.log('   name     - User display name (default: Admin User)');
  console.log('');
  console.log('📝 Examples:');
  console.log('   node scripts/create-admin.js');
  console.log('   # Creates: <EMAIL> / 123 / Admin User');
  console.log('');
  console.log('   node scripts/create-admin.js <EMAIL> 123');
  console.log('   # Creates: <EMAIL> / 123 / Admin User');
  console.log('');
  console.log('   node scripts/create-admin.js <EMAIL> 123 "Super Admin"');
  console.log('   # Creates: <EMAIL> / 123 / Super Admin');
  console.log('');
  console.log('   node scripts/create-admin.js <EMAIL> mypassword "John Doe"');
  console.log('   # Creates: <EMAIL> / mypassword / John Doe');
}

// Check for help flag
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showHelp();
  process.exit(0);
}

// Run the script if executed directly
if (require.main === module) {
  main();
}

module.exports = { createAdminUser, validateEmail, validatePassword };
