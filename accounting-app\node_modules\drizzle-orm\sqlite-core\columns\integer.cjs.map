{"version": 3, "sources": ["../../../src/sqlite-core/columns/integer.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilderRuntimeConfig,\n\tColumnDataType,\n\tHasDefault,\n\tIsP<PERSON>ry<PERSON>ey,\n\tMakeColumnConfig,\n\tNotNull,\n} from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport { sql } from '~/sql/sql.ts';\nimport type { OnConflict } from '~/sqlite-core/utils.ts';\nimport { type Equal, getColumnNameAndConfig, type Or } from '~/utils.ts';\nimport type { AnySQLiteTable } from '../table.ts';\nimport { SQLiteColumn, SQLiteColumnBuilder } from './common.ts';\n\nexport interface PrimaryKeyConfig {\n\tautoIncrement?: boolean;\n\tonConflict?: OnConflict;\n}\n\nexport abstract class SQLiteBaseIntegerBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n> extends SQLiteColumnBuilder<\n\tT,\n\tTRuntimeConfig & { autoIncrement: boolean },\n\t{},\n\t{ primaryKeyHasDefault: true }\n> {\n\tstatic override readonly [entityKind]: string = 'SQLiteBaseIntegerBuilder';\n\n\tconstructor(name: T['name'], dataType: T['dataType'], columnType: T['columnType']) {\n\t\tsuper(name, dataType, columnType);\n\t\tthis.config.autoIncrement = false;\n\t}\n\n\toverride primaryKey(config?: PrimaryKeyConfig): IsPrimaryKey<HasDefault<NotNull<this>>> {\n\t\tif (config?.autoIncrement) {\n\t\t\tthis.config.autoIncrement = true;\n\t\t}\n\t\tthis.config.hasDefault = true;\n\t\treturn super.primaryKey() as IsPrimaryKey<HasDefault<NotNull<this>>>;\n\t}\n\n\t/** @internal */\n\tabstract override build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteBaseInteger<MakeColumnConfig<T, TTableName>>;\n}\n\nexport abstract class SQLiteBaseInteger<\n\tT extends ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n> extends SQLiteColumn<T, TRuntimeConfig & { autoIncrement: boolean }> {\n\tstatic override readonly [entityKind]: string = 'SQLiteBaseInteger';\n\n\treadonly autoIncrement: boolean = this.config.autoIncrement;\n\n\tgetSQLType(): string {\n\t\treturn 'integer';\n\t}\n}\n\nexport type SQLiteIntegerBuilderInitial<TName extends string> = SQLiteIntegerBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SQLiteInteger';\n\tdata: number;\n\tdriverParam: number;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteIntegerBuilder<T extends ColumnBuilderBaseConfig<'number', 'SQLiteInteger'>>\n\textends SQLiteBaseIntegerBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteIntegerBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'SQLiteInteger');\n\t}\n\n\tbuild<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteInteger<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteInteger<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteInteger<T extends ColumnBaseConfig<'number', 'SQLiteInteger'>> extends SQLiteBaseInteger<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteInteger';\n}\n\nexport type SQLiteTimestampBuilderInitial<TName extends string> = SQLiteTimestampBuilder<{\n\tname: TName;\n\tdataType: 'date';\n\tcolumnType: 'SQLiteTimestamp';\n\tdata: Date;\n\tdriverParam: number;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteTimestampBuilder<T extends ColumnBuilderBaseConfig<'date', 'SQLiteTimestamp'>>\n\textends SQLiteBaseIntegerBuilder<T, { mode: 'timestamp' | 'timestamp_ms' }>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteTimestampBuilder';\n\n\tconstructor(name: T['name'], mode: 'timestamp' | 'timestamp_ms') {\n\t\tsuper(name, 'date', 'SQLiteTimestamp');\n\t\tthis.config.mode = mode;\n\t}\n\n\t/**\n\t * @deprecated Use `default()` with your own expression instead.\n\t *\n\t * Adds `DEFAULT (cast((julianday('now') - 2440587.5)*86400000 as integer))` to the column, which is the current epoch timestamp in milliseconds.\n\t */\n\tdefaultNow(): HasDefault<this> {\n\t\treturn this.default(sql`(cast((julianday('now') - 2440587.5)*86400000 as integer))`) as any;\n\t}\n\n\tbuild<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteTimestamp<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteTimestamp<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteTimestamp<T extends ColumnBaseConfig<'date', 'SQLiteTimestamp'>>\n\textends SQLiteBaseInteger<T, { mode: 'timestamp' | 'timestamp_ms' }>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteTimestamp';\n\n\treadonly mode: 'timestamp' | 'timestamp_ms' = this.config.mode;\n\n\toverride mapFromDriverValue(value: number): Date {\n\t\tif (this.config.mode === 'timestamp') {\n\t\t\treturn new Date(value * 1000);\n\t\t}\n\t\treturn new Date(value);\n\t}\n\n\toverride mapToDriverValue(value: Date): number {\n\t\tconst unix = value.getTime();\n\t\tif (this.config.mode === 'timestamp') {\n\t\t\treturn Math.floor(unix / 1000);\n\t\t}\n\t\treturn unix;\n\t}\n}\n\nexport type SQLiteBooleanBuilderInitial<TName extends string> = SQLiteBooleanBuilder<{\n\tname: TName;\n\tdataType: 'boolean';\n\tcolumnType: 'SQLiteBoolean';\n\tdata: boolean;\n\tdriverParam: number;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteBooleanBuilder<T extends ColumnBuilderBaseConfig<'boolean', 'SQLiteBoolean'>>\n\textends SQLiteBaseIntegerBuilder<T, { mode: 'boolean' }>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteBooleanBuilder';\n\n\tconstructor(name: T['name'], mode: 'boolean') {\n\t\tsuper(name, 'boolean', 'SQLiteBoolean');\n\t\tthis.config.mode = mode;\n\t}\n\n\tbuild<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteBoolean<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteBoolean<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteBoolean<T extends ColumnBaseConfig<'boolean', 'SQLiteBoolean'>>\n\textends SQLiteBaseInteger<T, { mode: 'boolean' }>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteBoolean';\n\n\treadonly mode: 'boolean' = this.config.mode;\n\n\toverride mapFromDriverValue(value: number): boolean {\n\t\treturn Number(value) === 1;\n\t}\n\n\toverride mapToDriverValue(value: boolean): number {\n\t\treturn value ? 1 : 0;\n\t}\n}\n\nexport interface IntegerConfig<\n\tTMode extends 'number' | 'timestamp' | 'timestamp_ms' | 'boolean' =\n\t\t| 'number'\n\t\t| 'timestamp'\n\t\t| 'timestamp_ms'\n\t\t| 'boolean',\n> {\n\tmode: TMode;\n}\n\nexport function integer(): SQLiteIntegerBuilderInitial<''>;\nexport function integer<TMode extends IntegerConfig['mode']>(\n\tconfig?: IntegerConfig<TMode>,\n): Or<Equal<TMode, 'timestamp'>, Equal<TMode, 'timestamp_ms'>> extends true ? SQLiteTimestampBuilderInitial<''>\n\t: Equal<TMode, 'boolean'> extends true ? SQLiteBooleanBuilderInitial<''>\n\t: SQLiteIntegerBuilderInitial<''>;\nexport function integer<TName extends string, TMode extends IntegerConfig['mode']>(\n\tname: TName,\n\tconfig?: IntegerConfig<TMode>,\n): Or<Equal<TMode, 'timestamp'>, Equal<TMode, 'timestamp_ms'>> extends true ? SQLiteTimestampBuilderInitial<TName>\n\t: Equal<TMode, 'boolean'> extends true ? SQLiteBooleanBuilderInitial<TName>\n\t: SQLiteIntegerBuilderInitial<TName>;\nexport function integer(a?: string | IntegerConfig, b?: IntegerConfig) {\n\tconst { name, config } = getColumnNameAndConfig<IntegerConfig | undefined>(a, b);\n\tif (config?.mode === 'timestamp' || config?.mode === 'timestamp_ms') {\n\t\treturn new SQLiteTimestampBuilder(name, config.mode);\n\t}\n\tif (config?.mode === 'boolean') {\n\t\treturn new SQLiteBooleanBuilder(name, config.mode);\n\t}\n\treturn new SQLiteIntegerBuilder(name);\n}\n\nexport const int = integer;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,oBAA2B;AAC3B,iBAAoB;AAEpB,mBAA4D;AAE5D,oBAAkD;AAO3C,MAAe,iCAGZ,kCAKR;AAAA,EACD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,UAAyB,YAA6B;AAClF,UAAM,MAAM,UAAU,UAAU;AAChC,SAAK,OAAO,gBAAgB;AAAA,EAC7B;AAAA,EAES,WAAW,QAAoE;AACvF,QAAI,QAAQ,eAAe;AAC1B,WAAK,OAAO,gBAAgB;AAAA,IAC7B;AACA,SAAK,OAAO,aAAa;AACzB,WAAO,MAAM,WAAW;AAAA,EACzB;AAMD;AAEO,MAAe,0BAGZ,2BAA6D;AAAA,EACtE,QAA0B,wBAAU,IAAY;AAAA,EAEvC,gBAAyB,KAAK,OAAO;AAAA,EAE9C,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAWO,MAAM,6BACJ,yBACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,eAAe;AAAA,EACtC;AAAA,EAEA,MACC,OACiD;AACjD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,sBAA6E,kBAAqB;AAAA,EAC9G,QAA0B,wBAAU,IAAY;AACjD;AAWO,MAAM,+BACJ,yBACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,MAAoC;AAChE,UAAM,MAAM,QAAQ,iBAAiB;AACrC,SAAK,OAAO,OAAO;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAA+B;AAC9B,WAAO,KAAK,QAAQ,0EAA+D;AAAA,EACpF;AAAA,EAEA,MACC,OACmD;AACnD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,wBACJ,kBACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,OAAqC,KAAK,OAAO;AAAA,EAEjD,mBAAmB,OAAqB;AAChD,QAAI,KAAK,OAAO,SAAS,aAAa;AACrC,aAAO,IAAI,KAAK,QAAQ,GAAI;AAAA,IAC7B;AACA,WAAO,IAAI,KAAK,KAAK;AAAA,EACtB;AAAA,EAES,iBAAiB,OAAqB;AAC9C,UAAM,OAAO,MAAM,QAAQ;AAC3B,QAAI,KAAK,OAAO,SAAS,aAAa;AACrC,aAAO,KAAK,MAAM,OAAO,GAAI;AAAA,IAC9B;AACA,WAAO;AAAA,EACR;AACD;AAWO,MAAM,6BACJ,yBACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,MAAiB;AAC7C,UAAM,MAAM,WAAW,eAAe;AACtC,SAAK,OAAO,OAAO;AAAA,EACpB;AAAA,EAEA,MACC,OACiD;AACjD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,sBACJ,kBACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,OAAkB,KAAK,OAAO;AAAA,EAE9B,mBAAmB,OAAwB;AACnD,WAAO,OAAO,KAAK,MAAM;AAAA,EAC1B;AAAA,EAES,iBAAiB,OAAwB;AACjD,WAAO,QAAQ,IAAI;AAAA,EACpB;AACD;AAwBO,SAAS,QAAQ,GAA4B,GAAmB;AACtE,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAkD,GAAG,CAAC;AAC/E,MAAI,QAAQ,SAAS,eAAe,QAAQ,SAAS,gBAAgB;AACpE,WAAO,IAAI,uBAAuB,MAAM,OAAO,IAAI;AAAA,EACpD;AACA,MAAI,QAAQ,SAAS,WAAW;AAC/B,WAAO,IAAI,qBAAqB,MAAM,OAAO,IAAI;AAAA,EAClD;AACA,SAAO,IAAI,qBAAqB,IAAI;AACrC;AAEO,MAAM,MAAM;", "names": []}