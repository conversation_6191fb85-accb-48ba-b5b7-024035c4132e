{"version": 3, "sources": ["../../src/sqlite-core/session.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { <PERSON><PERSON>zleError, TransactionRollbackError } from '~/errors.ts';\nimport type { TablesRelationalConfig } from '~/relations.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport type { Query, SQL } from '~/sql/sql.ts';\nimport type { SQLiteAsyncDialect, SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\n// import { QueryPromise } from '../index.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport { BaseSQLiteDatabase } from './db.ts';\nimport type { SQLiteRaw } from './query-builders/raw.ts';\nimport type { SelectedFieldsOrdered } from './query-builders/select.types.ts';\n\nexport interface PreparedQueryConfig {\n\ttype: 'sync' | 'async';\n\trun: unknown;\n\tall: unknown;\n\tget: unknown;\n\tvalues: unknown;\n\texecute: unknown;\n}\n\nexport class ExecuteResultSync<T> extends QueryPromise<T> {\n\tstatic override readonly [entityKind]: string = 'ExecuteResultSync';\n\n\tconstructor(private resultCb: () => T) {\n\t\tsuper();\n\t}\n\n\toverride async execute(): Promise<T> {\n\t\treturn this.resultCb();\n\t}\n\n\tsync(): T {\n\t\treturn this.resultCb();\n\t}\n}\n\nexport type ExecuteResult<TType extends 'sync' | 'async', TResult> = TType extends 'async' ? Promise<TResult>\n\t: ExecuteResultSync<TResult>;\n\nexport abstract class SQLitePreparedQuery<T extends PreparedQueryConfig> implements PreparedQuery {\n\tstatic readonly [entityKind]: string = 'PreparedQuery';\n\n\t/** @internal */\n\tjoinsNotNullableMap?: Record<string, boolean>;\n\n\tconstructor(\n\t\tprivate mode: 'sync' | 'async',\n\t\tprivate executeMethod: SQLiteExecuteMethod,\n\t\tprotected query: Query,\n\t) {}\n\n\tgetQuery(): Query {\n\t\treturn this.query;\n\t}\n\n\tabstract run(placeholderValues?: Record<string, unknown>): Result<T['type'], T['run']>;\n\n\tmapRunResult(result: unknown, _isFromBatch?: boolean): unknown {\n\t\treturn result;\n\t}\n\n\tabstract all(placeholderValues?: Record<string, unknown>): Result<T['type'], T['all']>;\n\n\tmapAllResult(_result: unknown, _isFromBatch?: boolean): unknown {\n\t\tthrow new Error('Not implemented');\n\t}\n\n\tabstract get(placeholderValues?: Record<string, unknown>): Result<T['type'], T['get']>;\n\n\tmapGetResult(_result: unknown, _isFromBatch?: boolean): unknown {\n\t\tthrow new Error('Not implemented');\n\t}\n\n\tabstract values(placeholderValues?: Record<string, unknown>): Result<T['type'], T['values']>;\n\n\texecute(placeholderValues?: Record<string, unknown>): ExecuteResult<T['type'], T['execute']> {\n\t\tif (this.mode === 'async') {\n\t\t\treturn this[this.executeMethod](placeholderValues) as ExecuteResult<T['type'], T['execute']>;\n\t\t}\n\t\treturn new ExecuteResultSync(() => this[this.executeMethod](placeholderValues));\n\t}\n\n\tmapResult(response: unknown, isFromBatch?: boolean) {\n\t\tswitch (this.executeMethod) {\n\t\t\tcase 'run': {\n\t\t\t\treturn this.mapRunResult(response, isFromBatch);\n\t\t\t}\n\t\t\tcase 'all': {\n\t\t\t\treturn this.mapAllResult(response, isFromBatch);\n\t\t\t}\n\t\t\tcase 'get': {\n\t\t\t\treturn this.mapGetResult(response, isFromBatch);\n\t\t\t}\n\t\t}\n\t}\n\n\t/** @internal */\n\tabstract isResponseInArrayMode(): boolean;\n}\n\nexport interface SQLiteTransactionConfig {\n\tbehavior?: 'deferred' | 'immediate' | 'exclusive';\n}\n\nexport type SQLiteExecuteMethod = 'run' | 'all' | 'get';\n\nexport abstract class SQLiteSession<\n\tTResultKind extends 'sync' | 'async',\n\tTRunResult,\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> {\n\tstatic readonly [entityKind]: string = 'SQLiteSession';\n\n\tconstructor(\n\t\t/** @internal */\n\t\treadonly dialect: { sync: SQLiteSyncDialect; async: SQLiteAsyncDialect }[TResultKind],\n\t) {}\n\n\tabstract prepareQuery(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][], mapColumnValue?: (value: unknown) => unknown) => unknown,\n\t): SQLitePreparedQuery<PreparedQueryConfig & { type: TResultKind }>;\n\n\tprepareOneTimeQuery(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tisResponseInArrayMode: boolean,\n\t): SQLitePreparedQuery<PreparedQueryConfig & { type: TResultKind }> {\n\t\treturn this.prepareQuery(query, fields, executeMethod, isResponseInArrayMode);\n\t}\n\n\tabstract transaction<T>(\n\t\ttransaction: (tx: SQLiteTransaction<TResultKind, TRunResult, TFullSchema, TSchema>) => Result<TResultKind, T>,\n\t\tconfig?: SQLiteTransactionConfig,\n\t): Result<TResultKind, T>;\n\n\trun(query: SQL): Result<TResultKind, TRunResult> {\n\t\tconst staticQuery = this.dialect.sqlToQuery(query);\n\t\ttry {\n\t\t\treturn this.prepareOneTimeQuery(staticQuery, undefined, 'run', false).run() as Result<TResultKind, TRunResult>;\n\t\t} catch (err) {\n\t\t\tthrow new DrizzleError({ cause: err, message: `Failed to run the query '${staticQuery.sql}'` });\n\t\t}\n\t}\n\n\t/** @internal */\n\textractRawRunValueFromBatchResult(result: unknown) {\n\t\treturn result;\n\t}\n\n\tall<T = unknown>(query: SQL): Result<TResultKind, T[]> {\n\t\treturn this.prepareOneTimeQuery(this.dialect.sqlToQuery(query), undefined, 'run', false).all() as Result<\n\t\t\tTResultKind,\n\t\t\tT[]\n\t\t>;\n\t}\n\n\t/** @internal */\n\textractRawAllValueFromBatchResult(_result: unknown): unknown {\n\t\tthrow new Error('Not implemented');\n\t}\n\n\tget<T = unknown>(query: SQL): Result<TResultKind, T> {\n\t\treturn this.prepareOneTimeQuery(this.dialect.sqlToQuery(query), undefined, 'run', false).get() as Result<\n\t\t\tTResultKind,\n\t\t\tT\n\t\t>;\n\t}\n\n\t/** @internal */\n\textractRawGetValueFromBatchResult(_result: unknown): unknown {\n\t\tthrow new Error('Not implemented');\n\t}\n\n\tvalues<T extends any[] = unknown[]>(\n\t\tquery: SQL,\n\t): Result<TResultKind, T[]> {\n\t\treturn this.prepareOneTimeQuery(this.dialect.sqlToQuery(query), undefined, 'run', false).values() as Result<\n\t\t\tTResultKind,\n\t\t\tT[]\n\t\t>;\n\t}\n\n\tasync count(sql: SQL) {\n\t\tconst result = await this.values(sql) as [[number]];\n\n\t\treturn result[0][0];\n\t}\n\n\t/** @internal */\n\textractRawValuesValueFromBatchResult(_result: unknown): unknown {\n\t\tthrow new Error('Not implemented');\n\t}\n}\n\nexport type Result<TKind extends 'sync' | 'async', TResult> = { sync: TResult; async: Promise<TResult> }[TKind];\n\nexport type DBResult<TKind extends 'sync' | 'async', TResult> = { sync: TResult; async: SQLiteRaw<TResult> }[TKind];\n\nexport abstract class SQLiteTransaction<\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends BaseSQLiteDatabase<TResultType, TRunResult, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'SQLiteTransaction';\n\n\tconstructor(\n\t\tresultType: TResultType,\n\t\tdialect: { sync: SQLiteSyncDialect; async: SQLiteAsyncDialect }[TResultType],\n\t\tsession: SQLiteSession<TResultType, TRunResult, TFullSchema, TSchema>,\n\t\tprotected schema: {\n\t\t\tfullSchema: Record<string, unknown>;\n\t\t\tschema: TSchema;\n\t\t\ttableNamesMap: Record<string, string>;\n\t\t} | undefined,\n\t\tprotected readonly nestedIndex = 0,\n\t) {\n\t\tsuper(resultType, dialect, session, schema);\n\t}\n\n\trollback(): never {\n\t\tthrow new TransactionRollbackError();\n\t}\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAC3B,SAAS,cAAc,gCAAgC;AAMvD,SAAS,oBAAoB;AAC7B,SAAS,0BAA0B;AAa5B,MAAM,0BAA6B,aAAgB;AAAA,EAGzD,YAAoB,UAAmB;AACtC,UAAM;AADa;AAAA,EAEpB;AAAA,EAJA,QAA0B,UAAU,IAAY;AAAA,EAMhD,MAAe,UAAsB;AACpC,WAAO,KAAK,SAAS;AAAA,EACtB;AAAA,EAEA,OAAU;AACT,WAAO,KAAK,SAAS;AAAA,EACtB;AACD;AAKO,MAAe,oBAA4E;AAAA,EAMjG,YACS,MACA,eACE,OACT;AAHO;AACA;AACE;AAAA,EACR;AAAA,EATH,QAAiB,UAAU,IAAY;AAAA;AAAA,EAGvC;AAAA,EAQA,WAAkB;AACjB,WAAO,KAAK;AAAA,EACb;AAAA,EAIA,aAAa,QAAiB,cAAiC;AAC9D,WAAO;AAAA,EACR;AAAA,EAIA,aAAa,SAAkB,cAAiC;AAC/D,UAAM,IAAI,MAAM,iBAAiB;AAAA,EAClC;AAAA,EAIA,aAAa,SAAkB,cAAiC;AAC/D,UAAM,IAAI,MAAM,iBAAiB;AAAA,EAClC;AAAA,EAIA,QAAQ,mBAAqF;AAC5F,QAAI,KAAK,SAAS,SAAS;AAC1B,aAAO,KAAK,KAAK,aAAa,EAAE,iBAAiB;AAAA,IAClD;AACA,WAAO,IAAI,kBAAkB,MAAM,KAAK,KAAK,aAAa,EAAE,iBAAiB,CAAC;AAAA,EAC/E;AAAA,EAEA,UAAU,UAAmB,aAAuB;AACnD,YAAQ,KAAK,eAAe;AAAA,MAC3B,KAAK,OAAO;AACX,eAAO,KAAK,aAAa,UAAU,WAAW;AAAA,MAC/C;AAAA,MACA,KAAK,OAAO;AACX,eAAO,KAAK,aAAa,UAAU,WAAW;AAAA,MAC/C;AAAA,MACA,KAAK,OAAO;AACX,eAAO,KAAK,aAAa,UAAU,WAAW;AAAA,MAC/C;AAAA,IACD;AAAA,EACD;AAID;AAQO,MAAe,cAKpB;AAAA,EAGD,YAEU,SACR;AADQ;AAAA,EACP;AAAA,EALH,QAAiB,UAAU,IAAY;AAAA,EAevC,oBACC,OACA,QACA,eACA,uBACmE;AACnE,WAAO,KAAK,aAAa,OAAO,QAAQ,eAAe,qBAAqB;AAAA,EAC7E;AAAA,EAOA,IAAI,OAA6C;AAChD,UAAM,cAAc,KAAK,QAAQ,WAAW,KAAK;AACjD,QAAI;AACH,aAAO,KAAK,oBAAoB,aAAa,QAAW,OAAO,KAAK,EAAE,IAAI;AAAA,IAC3E,SAAS,KAAK;AACb,YAAM,IAAI,aAAa,EAAE,OAAO,KAAK,SAAS,4BAA4B,YAAY,GAAG,IAAI,CAAC;AAAA,IAC/F;AAAA,EACD;AAAA;AAAA,EAGA,kCAAkC,QAAiB;AAClD,WAAO;AAAA,EACR;AAAA,EAEA,IAAiB,OAAsC;AACtD,WAAO,KAAK,oBAAoB,KAAK,QAAQ,WAAW,KAAK,GAAG,QAAW,OAAO,KAAK,EAAE,IAAI;AAAA,EAI9F;AAAA;AAAA,EAGA,kCAAkC,SAA2B;AAC5D,UAAM,IAAI,MAAM,iBAAiB;AAAA,EAClC;AAAA,EAEA,IAAiB,OAAoC;AACpD,WAAO,KAAK,oBAAoB,KAAK,QAAQ,WAAW,KAAK,GAAG,QAAW,OAAO,KAAK,EAAE,IAAI;AAAA,EAI9F;AAAA;AAAA,EAGA,kCAAkC,SAA2B;AAC5D,UAAM,IAAI,MAAM,iBAAiB;AAAA,EAClC;AAAA,EAEA,OACC,OAC2B;AAC3B,WAAO,KAAK,oBAAoB,KAAK,QAAQ,WAAW,KAAK,GAAG,QAAW,OAAO,KAAK,EAAE,OAAO;AAAA,EAIjG;AAAA,EAEA,MAAM,MAAM,KAAU;AACrB,UAAM,SAAS,MAAM,KAAK,OAAO,GAAG;AAEpC,WAAO,OAAO,CAAC,EAAE,CAAC;AAAA,EACnB;AAAA;AAAA,EAGA,qCAAqC,SAA2B;AAC/D,UAAM,IAAI,MAAM,iBAAiB;AAAA,EAClC;AACD;AAMO,MAAe,0BAKZ,mBAAkE;AAAA,EAG3E,YACC,YACA,SACA,SACU,QAKS,cAAc,GAChC;AACD,UAAM,YAAY,SAAS,SAAS,MAAM;AAPhC;AAKS;AAAA,EAGpB;AAAA,EAdA,QAA0B,UAAU,IAAY;AAAA,EAgBhD,WAAkB;AACjB,UAAM,IAAI,yBAAyB;AAAA,EACpC;AACD;", "names": []}