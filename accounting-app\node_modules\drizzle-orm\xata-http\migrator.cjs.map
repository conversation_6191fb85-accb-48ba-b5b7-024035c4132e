{"version": 3, "sources": ["../../src/xata-http/migrator.ts"], "sourcesContent": ["import { readMigrationFiles } from '~/migrator.ts';\nimport { sql } from '~/sql/sql.ts';\nimport type { XataHttpDatabase } from './driver.ts';\n\nexport interface MigrationConfig {\n\tmigrationsFolder: string;\n\tmigrationsTable?: string;\n}\n\n/**\n * This function reads migrationFolder and execute each unapplied migration and mark it as executed in database\n *\n * NOTE: The Xata HTTP driver does not support transactions. This means that if any part of a migration fails,\n * no rollback will be executed. Currently, you will need to handle unsuccessful migration yourself.\n * @param db - drizzle db instance\n * @param config - path to migration folder generated by drizzle-kit\n */ export async function migrate<TSchema extends Record<string, unknown>>(\n\tdb: XataHttpDatabase<TSchema>,\n\tconfig: MigrationConfig,\n) {\n\tconst migrations = readMigrationFiles(config);\n\tconst migrationsTable = config.migrationsTable ?? '__drizzle_migrations';\n\tconst migrationTableCreate = sql`\n\t\tCREATE TABLE IF NOT EXISTS ${sql.identifier(migrationsTable)} (\n\t\t\tid SERIAL PRIMARY KEY,\n\t\t\thash text NOT NULL,\n\t\t\tcreated_at bigint\n\t\t)\n\t`;\n\tawait db.session.execute(migrationTableCreate);\n\n\tconst dbMigrations = await db.session.all<{\n\t\tid: number;\n\t\thash: string;\n\t\tcreated_at: string;\n\t}>(\n\t\tsql`select id, hash, created_at from ${sql.identifier(migrationsTable)} order by created_at desc limit 1`,\n\t);\n\n\tconst lastDbMigration = dbMigrations[0];\n\n\tfor await (const migration of migrations) {\n\t\tif (!lastDbMigration || Number(lastDbMigration.created_at) < migration.folderMillis) {\n\t\t\tfor (const stmt of migration.sql) {\n\t\t\t\tawait db.session.execute(sql.raw(stmt));\n\t\t\t}\n\n\t\t\tawait db.session.execute(\n\t\t\t\tsql`insert into ${\n\t\t\t\t\tsql.identifier(migrationsTable)\n\t\t\t\t} (\"hash\", \"created_at\") values(${migration.hash}, ${migration.folderMillis})`,\n\t\t\t);\n\t\t}\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAAmC;AACnC,iBAAoB;AAehB,eAAsB,QACzB,IACA,QACC;AACD,QAAM,iBAAa,oCAAmB,MAAM;AAC5C,QAAM,kBAAkB,OAAO,mBAAmB;AAClD,QAAM,uBAAuB;AAAA,+BACC,eAAI,WAAW,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAM7D,QAAM,GAAG,QAAQ,QAAQ,oBAAoB;AAE7C,QAAM,eAAe,MAAM,GAAG,QAAQ;AAAA,IAKrC,kDAAuC,eAAI,WAAW,eAAe,CAAC;AAAA,EACvE;AAEA,QAAM,kBAAkB,aAAa,CAAC;AAEtC,mBAAiB,aAAa,YAAY;AACzC,QAAI,CAAC,mBAAmB,OAAO,gBAAgB,UAAU,IAAI,UAAU,cAAc;AACpF,iBAAW,QAAQ,UAAU,KAAK;AACjC,cAAM,GAAG,QAAQ,QAAQ,eAAI,IAAI,IAAI,CAAC;AAAA,MACvC;AAEA,YAAM,GAAG,QAAQ;AAAA,QAChB,6BACC,eAAI,WAAW,eAAe,CAC/B,kCAAkC,UAAU,IAAI,KAAK,UAAU,YAAY;AAAA,MAC5E;AAAA,IACD;AAAA,EACD;AACD;", "names": []}