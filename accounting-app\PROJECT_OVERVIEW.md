# Accounting Application - Project Overview

## 🎯 Project Vision
A comprehensive, enterprise-grade accounting application with Odoo-like features, built with modern web technologies and following best practices for scalability, security, and maintainability.

## 🏗️ Architecture Principles

### Core Principles
- **DRY (Don't Repeat Yourself)**: Single source of truth for all functionality
- **YAGNI (You Aren't Gonna Need It)**: Only implement essential features
- **Backend/Frontend Separation**: Clear API boundaries and modular architecture
- **Database Agnostic**: Support for SQLite, MySQL, and PostgreSQL
- **Type Safety**: Comprehensive TypeScript coverage throughout

### Design Patterns
- **Repository Pattern**: Database operations abstraction
- **Service Layer**: Business logic separation
- **Factory Pattern**: Centralized instance creation
- **Singleton Pattern**: Database connections and services
- **Observer Pattern**: Real-time updates and notifications

## 🛠️ Technology Stack

### Frontend
- **Framework**: Next.js 15 with App Router
- **UI Library**: Material-UI (MUI) Pro with AdminLTE-style components
- **Language**: TypeScript
- **State Management**: React Context API + Custom Hooks
- **Styling**: MUI Theme System with custom AdminLTE design
- **Authentication**: JWT with HTTP-only cookies

### Backend
- **API**: Next.js API Routes
- **Database**: Database-agnostic with Drizzle ORM
- **Supported DBs**: SQLite (default), MySQL, PostgreSQL
- **Authentication**: JWT tokens with bcrypt password hashing
- **Validation**: Comprehensive input validation and sanitization

### Development Tools
- **Package Manager**: npm
- **Code Quality**: ESLint, TypeScript compiler
- **Database Migrations**: Custom migration system
- **Testing**: Jest + React Testing Library (planned)

## 📊 Database Schema

### Hierarchical Structure
```
Users (Authentication & Profile)
├── Companies (Business Entities)
    ├── Financial Years (Accounting Periods)
        ├── Accounts (Chart of Accounts)
        └── Journal Entries (Transactions)
            └── Journal Entry Lines (Debit/Credit Lines)
```

### Core Entities
1. **Users**: Authentication and user management
2. **Companies**: Multi-company support
3. **Financial Years**: Accounting periods per company
4. **Account Types**: Predefined accounting categories
5. **Accounts**: Chart of accounts with hierarchy
6. **Journal Entries**: Accounting transactions
7. **Journal Entry Lines**: Individual debit/credit entries

## 🔐 Security Features

### Authentication & Authorization
- **JWT Tokens**: Secure, stateless authentication
- **HTTP-only Cookies**: XSS protection
- **Password Hashing**: bcrypt with salt rounds
- **Session Management**: Automatic token refresh
- **Role-based Access**: User permissions and company access

### Data Protection
- **Input Validation**: Comprehensive server-side validation
- **SQL Injection Prevention**: Parameterized queries via ORM
- **XSS Protection**: Input sanitization and output encoding
- **CSRF Protection**: Token-based request validation
- **Rate Limiting**: API endpoint protection (planned)

## 🎨 UI/UX Design

### AdminLTE-Style Interface
- **Sidebar Navigation**: Collapsible menu with icons
- **Dashboard Cards**: Statistics and quick actions
- **Data Tables**: Sortable, filterable, paginated lists
- **Forms**: Comprehensive validation and error handling
- **Modals**: Overlay dialogs for actions
- **Responsive Design**: Mobile-first approach

### MUI Pro Components
- **DataGrid**: Advanced table with sorting, filtering, pagination
- **DatePicker**: Professional date selection
- **Charts**: Data visualization components
- **TreeView**: Hierarchical data display
- **Autocomplete**: Smart search and selection

## 📋 Odoo-like Features

### Data Management
- **Pagination**: Server-side pagination with configurable page sizes
- **Filtering**: Advanced filtering with multiple operators
- **Sorting**: Multi-column sorting capabilities
- **Search**: Full-text search across multiple fields
- **Bulk Operations**: Mass create, update, delete operations

### User Experience
- **Server-side Rendering**: Fast initial page loads
- **Real-time Updates**: Live data synchronization
- **Keyboard Shortcuts**: Power user efficiency
- **Export/Import**: Data exchange capabilities
- **Audit Trail**: Change tracking and history

## 🗂️ Project Structure

```
accounting-app/
├── src/
│   ├── app/                          # Next.js App Router
│   │   ├── api/                      # Backend API Routes
│   │   │   ├── auth/                 # Authentication endpoints
│   │   │   ├── companies/            # Company management
│   │   │   └── users/                # User management
│   │   ├── auth/                     # Frontend auth pages
│   │   ├── dashboard/                # Main dashboard
│   │   ├── companies/                # Company management pages
│   │   └── layout.tsx                # Root layout with providers
│   ├── components/                   # Reusable UI components
│   │   ├── layout/                   # AdminLTE-style layout
│   │   ├── forms/                    # Form components
│   │   ├── tables/                   # Data table components
│   │   └── charts/                   # Visualization components
│   ├── database/                     # Database layer
│   │   ├── schemas/                  # Drizzle ORM schemas
│   │   ├── repository/               # Repository pattern
│   │   ├── migrations.ts             # Migration system
│   │   ├── connection.ts             # Database connections
│   │   └── config.ts                 # Database configuration
│   ├── services/                     # Service layer
│   │   ├── frontend/                 # Frontend services
│   │   ├── userService.ts            # User operations
│   │   ├── companyService.ts         # Company operations
│   │   └── authService.ts            # Authentication logic
│   ├── lib/                          # Core utilities
│   │   ├── auth.ts                   # JWT utilities
│   │   └── validation.ts             # Input validation
│   ├── middleware/                   # API middleware
│   │   └── auth.ts                   # Authentication middleware
│   ├── hooks/                        # Custom React hooks
│   │   ├── useAuth.ts                # Authentication hook
│   │   ├── useApi.ts                 # API request hook
│   │   └── usePagination.ts          # Pagination hook
│   ├── types/                        # TypeScript definitions
│   │   └── index.ts                  # All type definitions
│   └── constants/                    # Application constants
│       └── index.ts                  # Configuration constants
├── scripts/                          # Build and setup scripts
│   ├── init-database-new.js         # Database initialization
│   └── create-admin-new.js          # Admin user creation
└── data/                            # SQLite database files
```

## 🚀 Getting Started

### Quick Setup
```bash
# Clone and setup
git clone <repository-url>
cd accounting-app
npm run setup

# Start development
npm run dev
```

### Manual Setup
```bash
# Install dependencies
npm install

# Initialize database
npm run init-db

# Create admin user
npm run create-admin

# Start development server
npm run dev
```

### Default Credentials
- **Email**: <EMAIL>
- **Password**: 123456

## 🔧 Available Scripts

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run start           # Start production server

# Database
npm run init-db         # Initialize database schema
npm run create-admin    # Create admin user

# Setup
npm run setup           # Full setup (install + init-db)
npm run clean           # Clean build files and database

# Code Quality
npm run lint            # Run ESLint
npm run type-check      # Run TypeScript compiler
```

## 🌐 Database Support

### Environment Configuration
```bash
# SQLite (default)
DATABASE_TYPE=sqlite
DATABASE_URL=./data/accounting.db

# MySQL
DATABASE_TYPE=mysql
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=accounting
DATABASE_USER=root
DATABASE_PASSWORD=password

# PostgreSQL
DATABASE_TYPE=postgresql
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=accounting
DATABASE_USER=postgres
DATABASE_PASSWORD=password
```

## 📈 Roadmap

### Phase 1: Foundation ✅
- [x] Database-agnostic ORM setup
- [x] Authentication system
- [x] Basic CRUD operations
- [x] Repository pattern implementation

### Phase 2: Core Features (In Progress)
- [ ] Beautiful MUI Pro components
- [ ] AdminLTE-style dashboard
- [ ] Company management
- [ ] Financial year management
- [ ] Chart of accounts

### Phase 3: Advanced Features
- [ ] Journal entries and transactions
- [ ] Financial reports
- [ ] Data export/import
- [ ] Advanced filtering and search
- [ ] Real-time updates

### Phase 4: Enterprise Features
- [ ] Multi-tenancy
- [ ] API rate limiting
- [ ] Audit logging
- [ ] Advanced security features
- [ ] Performance optimization

## 🤝 Contributing

### Code Standards
- Follow TypeScript strict mode
- Use ESLint configuration
- Write comprehensive JSDoc comments
- Follow DRY and YAGNI principles
- Maintain 100% type safety

### Database Changes
- Always use migration system
- Test with all supported databases
- Maintain backward compatibility
- Document schema changes

### Security Guidelines
- Never store passwords in plain text
- Always validate user input
- Use parameterized queries
- Implement proper error handling
- Follow OWASP security guidelines
