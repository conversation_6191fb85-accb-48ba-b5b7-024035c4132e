{"name": "accounting-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "init-db": "node scripts/init-database.js", "create-admin": "node scripts/create-admin.js", "setup": "npm install && npm run init-db", "clean": "rm -rf .next data/*.db", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/lab": "^7.0.0-beta.12", "@mui/material": "^7.1.0", "@mui/system": "^7.1.0", "@mui/x-data-grid-pro": "^8.4.0", "@mui/x-date-pickers-pro": "^8.4.0", "@mui/x-tree-view-pro": "^8.4.0", "@supabase/supabase-js": "^2.49.8", "bcryptjs": "^3.0.2", "better-sqlite3": "^11.10.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.43.1", "jsonwebtoken": "^9.0.2", "next": "15.3.2", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "sqlite3": "^5.1.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}