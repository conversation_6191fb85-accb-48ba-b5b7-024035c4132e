[{"name": "hot-reloader", "duration": 143, "timestamp": 12468903400, "id": 3, "tags": {"version": "15.3.2"}, "startTime": 1748206060013, "traceId": "036348484bf08711"}, {"name": "setup-dev-bundler", "duration": 912577, "timestamp": 12468522652, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748206059633, "traceId": "036348484bf08711"}, {"name": "run-instrumentation-hook", "duration": 39, "timestamp": 12469501589, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748206060611, "traceId": "036348484bf08711"}, {"name": "start-dev-server", "duration": 1805783, "timestamp": 12467731820, "id": 1, "tags": {"cpus": "8", "platform": "win32", "memory.freeMem": "26515054592", "memory.totalMem": "38427865088", "memory.heapSizeLimit": "19263389696", "memory.rss": "187965440", "memory.heapTotal": "102531072", "memory.heapUsed": "69626816"}, "startTime": 1748206058842, "traceId": "036348484bf08711"}, {"name": "compile-path", "duration": 3441422, "timestamp": 12494099478, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748206085209, "traceId": "036348484bf08711"}, {"name": "ensure-page", "duration": 3443488, "timestamp": 12494098471, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748206085208, "traceId": "036348484bf08711"}]