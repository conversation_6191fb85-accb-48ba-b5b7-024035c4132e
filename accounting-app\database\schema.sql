-- Accounting App Database Schema
-- This file contains the SQL schema for the accounting application

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Companies table
CREATE TABLE public.companies (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Financial Years table
CREATE TABLE public.financial_years (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure only one active financial year per company
    CONSTRAINT unique_active_year_per_company UNIQUE (company_id, is_active) DEFERRABLE INITIALLY DEFERRED
);

-- Account Types (Chart of Accounts categories)
CREATE TABLE public.account_types (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE')),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chart of Accounts
CREATE TABLE public.accounts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    code TEXT NOT NULL,
    name TEXT NOT NULL,
    account_type_id UUID REFERENCES public.account_types(id) NOT NULL,
    parent_account_id UUID REFERENCES public.accounts(id),
    is_active BOOLEAN DEFAULT TRUE,
    company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE NOT NULL,
    financial_year_id UUID REFERENCES public.financial_years(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique account codes within a company and financial year
    CONSTRAINT unique_account_code_per_company_year UNIQUE (company_id, financial_year_id, code)
);

-- Journal Entries
CREATE TABLE public.journal_entries (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    entry_number TEXT NOT NULL,
    date DATE NOT NULL,
    description TEXT NOT NULL,
    reference TEXT,
    total_debit DECIMAL(15,2) NOT NULL DEFAULT 0,
    total_credit DECIMAL(15,2) NOT NULL DEFAULT 0,
    status TEXT NOT NULL DEFAULT 'DRAFT' CHECK (status IN ('DRAFT', 'POSTED', 'REVERSED')),
    company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE NOT NULL,
    financial_year_id UUID REFERENCES public.financial_years(id) ON DELETE CASCADE NOT NULL,
    created_by UUID REFERENCES public.users(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure balanced entries
    CONSTRAINT balanced_entry CHECK (total_debit = total_credit),
    -- Ensure unique entry numbers within a company and financial year
    CONSTRAINT unique_entry_number_per_company_year UNIQUE (company_id, financial_year_id, entry_number)
);

-- Journal Entry Lines (Individual debit/credit lines)
CREATE TABLE public.journal_entry_lines (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    journal_entry_id UUID REFERENCES public.journal_entries(id) ON DELETE CASCADE NOT NULL,
    account_id UUID REFERENCES public.accounts(id) NOT NULL,
    description TEXT,
    debit_amount DECIMAL(15,2) DEFAULT 0,
    credit_amount DECIMAL(15,2) DEFAULT 0,
    line_number INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure either debit or credit, not both
    CONSTRAINT debit_or_credit_not_both CHECK (
        (debit_amount > 0 AND credit_amount = 0) OR 
        (credit_amount > 0 AND debit_amount = 0)
    ),
    -- Ensure unique line numbers within a journal entry
    CONSTRAINT unique_line_number_per_entry UNIQUE (journal_entry_id, line_number)
);

-- Insert default account types
INSERT INTO public.account_types (name, category, description) VALUES
('Current Assets', 'ASSET', 'Assets that can be converted to cash within one year'),
('Fixed Assets', 'ASSET', 'Long-term assets used in business operations'),
('Current Liabilities', 'LIABILITY', 'Debts due within one year'),
('Long-term Liabilities', 'LIABILITY', 'Debts due after one year'),
('Owner''s Equity', 'EQUITY', 'Owner''s stake in the business'),
('Revenue', 'REVENUE', 'Income from business operations'),
('Operating Expenses', 'EXPENSE', 'Costs of running the business'),
('Other Income', 'REVENUE', 'Non-operating income'),
('Other Expenses', 'EXPENSE', 'Non-operating expenses');

-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.financial_years ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.journal_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.journal_entry_lines ENABLE ROW LEVEL SECURITY;

-- Users can only see their own profile
CREATE POLICY "Users can view own profile" ON public.users
    FOR ALL USING (auth.uid() = id);

-- Users can only see their own companies
CREATE POLICY "Users can manage own companies" ON public.companies
    FOR ALL USING (auth.uid() = user_id);

-- Users can only see financial years of their companies
CREATE POLICY "Users can manage financial years of own companies" ON public.financial_years
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.companies 
            WHERE companies.id = financial_years.company_id 
            AND companies.user_id = auth.uid()
        )
    );

-- Users can only see accounts of their companies
CREATE POLICY "Users can manage accounts of own companies" ON public.accounts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.companies 
            WHERE companies.id = accounts.company_id 
            AND companies.user_id = auth.uid()
        )
    );

-- Users can only see journal entries of their companies
CREATE POLICY "Users can manage journal entries of own companies" ON public.journal_entries
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.companies 
            WHERE companies.id = journal_entries.company_id 
            AND companies.user_id = auth.uid()
        )
    );

-- Users can only see journal entry lines of their companies
CREATE POLICY "Users can manage journal entry lines of own companies" ON public.journal_entry_lines
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.journal_entries je
            JOIN public.companies c ON c.id = je.company_id
            WHERE je.id = journal_entry_lines.journal_entry_id 
            AND c.user_id = auth.uid()
        )
    );

-- Account types are readable by all authenticated users
ALTER TABLE public.account_types ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Account types are readable by authenticated users" ON public.account_types
    FOR SELECT USING (auth.role() = 'authenticated');

-- Functions and Triggers

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_companies_updated_at BEFORE UPDATE ON public.companies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_financial_years_updated_at BEFORE UPDATE ON public.financial_years
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_accounts_updated_at BEFORE UPDATE ON public.accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_journal_entries_updated_at BEFORE UPDATE ON public.journal_entries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to handle user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, name)
    VALUES (NEW.id, NEW.email, COALESCE(NEW.raw_user_meta_data->>'name', NEW.email));
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile on signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
