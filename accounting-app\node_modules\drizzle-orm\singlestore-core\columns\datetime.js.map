{"version": 3, "sources": ["../../../src/singlestore-core/columns/datetime.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilderRuntimeConfig,\n\tGeneratedColumnConfig,\n\tHasGenerated,\n\tMakeColumnConfig,\n} from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport type { SQL } from '~/sql/index.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { SingleStoreColumn, SingleStoreColumnBuilder } from './common.ts';\n\nexport type SingleStoreDateTimeBuilderInitial<TName extends string> = SingleStoreDateTimeBuilder<{\n\tname: TName;\n\tdataType: 'date';\n\tcolumnType: 'SingleStoreDateTime';\n\tdata: Date;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreDateTimeBuilder<T extends ColumnBuilderBaseConfig<'date', 'SingleStoreDateTime'>>\n\textends SingleStoreColumnBuilder<T, SingleStoreDatetimeConfig>\n{\n\t/** @internal */\n\t// TODO: we need to add a proper support for SingleStore\n\toverride generatedAlwaysAs(\n\t\t_as: SQL<unknown> | (() => SQL) | T['data'],\n\t\t_config?: Partial<GeneratedColumnConfig<unknown>>,\n\t): HasGenerated<this, {}> {\n\t\tthrow new Error('Method not implemented.');\n\t}\n\tstatic override readonly [entityKind]: string = 'SingleStoreDateTimeBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'date', 'SingleStoreDateTime');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreDateTime<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreDateTime<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreDateTime<T extends ColumnBaseConfig<'date', 'SingleStoreDateTime'>>\n\textends SingleStoreColumn<T>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreDateTime';\n\n\tconstructor(\n\t\ttable: AnySingleStoreTable<{ name: T['tableName'] }>,\n\t\tconfig: SingleStoreDateTimeBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn `datetime`;\n\t}\n\n\toverride mapToDriverValue(value: Date): unknown {\n\t\treturn value.toISOString().replace('T', ' ').replace('Z', '');\n\t}\n\n\toverride mapFromDriverValue(value: string): Date {\n\t\treturn new Date(value.replace(' ', 'T') + 'Z');\n\t}\n}\n\nexport type SingleStoreDateTimeStringBuilderInitial<TName extends string> = SingleStoreDateTimeStringBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'SingleStoreDateTimeString';\n\tdata: string;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreDateTimeStringBuilder<T extends ColumnBuilderBaseConfig<'string', 'SingleStoreDateTimeString'>>\n\textends SingleStoreColumnBuilder<T, SingleStoreDatetimeConfig>\n{\n\t/** @internal */\n\t// TODO: we need to add a proper support for SingleStore\n\toverride generatedAlwaysAs(\n\t\t_as: SQL<unknown> | (() => SQL) | T['data'],\n\t\t_config?: Partial<GeneratedColumnConfig<unknown>>,\n\t): HasGenerated<this, {}> {\n\t\tthrow new Error('Method not implemented.');\n\t}\n\tstatic override readonly [entityKind]: string = 'SingleStoreDateTimeStringBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'SingleStoreDateTimeString');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreDateTimeString<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreDateTimeString<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreDateTimeString<T extends ColumnBaseConfig<'string', 'SingleStoreDateTimeString'>>\n\textends SingleStoreColumn<T>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreDateTimeString';\n\n\tconstructor(\n\t\ttable: AnySingleStoreTable<{ name: T['tableName'] }>,\n\t\tconfig: SingleStoreDateTimeStringBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn `datetime`;\n\t}\n}\n\nexport interface SingleStoreDatetimeConfig<TMode extends 'date' | 'string' = 'date' | 'string'> {\n\tmode?: TMode;\n}\n\nexport function datetime(): SingleStoreDateTimeBuilderInitial<''>;\nexport function datetime<TMode extends SingleStoreDatetimeConfig['mode'] & {}>(\n\tconfig?: SingleStoreDatetimeConfig<TMode>,\n): Equal<TMode, 'string'> extends true ? SingleStoreDateTimeStringBuilderInitial<''>\n\t: SingleStoreDateTimeBuilderInitial<''>;\nexport function datetime<TName extends string, TMode extends SingleStoreDatetimeConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: SingleStoreDatetimeConfig<TMode>,\n): Equal<TMode, 'string'> extends true ? SingleStoreDateTimeStringBuilderInitial<TName>\n\t: SingleStoreDateTimeBuilderInitial<TName>;\nexport function datetime(a?: string | SingleStoreDatetimeConfig, b?: SingleStoreDatetimeConfig) {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreDatetimeConfig | undefined>(a, b);\n\tif (config?.mode === 'string') {\n\t\treturn new SingleStoreDateTimeStringBuilder(name);\n\t}\n\treturn new SingleStoreDateTimeBuilder(name);\n}\n"], "mappings": "AAQA,SAAS,kBAAkB;AAG3B,SAAqB,8BAA8B;AACnD,SAAS,mBAAmB,gCAAgC;AAYrD,MAAM,mCACJ,yBACT;AAAA;AAAA;AAAA,EAGU,kBACR,KACA,SACyB;AACzB,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC1C;AAAA,EACA,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,QAAQ,qBAAqB;AAAA,EAC1C;AAAA;AAAA,EAGS,MACR,OACuD;AACvD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,4BACJ,kBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YACC,OACA,QACC;AACD,UAAM,OAAO,MAAM;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,iBAAiB,OAAsB;AAC/C,WAAO,MAAM,YAAY,EAAE,QAAQ,KAAK,GAAG,EAAE,QAAQ,KAAK,EAAE;AAAA,EAC7D;AAAA,EAES,mBAAmB,OAAqB;AAChD,WAAO,oBAAI,KAAK,MAAM,QAAQ,KAAK,GAAG,IAAI,GAAG;AAAA,EAC9C;AACD;AAYO,MAAM,yCACJ,yBACT;AAAA;AAAA;AAAA,EAGU,kBACR,KACA,SACyB;AACzB,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC1C;AAAA,EACA,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,2BAA2B;AAAA,EAClD;AAAA;AAAA,EAGS,MACR,OAC6D;AAC7D,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,kCACJ,kBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YACC,OACA,QACC;AACD,UAAM,OAAO,MAAM;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAgBO,SAAS,SAAS,GAAwC,GAA+B;AAC/F,QAAM,EAAE,MAAM,OAAO,IAAI,uBAA8D,GAAG,CAAC;AAC3F,MAAI,QAAQ,SAAS,UAAU;AAC9B,WAAO,IAAI,iCAAiC,IAAI;AAAA,EACjD;AACA,SAAO,IAAI,2BAA2B,IAAI;AAC3C;", "names": []}