/**
 * Logout API Route
 * 
 * Secure logout endpoint that clears authentication cookies
 * and invalidates user sessions.
 * 
 * Following DRY principle: Single logout endpoint
 * Following YAGNI principle: Only essential logout features
 */

import { NextRequest, NextResponse } from 'next/server';
import { clearAuthCookies, getCurrentUser } from '@/lib/jwt';

/**
 * Logout response interface
 */
interface LogoutResponse {
  success: boolean;
  message: string;
}

/**
 * POST /api/auth/logout
 * 
 * Log out the current user by clearing authentication cookies
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Get current user (optional - for logging purposes)
    const currentUser = getCurrentUser(request);
    
    if (currentUser) {
      console.log(`User logout: ${currentUser.email} (ID: ${currentUser.userId})`);
    }

    // Create response
    const response = NextResponse.json<LogoutResponse>(
      {
        success: true,
        message: 'Logout successful',
      },
      { status: 200 }
    );

    // Clear authentication cookies
    clearAuthCookies(response);

    return response;

  } catch (error) {
    console.error('Logout API error:', error);

    // Even if there's an error, we should still clear cookies
    const response = NextResponse.json<LogoutResponse>(
      {
        success: true,
        message: 'Logout completed',
      },
      { status: 200 }
    );

    clearAuthCookies(response);
    return response;
  }
}

/**
 * GET /api/auth/logout
 * 
 * Also support GET requests for logout (common pattern)
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  return POST(request);
}

/**
 * OPTIONS /api/auth/logout
 * 
 * Handle CORS preflight requests
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
