{"version": 3, "sources": ["../../../../src/pg-core/columns/vector_extension/vector.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from '../common.ts';\n\nexport type PgVectorBuilderInitial<TName extends string, TDimensions extends number> = PgVectorBuilder<{\n\tname: TName;\n\tdataType: 'array';\n\tcolumnType: 'PgVector';\n\tdata: number[];\n\tdriverParam: string;\n\tenumValues: undefined;\n\tdimensions: TDimensions;\n}>;\n\nexport class PgVectorBuilder<T extends ColumnBuilderBaseConfig<'array', 'PgVector'> & { dimensions: number }>\n\textends PgColumnBuilder<\n\t\tT,\n\t\t{ dimensions: T['dimensions'] },\n\t\t{ dimensions: T['dimensions'] }\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'PgVectorBuilder';\n\n\tconstructor(name: string, config: PgVectorConfig<T['dimensions']>) {\n\t\tsuper(name, 'array', 'PgVector');\n\t\tthis.config.dimensions = config.dimensions;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgVector<MakeColumnConfig<T, TTableName> & { dimensions: T['dimensions'] }> {\n\t\treturn new PgVector<MakeColumnConfig<T, TTableName> & { dimensions: T['dimensions'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgVector<T extends ColumnBaseConfig<'array', 'PgVector'> & { dimensions: number | undefined }>\n\textends PgColumn<T, { dimensions: T['dimensions'] }, { dimensions: T['dimensions'] }>\n{\n\tstatic override readonly [entityKind]: string = 'PgVector';\n\n\treadonly dimensions: T['dimensions'] = this.config.dimensions;\n\n\tgetSQLType(): string {\n\t\treturn `vector(${this.dimensions})`;\n\t}\n\n\toverride mapToDriverValue(value: unknown): unknown {\n\t\treturn JSON.stringify(value);\n\t}\n\n\toverride mapFromDriverValue(value: string): unknown {\n\t\treturn value\n\t\t\t.slice(1, -1)\n\t\t\t.split(',')\n\t\t\t.map((v) => Number.parseFloat(v));\n\t}\n}\n\nexport interface PgVectorConfig<TDimensions extends number = number> {\n\tdimensions: TDimensions;\n}\n\nexport function vector<D extends number>(\n\tconfig: PgVectorConfig<D>,\n): PgVectorBuilderInitial<'', D>;\nexport function vector<TName extends string, D extends number>(\n\tname: TName,\n\tconfig: PgVectorConfig<D>,\n): PgVectorBuilderInitial<TName, D>;\nexport function vector(a: string | PgVectorConfig, b?: PgVectorConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgVectorConfig>(a, b);\n\treturn new PgVectorBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAuC;AACvC,oBAA0C;AAYnC,MAAM,wBACJ,8BAKT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAc,QAAyC;AAClE,UAAM,MAAM,SAAS,UAAU;AAC/B,SAAK,OAAO,aAAa,OAAO;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OAC8E;AAC9E,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,iBACJ,uBACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,aAA8B,KAAK,OAAO;AAAA,EAEnD,aAAqB;AACpB,WAAO,UAAU,KAAK,UAAU;AAAA,EACjC;AAAA,EAES,iBAAiB,OAAyB;AAClD,WAAO,KAAK,UAAU,KAAK;AAAA,EAC5B;AAAA,EAES,mBAAmB,OAAwB;AACnD,WAAO,MACL,MAAM,GAAG,EAAE,EACX,MAAM,GAAG,EACT,IAAI,CAAC,MAAM,OAAO,WAAW,CAAC,CAAC;AAAA,EAClC;AACD;AAaO,SAAS,OAAO,GAA4B,GAAoB;AACtE,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAuC,GAAG,CAAC;AACpE,SAAO,IAAI,gBAAgB,MAAM,MAAM;AACxC;", "names": []}