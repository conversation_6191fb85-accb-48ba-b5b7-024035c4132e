/**
 * Repository Index
 * 
 * This file exports all repositories for easy importing and provides
 * a centralized access point for all database operations.
 * 
 * Following DRY principle: Single export point for all repositories
 * Following YAGNI principle: Only essential exports
 */

// Export base repository
export * from './base';

// Export specific repositories
export * from './userRepository';
export * from './companyRepository';

// Re-export for convenience
export { BaseRepository } from './base';
export { UserRepository } from './userRepository';
export { CompanyRepository } from './companyRepository';

/**
 * Repository factory for creating repository instances
 * Following DRY principle: Centralized repository creation
 */
export class RepositoryFactory {
  private static userRepository: UserRepository | null = null;
  private static companyRepository: CompanyRepository | null = null;

  /**
   * Get user repository instance (Singleton)
   */
  static getUserRepository(): UserRepository {
    if (!this.userRepository) {
      this.userRepository = new UserRepository();
    }
    return this.userRepository;
  }

  /**
   * Get company repository instance (Singleton)
   */
  static getCompanyRepository(): CompanyRepository {
    if (!this.companyRepository) {
      this.companyRepository = new CompanyRepository();
    }
    return this.companyRepository;
  }

  /**
   * Reset all repository instances (useful for testing)
   */
  static reset(): void {
    this.userRepository = null;
    this.companyRepository = null;
  }
}

/**
 * Convenience exports for direct repository access
 */
export const userRepository = RepositoryFactory.getUserRepository();
export const companyRepository = RepositoryFactory.getCompanyRepository();
