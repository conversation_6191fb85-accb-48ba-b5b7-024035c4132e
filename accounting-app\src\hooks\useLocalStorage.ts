/**
 * useLocalStorage Hook
 * 
 * A custom React hook for managing localStorage with TypeScript support.
 * This hook provides a simple interface for storing and retrieving data from localStorage
 * with automatic JSON serialization/deserialization and error handling.
 */

import { useState, useEffect, useCallback } from 'react';

/**
 * Custom hook for managing localStorage state
 * 
 * @template T - The type of the stored value
 * @param key - The localStorage key to use
 * @param initialValue - The initial value to use if no stored value exists
 * @returns A tuple containing the current value, setter function, and remove function
 * 
 * @example
 * const [user, setUser, removeUser] = useLocalStorage<User>('user', null);
 * 
 * // Set a value
 * setUser({ id: '1', name: '<PERSON>' });
 * 
 * // Remove the value
 * removeUser();
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  // State to store the current value
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined') {
        return initialValue;
      }

      // Get the item from localStorage
      const item = window.localStorage.getItem(key);
      
      // Parse the stored JSON or return the initial value
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      // If error occurs, log it and return initial value
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  /**
   * Function to set a new value in localStorage and state
   * 
   * @param value - The new value to store, or a function that receives the current value
   */
  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        // Allow value to be a function so we have the same API as useState
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        
        // Save state
        setStoredValue(valueToStore);
        
        // Save to localStorage if in browser environment
        if (typeof window !== 'undefined') {
          window.localStorage.setItem(key, JSON.stringify(valueToStore));
        }
      } catch (error) {
        // Log error if setting localStorage fails
        console.error(`Error setting localStorage key "${key}":`, error);
      }
    },
    [key, storedValue]
  );

  /**
   * Function to remove the value from localStorage and reset to initial value
   */
  const removeValue = useCallback(() => {
    try {
      // Reset state to initial value
      setStoredValue(initialValue);
      
      // Remove from localStorage if in browser environment
      if (typeof window !== 'undefined') {
        window.localStorage.removeItem(key);
      }
    } catch (error) {
      // Log error if removing from localStorage fails
      console.error(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  /**
   * Effect to listen for changes to localStorage from other tabs/windows
   * This ensures the hook stays in sync across browser tabs
   */
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') {
      return;
    }

    /**
     * Handler for storage events (when localStorage changes in another tab)
     * 
     * @param e - The storage event
     */
    const handleStorageChange = (e: StorageEvent) => {
      // Only respond to changes for our specific key
      if (e.key === key && e.newValue !== null) {
        try {
          // Parse the new value and update state
          const newValue = JSON.parse(e.newValue);
          setStoredValue(newValue);
        } catch (error) {
          console.error(`Error parsing localStorage value for key "${key}":`, error);
        }
      } else if (e.key === key && e.newValue === null) {
        // Key was removed, reset to initial value
        setStoredValue(initialValue);
      }
    };

    // Add event listener for storage changes
    window.addEventListener('storage', handleStorageChange);

    // Cleanup function to remove event listener
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [key, initialValue]);

  return [storedValue, setValue, removeValue];
}
