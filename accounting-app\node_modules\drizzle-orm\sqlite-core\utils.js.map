{"version": 3, "sources": ["../../src/sqlite-core/utils.ts"], "sourcesContent": ["import { is } from '~/entity.ts';\nimport { Table } from '~/table.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type { Check } from './checks.ts';\nimport { CheckBuilder } from './checks.ts';\nimport type { ForeignKey } from './foreign-keys.ts';\nimport { ForeignKeyBuilder } from './foreign-keys.ts';\nimport type { Index } from './indexes.ts';\nimport { IndexBuilder } from './indexes.ts';\nimport type { PrimaryKey } from './primary-keys.ts';\nimport { PrimaryKeyBuilder } from './primary-keys.ts';\nimport { SQLiteTable } from './table.ts';\nimport { type UniqueConstraint, UniqueConstraintBuilder } from './unique-constraint.ts';\nimport type { SQLiteView } from './view.ts';\n\nexport function getTableConfig<TTable extends SQLiteTable>(table: TTable) {\n\tconst columns = Object.values(table[SQLiteTable.Symbol.Columns]);\n\tconst indexes: Index[] = [];\n\tconst checks: Check[] = [];\n\tconst primaryKeys: PrimaryKey[] = [];\n\tconst uniqueConstraints: UniqueConstraint[] = [];\n\tconst foreignKeys: ForeignKey[] = Object.values(table[SQLiteTable.Symbol.InlineForeignKeys]);\n\tconst name = table[Table.Symbol.Name];\n\n\tconst extraConfigBuilder = table[SQLiteTable.Symbol.ExtraConfigBuilder];\n\n\tif (extraConfigBuilder !== undefined) {\n\t\tconst extraConfig = extraConfigBuilder(table[SQLiteTable.Symbol.Columns]);\n\t\tconst extraValues = Array.isArray(extraConfig) ? extraConfig.flat(1) as any[] : Object.values(extraConfig);\n\t\tfor (const builder of Object.values(extraValues)) {\n\t\t\tif (is(builder, IndexBuilder)) {\n\t\t\t\tindexes.push(builder.build(table));\n\t\t\t} else if (is(builder, CheckBuilder)) {\n\t\t\t\tchecks.push(builder.build(table));\n\t\t\t} else if (is(builder, UniqueConstraintBuilder)) {\n\t\t\t\tuniqueConstraints.push(builder.build(table));\n\t\t\t} else if (is(builder, PrimaryKeyBuilder)) {\n\t\t\t\tprimaryKeys.push(builder.build(table));\n\t\t\t} else if (is(builder, ForeignKeyBuilder)) {\n\t\t\t\tforeignKeys.push(builder.build(table));\n\t\t\t}\n\t\t}\n\t}\n\n\treturn {\n\t\tcolumns,\n\t\tindexes,\n\t\tforeignKeys,\n\t\tchecks,\n\t\tprimaryKeys,\n\t\tuniqueConstraints,\n\t\tname,\n\t};\n}\n\nexport type OnConflict = 'rollback' | 'abort' | 'fail' | 'ignore' | 'replace';\n\nexport function getViewConfig<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n>(view: SQLiteView<TName, TExisting>) {\n\treturn {\n\t\t...view[ViewBaseConfig],\n\t\t// ...view[SQLiteViewConfig],\n\t};\n}\n"], "mappings": "AAAA,SAAS,UAAU;AACnB,SAAS,aAAa;AACtB,SAAS,sBAAsB;AAE/B,SAAS,oBAAoB;AAE7B,SAAS,yBAAyB;AAElC,SAAS,oBAAoB;AAE7B,SAAS,yBAAyB;AAClC,SAAS,mBAAmB;AAC5B,SAAgC,+BAA+B;AAGxD,SAAS,eAA2C,OAAe;AACzE,QAAM,UAAU,OAAO,OAAO,MAAM,YAAY,OAAO,OAAO,CAAC;AAC/D,QAAM,UAAmB,CAAC;AAC1B,QAAM,SAAkB,CAAC;AACzB,QAAM,cAA4B,CAAC;AACnC,QAAM,oBAAwC,CAAC;AAC/C,QAAM,cAA4B,OAAO,OAAO,MAAM,YAAY,OAAO,iBAAiB,CAAC;AAC3F,QAAM,OAAO,MAAM,MAAM,OAAO,IAAI;AAEpC,QAAM,qBAAqB,MAAM,YAAY,OAAO,kBAAkB;AAEtE,MAAI,uBAAuB,QAAW;AACrC,UAAM,cAAc,mBAAmB,MAAM,YAAY,OAAO,OAAO,CAAC;AACxE,UAAM,cAAc,MAAM,QAAQ,WAAW,IAAI,YAAY,KAAK,CAAC,IAAa,OAAO,OAAO,WAAW;AACzG,eAAW,WAAW,OAAO,OAAO,WAAW,GAAG;AACjD,UAAI,GAAG,SAAS,YAAY,GAAG;AAC9B,gBAAQ,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MAClC,WAAW,GAAG,SAAS,YAAY,GAAG;AACrC,eAAO,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MACjC,WAAW,GAAG,SAAS,uBAAuB,GAAG;AAChD,0BAAkB,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MAC5C,WAAW,GAAG,SAAS,iBAAiB,GAAG;AAC1C,oBAAY,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MACtC,WAAW,GAAG,SAAS,iBAAiB,GAAG;AAC1C,oBAAY,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MACtC;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AAIO,SAAS,cAGd,MAAoC;AACrC,SAAO;AAAA,IACN,GAAG,KAAK,cAAc;AAAA;AAAA,EAEvB;AACD;", "names": []}