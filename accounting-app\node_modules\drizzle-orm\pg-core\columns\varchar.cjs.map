{"version": 3, "sources": ["../../../src/pg-core/columns/varchar.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig, type Writable } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgVarcharBuilderInitial<\n\tTName extends string,\n\tTEnum extends [string, ...string[]],\n\tTLength extends number | undefined,\n> = PgVarcharBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgVarchar';\n\tdata: TEnum[number];\n\tdriverParam: string;\n\tenumValues: TEnum;\n\tlength: TLength;\n}>;\n\nexport class PgVarcharBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'PgVarchar'> & { length?: number | undefined },\n> extends PgColumnBuilder<\n\tT,\n\t{ length: T['length']; enumValues: T['enumValues'] },\n\t{ length: T['length'] }\n> {\n\tstatic override readonly [entityKind]: string = 'PgVarcharBuilder';\n\n\tconstructor(name: T['name'], config: PgVarcharConfig<T['enumValues'], T['length']>) {\n\t\tsuper(name, 'string', 'PgVarchar');\n\t\tthis.config.length = config.length;\n\t\tthis.config.enumValues = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgVarchar<MakeColumnConfig<T, TTableName> & { length: T['length'] }> {\n\t\treturn new PgVarchar<MakeColumnConfig<T, TTableName> & { length: T['length'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgVarchar<T extends ColumnBaseConfig<'string', 'PgVarchar'> & { length?: number | undefined }>\n\textends PgColumn<T, { length: T['length']; enumValues: T['enumValues'] }, { length: T['length'] }>\n{\n\tstatic override readonly [entityKind]: string = 'PgVarchar';\n\n\treadonly length = this.config.length;\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `varchar` : `varchar(${this.length})`;\n\t}\n}\n\nexport interface PgVarcharConfig<\n\tTEnum extends readonly string[] | string[] | undefined = readonly string[] | string[] | undefined,\n\tTLength extends number | undefined = number | undefined,\n> {\n\tenum?: TEnum;\n\tlength?: TLength;\n}\n\nexport function varchar(): PgVarcharBuilderInitial<'', [string, ...string[]], undefined>;\nexport function varchar<\n\tU extends string,\n\tT extends Readonly<[U, ...U[]]>,\n\tL extends number | undefined,\n>(\n\tconfig?: PgVarcharConfig<T | Writable<T>, L>,\n): PgVarcharBuilderInitial<'', Writable<T>, L>;\nexport function varchar<\n\tTName extends string,\n\tU extends string,\n\tT extends Readonly<[U, ...U[]]>,\n\tL extends number | undefined,\n>(\n\tname: TName,\n\tconfig?: PgVarcharConfig<T | Writable<T>, L>,\n): PgVarcharBuilderInitial<TName, Writable<T>, L>;\nexport function varchar(a?: string | PgVarcharConfig, b: PgVarcharConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<PgVarcharConfig>(a, b);\n\treturn new PgVarcharBuilder(name, config as any);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAsD;AACtD,oBAA0C;AAgBnC,MAAM,yBAEH,8BAIR;AAAA,EACD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAuD;AACnF,UAAM,MAAM,UAAU,WAAW;AACjC,SAAK,OAAO,SAAS,OAAO;AAC5B,SAAK,OAAO,aAAa,OAAO;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OACuE;AACvE,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,kBACJ,uBACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,SAAS,KAAK,OAAO;AAAA,EACZ,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,YAAY,WAAW,KAAK,MAAM;AAAA,EACtE;AACD;AA2BO,SAAS,QAAQ,GAA8B,IAAqB,CAAC,GAAQ;AACnF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAwC,GAAG,CAAC;AACrE,SAAO,IAAI,iBAAiB,MAAM,MAAa;AAChD;", "names": []}