{"version": 3, "sources": ["../../src/sqlite-proxy/migrator.ts"], "sourcesContent": ["import type { MigrationConfig } from '~/migrator.ts';\nimport { readMigrationFiles } from '~/migrator.ts';\nimport { sql } from '~/sql/sql.ts';\nimport type { SqliteRemoteDatabase } from './driver.ts';\n\nexport type ProxyMigrator = (migrationQueries: string[]) => Promise<void>;\n\nexport async function migrate<TSchema extends Record<string, unknown>>(\n\tdb: SqliteRemoteDatabase<TSchema>,\n\tcallback: ProxyMigrator,\n\tconfig: MigrationConfig,\n) {\n\tconst migrations = readMigrationFiles(config);\n\n\tconst migrationsTable = typeof config === 'string'\n\t\t? '__drizzle_migrations'\n\t\t: config.migrationsTable ?? '__drizzle_migrations';\n\n\tconst migrationTableCreate = sql`\n\t\tCREATE TABLE IF NOT EXISTS ${sql.identifier(migrationsTable)} (\n\t\t\tid SERIAL PRIMARY KEY,\n\t\t\thash text NOT NULL,\n\t\t\tcreated_at numeric\n\t\t)\n\t`;\n\n\tawait db.run(migrationTableCreate);\n\n\tconst dbMigrations = await db.values<[number, string, string]>(\n\t\tsql`SELECT id, hash, created_at FROM ${sql.identifier(migrationsTable)} ORDER BY created_at DESC LIMIT 1`,\n\t);\n\n\tconst lastDbMigration = dbMigrations[0] ?? undefined;\n\n\tconst queriesToRun: string[] = [];\n\tfor (const migration of migrations) {\n\t\tif (\n\t\t\t!lastDbMigration\n\t\t\t|| Number(lastDbMigration[2])! < migration.folderMillis\n\t\t) {\n\t\t\tqueriesToRun.push(\n\t\t\t\t...migration.sql,\n\t\t\t\t`INSERT INTO \\`${migrationsTable}\\` (\"hash\", \"created_at\") VALUES('${migration.hash}', '${migration.folderMillis}')`,\n\t\t\t);\n\t\t}\n\t}\n\n\tawait callback(queriesToRun);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAmC;AACnC,iBAAoB;AAKpB,eAAsB,QACrB,IACA,UACA,QACC;AACD,QAAM,iBAAa,oCAAmB,MAAM;AAE5C,QAAM,kBAAkB,OAAO,WAAW,WACvC,yBACA,OAAO,mBAAmB;AAE7B,QAAM,uBAAuB;AAAA,+BACC,eAAI,WAAW,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAO7D,QAAM,GAAG,IAAI,oBAAoB;AAEjC,QAAM,eAAe,MAAM,GAAG;AAAA,IAC7B,kDAAuC,eAAI,WAAW,eAAe,CAAC;AAAA,EACvE;AAEA,QAAM,kBAAkB,aAAa,CAAC,KAAK;AAE3C,QAAM,eAAyB,CAAC;AAChC,aAAW,aAAa,YAAY;AACnC,QACC,CAAC,mBACE,OAAO,gBAAgB,CAAC,CAAC,IAAK,UAAU,cAC1C;AACD,mBAAa;AAAA,QACZ,GAAG,UAAU;AAAA,QACb,iBAAiB,eAAe,qCAAqC,UAAU,IAAI,OAAO,UAAU,YAAY;AAAA,MACjH;AAAA,IACD;AAAA,EACD;AAEA,QAAM,SAAS,YAAY;AAC5B;", "names": []}