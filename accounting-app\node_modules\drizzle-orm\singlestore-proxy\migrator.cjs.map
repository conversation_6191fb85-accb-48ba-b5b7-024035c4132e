{"version": 3, "sources": ["../../src/singlestore-proxy/migrator.ts"], "sourcesContent": ["import type { MigrationConfig } from '~/migrator.ts';\nimport { readMigrationFiles } from '~/migrator.ts';\nimport { sql } from '~/sql/sql.ts';\nimport type { SingleStoreRemoteDatabase } from './driver.ts';\n\nexport type ProxyMigrator = (migrationQueries: string[]) => Promise<void>;\n\nexport async function migrate<TSchema extends Record<string, unknown>>(\n\tdb: SingleStoreRemoteDatabase<TSchema>,\n\tcallback: ProxyMigrator,\n\tconfig: MigrationConfig,\n) {\n\tconst migrations = readMigrationFiles(config);\n\n\tconst migrationsTable = config.migrationsTable ?? '__drizzle_migrations';\n\tconst migrationTableCreate = sql`\n\t\tcreate table if not exists ${sql.identifier(migrationsTable)} (\n\t\t\tid serial primary key,\n\t\t\thash text not null,\n\t\t\tcreated_at bigint\n\t\t)\n\t`;\n\tawait db.execute(migrationTableCreate);\n\n\tconst dbMigrations = await db.select({\n\t\tid: sql.raw('id'),\n\t\thash: sql.raw('hash'),\n\t\tcreated_at: sql.raw('created_at'),\n\t}).from(sql.identifier(migrationsTable).getSQL()).orderBy(\n\t\tsql.raw('created_at desc'),\n\t).limit(1);\n\n\tconst lastDbMigration = dbMigrations[0];\n\n\tconst queriesToRun: string[] = [];\n\n\tfor (const migration of migrations) {\n\t\tif (\n\t\t\t!lastDbMigration\n\t\t\t|| Number(lastDbMigration.created_at) < migration.folderMillis\n\t\t) {\n\t\t\tqueriesToRun.push(\n\t\t\t\t...migration.sql,\n\t\t\t\t`insert into ${\n\t\t\t\t\tsql.identifier(migrationsTable).value\n\t\t\t\t} (\\`hash\\`, \\`created_at\\`) values('${migration.hash}', '${migration.folderMillis}')`,\n\t\t\t);\n\t\t}\n\t}\n\n\tawait callback(queriesToRun);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAmC;AACnC,iBAAoB;AAKpB,eAAsB,QACrB,IACA,UACA,QACC;AACD,QAAM,iBAAa,oCAAmB,MAAM;AAE5C,QAAM,kBAAkB,OAAO,mBAAmB;AAClD,QAAM,uBAAuB;AAAA,+BACC,eAAI,WAAW,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAM7D,QAAM,GAAG,QAAQ,oBAAoB;AAErC,QAAM,eAAe,MAAM,GAAG,OAAO;AAAA,IACpC,IAAI,eAAI,IAAI,IAAI;AAAA,IAChB,MAAM,eAAI,IAAI,MAAM;AAAA,IACpB,YAAY,eAAI,IAAI,YAAY;AAAA,EACjC,CAAC,EAAE,KAAK,eAAI,WAAW,eAAe,EAAE,OAAO,CAAC,EAAE;AAAA,IACjD,eAAI,IAAI,iBAAiB;AAAA,EAC1B,EAAE,MAAM,CAAC;AAET,QAAM,kBAAkB,aAAa,CAAC;AAEtC,QAAM,eAAyB,CAAC;AAEhC,aAAW,aAAa,YAAY;AACnC,QACC,CAAC,mBACE,OAAO,gBAAgB,UAAU,IAAI,UAAU,cACjD;AACD,mBAAa;AAAA,QACZ,GAAG,UAAU;AAAA,QACb,eACC,eAAI,WAAW,eAAe,EAAE,KACjC,uCAAuC,UAAU,IAAI,OAAO,UAAU,YAAY;AAAA,MACnF;AAAA,IACD;AAAA,EACD;AAEA,QAAM,SAAS,YAAY;AAC5B;", "names": []}