import { bigint } from "./bigint.js";
import { binary } from "./binary.js";
import { boolean } from "./boolean.js";
import { char } from "./char.js";
import { customType } from "./custom.js";
import { date } from "./date.js";
import { datetime } from "./datetime.js";
import { decimal } from "./decimal.js";
import { double } from "./double.js";
import { mysqlEnum } from "./enum.js";
import { float } from "./float.js";
import { int } from "./int.js";
import { json } from "./json.js";
import { mediumint } from "./mediumint.js";
import { real } from "./real.js";
import { serial } from "./serial.js";
import { smallint } from "./smallint.js";
import { longtext, mediumtext, text, tinytext } from "./text.js";
import { time } from "./time.js";
import { timestamp } from "./timestamp.js";
import { tinyint } from "./tinyint.js";
import { varbinary } from "./varbinary.js";
import { varchar } from "./varchar.js";
import { year } from "./year.js";
export declare function getMySqlColumnBuilders(): {
    bigint: typeof bigint;
    binary: typeof binary;
    boolean: typeof boolean;
    char: typeof char;
    customType: typeof customType;
    date: typeof date;
    datetime: typeof datetime;
    decimal: typeof decimal;
    double: typeof double;
    mysqlEnum: typeof mysqlEnum;
    float: typeof float;
    int: typeof int;
    json: typeof json;
    mediumint: typeof mediumint;
    real: typeof real;
    serial: typeof serial;
    smallint: typeof smallint;
    text: typeof text;
    time: typeof time;
    timestamp: typeof timestamp;
    tinyint: typeof tinyint;
    varbinary: typeof varbinary;
    varchar: typeof varchar;
    year: typeof year;
    longtext: typeof longtext;
    mediumtext: typeof mediumtext;
    tinytext: typeof tinytext;
};
export type MySqlColumnBuilders = ReturnType<typeof getMySqlColumnBuilders>;
