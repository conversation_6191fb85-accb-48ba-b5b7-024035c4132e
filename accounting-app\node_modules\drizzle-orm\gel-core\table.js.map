{"version": 3, "sources": ["../../src/gel-core/table.ts"], "sourcesContent": ["import type { BuildColumns, BuildExtraConfigColumns } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport { Table, type TableConfig as TableConfigBase, type UpdateTableConfig } from '~/table.ts';\nimport type { CheckBuilder } from './checks.ts';\nimport { type GelColumnsBuilders, getGelColumnBuilders } from './columns/all.ts';\nimport type { GelColumn, GelColumnBuilder, GelColumnBuilderBase, GelExtraConfigColumn } from './columns/common.ts';\nimport type { ForeignKey, ForeignKeyBuilder } from './foreign-keys.ts';\nimport type { AnyIndexBuilder } from './indexes.ts';\nimport type { GelPolicy } from './policies.ts';\nimport type { PrimaryKeyBuilder } from './primary-keys.ts';\nimport type { UniqueConstraintBuilder } from './unique-constraint.ts';\n\nexport type GelTableExtraConfigValue =\n\t| AnyIndexBuilder\n\t| CheckBuilder\n\t| ForeignKeyBuilder\n\t| PrimaryKeyBuilder\n\t| UniqueConstraintBuilder\n\t| GelPolicy;\n\nexport type GelTableExtraConfig = Record<\n\tstring,\n\tGelTableExtraConfigValue\n>;\n\nexport type TableConfig = TableConfigBase<GelColumn>;\n\n/** @internal */\nexport const InlineForeignKeys = Symbol.for('drizzle:GelInlineForeignKeys');\n/** @internal */\nexport const EnableRLS = Symbol.for('drizzle:EnableRLS');\n\nexport class GelTable<T extends TableConfig = TableConfig> extends Table<T> {\n\tstatic override readonly [entityKind]: string = 'GelTable';\n\n\t/** @internal */\n\tstatic override readonly Symbol = Object.assign({}, Table.Symbol, {\n\t\tInlineForeignKeys: InlineForeignKeys as typeof InlineForeignKeys,\n\t\tEnableRLS: EnableRLS as typeof EnableRLS,\n\t});\n\n\t/**@internal */\n\t[InlineForeignKeys]: ForeignKey[] = [];\n\n\t/** @internal */\n\t[EnableRLS]: boolean = false;\n\n\t/** @internal */\n\toverride [Table.Symbol.ExtraConfigBuilder]: ((self: Record<string, GelColumn>) => GelTableExtraConfig) | undefined =\n\t\tundefined;\n\n\t/** @internal */\n\toverride [Table.Symbol.ExtraConfigColumns]: Record<string, GelExtraConfigColumn> = {};\n}\n\nexport type AnyGelTable<TPartial extends Partial<TableConfig> = {}> = GelTable<\n\tUpdateTableConfig<TableConfig, TPartial>\n>;\n\nexport type GelTableWithColumns<T extends TableConfig> =\n\t& GelTable<T>\n\t& {\n\t\t[Key in keyof T['columns']]: T['columns'][Key];\n\t}\n\t& {\n\t\tenableRLS: () => Omit<\n\t\t\tGelTableWithColumns<T>,\n\t\t\t'enableRLS'\n\t\t>;\n\t};\n\n/** @internal */\nexport function gelTableWithSchema<\n\tTTableName extends string,\n\tTSchemaName extends string | undefined,\n\tTColumnsMap extends Record<string, GelColumnBuilderBase>,\n>(\n\tname: TTableName,\n\tcolumns: TColumnsMap | ((columnTypes: GelColumnsBuilders) => TColumnsMap),\n\textraConfig:\n\t\t| ((\n\t\t\tself: BuildExtraConfigColumns<TTableName, TColumnsMap, 'gel'>,\n\t\t) => GelTableExtraConfig | GelTableExtraConfigValue[])\n\t\t| undefined,\n\tschema: TSchemaName,\n\tbaseName = name,\n): GelTableWithColumns<{\n\tname: TTableName;\n\tschema: TSchemaName;\n\tcolumns: BuildColumns<TTableName, TColumnsMap, 'gel'>;\n\tdialect: 'gel';\n}> {\n\tconst rawTable = new GelTable<{\n\t\tname: TTableName;\n\t\tschema: TSchemaName;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'gel'>;\n\t\tdialect: 'gel';\n\t}>(name, schema, baseName);\n\n\tconst parsedColumns: TColumnsMap = typeof columns === 'function' ? columns(getGelColumnBuilders()) : columns;\n\n\tconst builtColumns = Object.fromEntries(\n\t\tObject.entries(parsedColumns).map(([name, colBuilderBase]) => {\n\t\t\tconst colBuilder = colBuilderBase as GelColumnBuilder;\n\t\t\tcolBuilder.setName(name);\n\t\t\tconst column = colBuilder.build(rawTable);\n\t\t\trawTable[InlineForeignKeys].push(...colBuilder.buildForeignKeys(column, rawTable));\n\t\t\treturn [name, column];\n\t\t}),\n\t) as unknown as BuildColumns<TTableName, TColumnsMap, 'gel'>;\n\n\tconst builtColumnsForExtraConfig = Object.fromEntries(\n\t\tObject.entries(parsedColumns).map(([name, colBuilderBase]) => {\n\t\t\tconst colBuilder = colBuilderBase as GelColumnBuilder;\n\t\t\tcolBuilder.setName(name);\n\t\t\tconst column = colBuilder.buildExtraConfigColumn(rawTable);\n\t\t\treturn [name, column];\n\t\t}),\n\t) as unknown as BuildExtraConfigColumns<TTableName, TColumnsMap, 'gel'>;\n\n\tconst table = Object.assign(rawTable, builtColumns);\n\n\ttable[Table.Symbol.Columns] = builtColumns;\n\ttable[Table.Symbol.ExtraConfigColumns] = builtColumnsForExtraConfig;\n\n\tif (extraConfig) {\n\t\ttable[GelTable.Symbol.ExtraConfigBuilder] = extraConfig as any;\n\t}\n\n\treturn Object.assign(table, {\n\t\tenableRLS: () => {\n\t\t\ttable[GelTable.Symbol.EnableRLS] = true;\n\t\t\treturn table as GelTableWithColumns<{\n\t\t\t\tname: TTableName;\n\t\t\t\tschema: TSchemaName;\n\t\t\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'gel'>;\n\t\t\t\tdialect: 'gel';\n\t\t\t}>;\n\t\t},\n\t});\n}\n\nexport interface GelTableFn<TSchema extends string | undefined = undefined> {\n\t/**\n\t * @deprecated The third parameter of GelTable is changing and will only accept an array instead of an object\n\t *\n\t * @example\n\t * Deprecated version:\n\t * ```ts\n\t * export const users = gelTable(\"users\", {\n\t * \tid: integer(),\n\t * }, (t) => ({\n\t * \tidx: index('custom_name').on(t.id)\n\t * }));\n\t * ```\n\t *\n\t * New API:\n\t * ```ts\n\t * export const users = gelTable(\"users\", {\n\t * \tid: integer(),\n\t * }, (t) => [\n\t * \tindex('custom_name').on(t.id)\n\t * ]);\n\t * ```\n\t */\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, GelColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: TColumnsMap,\n\t\textraConfig: (\n\t\t\tself: BuildExtraConfigColumns<TTableName, TColumnsMap, 'gel'>,\n\t\t) => GelTableExtraConfig,\n\t): GelTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'gel'>;\n\t\tdialect: 'gel';\n\t}>;\n\n\t/**\n\t * @deprecated The third parameter of gelTable is changing and will only accept an array instead of an object\n\t *\n\t * @example\n\t * Deprecated version:\n\t * ```ts\n\t * export const users = gelTable(\"users\", {\n\t * \tid: integer(),\n\t * }, (t) => ({\n\t * \tidx: index('custom_name').on(t.id)\n\t * }));\n\t * ```\n\t *\n\t * New API:\n\t * ```ts\n\t * export const users = gelTable(\"users\", {\n\t * \tid: integer(),\n\t * }, (t) => [\n\t * \tindex('custom_name').on(t.id)\n\t * ]);\n\t * ```\n\t */\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, GelColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: (columnTypes: GelColumnsBuilders) => TColumnsMap,\n\t\textraConfig: (self: BuildExtraConfigColumns<TTableName, TColumnsMap, 'gel'>) => GelTableExtraConfig,\n\t): GelTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'gel'>;\n\t\tdialect: 'gel';\n\t}>;\n\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, GelColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: TColumnsMap,\n\t\textraConfig?: (\n\t\t\tself: BuildExtraConfigColumns<TTableName, TColumnsMap, 'gel'>,\n\t\t) => GelTableExtraConfigValue[],\n\t): GelTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'gel'>;\n\t\tdialect: 'gel';\n\t}>;\n\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, GelColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: (columnTypes: GelColumnsBuilders) => TColumnsMap,\n\t\textraConfig?: (self: BuildExtraConfigColumns<TTableName, TColumnsMap, 'gel'>) => GelTableExtraConfigValue[],\n\t): GelTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'gel'>;\n\t\tdialect: 'gel';\n\t}>;\n}\n\nexport const gelTable: GelTableFn = (name, columns, extraConfig) => {\n\treturn gelTableWithSchema(name, columns, extraConfig, undefined);\n};\n\nexport function gelTableCreator(customizeTableName: (name: string) => string): GelTableFn {\n\treturn (name, columns, extraConfig) => {\n\t\treturn gelTableWithSchema(customizeTableName(name) as typeof name, columns, extraConfig, undefined, name);\n\t};\n}\n"], "mappings": "AACA,SAAS,kBAAkB;AAC3B,SAAS,aAA0E;AAEnF,SAAkC,4BAA4B;AAwBvD,MAAM,oBAAoB,OAAO,IAAI,8BAA8B;AAEnE,MAAM,YAAY,OAAO,IAAI,mBAAmB;AAEhD,MAAM,iBAAsD,MAAS;AAAA,EAC3E,QAA0B,UAAU,IAAY;AAAA;AAAA,EAGhD,OAAyB,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM,QAAQ;AAAA,IACjE;AAAA,IACA;AAAA,EACD,CAAC;AAAA;AAAA,EAGD,CAAC,iBAAiB,IAAkB,CAAC;AAAA;AAAA,EAGrC,CAAC,SAAS,IAAa;AAAA;AAAA,EAGvB,CAAU,MAAM,OAAO,kBAAkB,IACxC;AAAA;AAAA,EAGD,CAAU,MAAM,OAAO,kBAAkB,IAA0C,CAAC;AACrF;AAmBO,SAAS,mBAKf,MACA,SACA,aAKA,QACA,WAAW,MAMT;AACF,QAAM,WAAW,IAAI,SAKlB,MAAM,QAAQ,QAAQ;AAEzB,QAAM,gBAA6B,OAAO,YAAY,aAAa,QAAQ,qBAAqB,CAAC,IAAI;AAErG,QAAM,eAAe,OAAO;AAAA,IAC3B,OAAO,QAAQ,aAAa,EAAE,IAAI,CAAC,CAACA,OAAM,cAAc,MAAM;AAC7D,YAAM,aAAa;AACnB,iBAAW,QAAQA,KAAI;AACvB,YAAM,SAAS,WAAW,MAAM,QAAQ;AACxC,eAAS,iBAAiB,EAAE,KAAK,GAAG,WAAW,iBAAiB,QAAQ,QAAQ,CAAC;AACjF,aAAO,CAACA,OAAM,MAAM;AAAA,IACrB,CAAC;AAAA,EACF;AAEA,QAAM,6BAA6B,OAAO;AAAA,IACzC,OAAO,QAAQ,aAAa,EAAE,IAAI,CAAC,CAACA,OAAM,cAAc,MAAM;AAC7D,YAAM,aAAa;AACnB,iBAAW,QAAQA,KAAI;AACvB,YAAM,SAAS,WAAW,uBAAuB,QAAQ;AACzD,aAAO,CAACA,OAAM,MAAM;AAAA,IACrB,CAAC;AAAA,EACF;AAEA,QAAM,QAAQ,OAAO,OAAO,UAAU,YAAY;AAElD,QAAM,MAAM,OAAO,OAAO,IAAI;AAC9B,QAAM,MAAM,OAAO,kBAAkB,IAAI;AAEzC,MAAI,aAAa;AAChB,UAAM,SAAS,OAAO,kBAAkB,IAAI;AAAA,EAC7C;AAEA,SAAO,OAAO,OAAO,OAAO;AAAA,IAC3B,WAAW,MAAM;AAChB,YAAM,SAAS,OAAO,SAAS,IAAI;AACnC,aAAO;AAAA,IAMR;AAAA,EACD,CAAC;AACF;AA4GO,MAAM,WAAuB,CAAC,MAAM,SAAS,gBAAgB;AACnE,SAAO,mBAAmB,MAAM,SAAS,aAAa,MAAS;AAChE;AAEO,SAAS,gBAAgB,oBAA0D;AACzF,SAAO,CAAC,MAAM,SAAS,gBAAgB;AACtC,WAAO,mBAAmB,mBAAmB,IAAI,GAAkB,SAAS,aAAa,QAAW,IAAI;AAAA,EACzG;AACD;", "names": ["name"]}