{"version": 3, "sources": ["../../../src/singlestore-core/query-builders/query-builder.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { SingleStoreDialectConfig } from '~/singlestore-core/dialect.ts';\nimport { SingleStoreDialect } from '~/singlestore-core/dialect.ts';\nimport type { WithBuilder } from '~/singlestore-core/subquery.ts';\nimport type { ColumnsSelection, SQL } from '~/sql/sql.ts';\nimport { WithSubquery } from '~/subquery.ts';\nimport { SingleStoreSelectBuilder } from './select.ts';\nimport type { SelectedFields } from './select.types.ts';\n\nexport class QueryBuilder {\n\tstatic readonly [entityKind]: string = 'SingleStoreQueryBuilder';\n\n\tprivate dialect: SingleStoreDialect | undefined;\n\tprivate dialectConfig: SingleStoreDialectConfig | undefined;\n\n\tconstructor(dialect?: SingleStoreDialect | SingleStoreDialectConfig) {\n\t\tthis.dialect = is(dialect, SingleStoreDialect) ? dialect : undefined;\n\t\tthis.dialectConfig = is(dialect, SingleStoreDialect) ? undefined : dialect;\n\t}\n\n\t$with: WithBuilder = (alias: string, selection?: ColumnsSelection) => {\n\t\tconst queryBuilder = this;\n\t\tconst as = (\n\t\t\tqb:\n\t\t\t\t| TypedQueryBuilder<ColumnsSelection | undefined>\n\t\t\t\t| SQL\n\t\t\t\t| ((qb: QueryBuilder) => TypedQueryBuilder<ColumnsSelection | undefined> | SQL),\n\t\t) => {\n\t\t\tif (typeof qb === 'function') {\n\t\t\t\tqb = qb(queryBuilder);\n\t\t\t}\n\n\t\t\treturn new Proxy(\n\t\t\t\tnew WithSubquery(\n\t\t\t\t\tqb.getSQL(),\n\t\t\t\t\tselection ?? ('getSelectedFields' in qb ? qb.getSelectedFields() ?? {} : {}) as SelectedFields,\n\t\t\t\t\talias,\n\t\t\t\t\ttrue,\n\t\t\t\t),\n\t\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t\t) as any;\n\t\t};\n\t\treturn { as };\n\t};\n\n\twith(...queries: WithSubquery[]) {\n\t\tconst self = this;\n\n\t\tfunction select(): SingleStoreSelectBuilder<undefined, never, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(\n\t\t\tfields: TSelection,\n\t\t): SingleStoreSelectBuilder<TSelection, never, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(\n\t\t\tfields?: TSelection,\n\t\t): SingleStoreSelectBuilder<TSelection | undefined, never, 'qb'> {\n\t\t\treturn new SingleStoreSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\twithList: queries,\n\t\t\t});\n\t\t}\n\n\t\tfunction selectDistinct(): SingleStoreSelectBuilder<undefined, never, 'qb'>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(\n\t\t\tfields: TSelection,\n\t\t): SingleStoreSelectBuilder<TSelection, never, 'qb'>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(\n\t\t\tfields?: TSelection,\n\t\t): SingleStoreSelectBuilder<TSelection | undefined, never, 'qb'> {\n\t\t\treturn new SingleStoreSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\twithList: queries,\n\t\t\t\tdistinct: true,\n\t\t\t});\n\t\t}\n\n\t\treturn { select, selectDistinct };\n\t}\n\n\tselect(): SingleStoreSelectBuilder<undefined, never, 'qb'>;\n\tselect<TSelection extends SelectedFields>(fields: TSelection): SingleStoreSelectBuilder<TSelection, never, 'qb'>;\n\tselect<TSelection extends SelectedFields>(\n\t\tfields?: TSelection,\n\t): SingleStoreSelectBuilder<TSelection | undefined, never, 'qb'> {\n\t\treturn new SingleStoreSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t});\n\t}\n\n\tselectDistinct(): SingleStoreSelectBuilder<undefined, never, 'qb'>;\n\tselectDistinct<TSelection extends SelectedFields>(\n\t\tfields: TSelection,\n\t): SingleStoreSelectBuilder<TSelection, never, 'qb'>;\n\tselectDistinct<TSelection extends SelectedFields>(\n\t\tfields?: TSelection,\n\t): SingleStoreSelectBuilder<TSelection | undefined, never, 'qb'> {\n\t\treturn new SingleStoreSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t\tdistinct: true,\n\t\t});\n\t}\n\n\t// Lazy load dialect to avoid circular dependency\n\tprivate getDialect() {\n\t\tif (!this.dialect) {\n\t\t\tthis.dialect = new SingleStoreDialect(this.dialectConfig);\n\t\t}\n\n\t\treturn this.dialect;\n\t}\n}\n"], "mappings": "AAAA,SAAS,YAAY,UAAU;AAE/B,SAAS,6BAA6B;AAEtC,SAAS,0BAA0B;AAGnC,SAAS,oBAAoB;AAC7B,SAAS,gCAAgC;AAGlC,MAAM,aAAa;AAAA,EACzB,QAAiB,UAAU,IAAY;AAAA,EAE/B;AAAA,EACA;AAAA,EAER,YAAY,SAAyD;AACpE,SAAK,UAAU,GAAG,SAAS,kBAAkB,IAAI,UAAU;AAC3D,SAAK,gBAAgB,GAAG,SAAS,kBAAkB,IAAI,SAAY;AAAA,EACpE;AAAA,EAEA,QAAqB,CAAC,OAAe,cAAiC;AACrE,UAAM,eAAe;AACrB,UAAM,KAAK,CACV,OAII;AACJ,UAAI,OAAO,OAAO,YAAY;AAC7B,aAAK,GAAG,YAAY;AAAA,MACrB;AAEA,aAAO,IAAI;AAAA,QACV,IAAI;AAAA,UACH,GAAG,OAAO;AAAA,UACV,cAAc,uBAAuB,KAAK,GAAG,kBAAkB,KAAK,CAAC,IAAI,CAAC;AAAA,UAC1E;AAAA,UACA;AAAA,QACD;AAAA,QACA,IAAI,sBAAsB,EAAE,OAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;AAAA,MACvF;AAAA,IACD;AACA,WAAO,EAAE,GAAG;AAAA,EACb;AAAA,EAEA,QAAQ,SAAyB;AAChC,UAAM,OAAO;AAMb,aAAS,OACR,QACgE;AAChE,aAAO,IAAI,yBAAyB;AAAA,QACnC,QAAQ,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,KAAK,WAAW;AAAA,QACzB,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAMA,aAAS,eACR,QACgE;AAChE,aAAO,IAAI,yBAAyB;AAAA,QACnC,QAAQ,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,KAAK,WAAW;AAAA,QACzB,UAAU;AAAA,QACV,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAEA,WAAO,EAAE,QAAQ,eAAe;AAAA,EACjC;AAAA,EAIA,OACC,QACgE;AAChE,WAAO,IAAI,yBAAyB;AAAA,MACnC,QAAQ,UAAU;AAAA,MAClB,SAAS;AAAA,MACT,SAAS,KAAK,WAAW;AAAA,IAC1B,CAAC;AAAA,EACF;AAAA,EAMA,eACC,QACgE;AAChE,WAAO,IAAI,yBAAyB;AAAA,MACnC,QAAQ,UAAU;AAAA,MAClB,SAAS;AAAA,MACT,SAAS,KAAK,WAAW;AAAA,MACzB,UAAU;AAAA,IACX,CAAC;AAAA,EACF;AAAA;AAAA,EAGQ,aAAa;AACpB,QAAI,CAAC,KAAK,SAAS;AAClB,WAAK,UAAU,IAAI,mBAAmB,KAAK,aAAa;AAAA,IACzD;AAEA,WAAO,KAAK;AAAA,EACb;AACD;", "names": []}