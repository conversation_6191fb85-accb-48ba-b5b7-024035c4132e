{"version": 3, "sources": ["../../../src/sqlite-core/query-builders/query-builder.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, SQL } from '~/sql/sql.ts';\nimport type { SQLiteDialectConfig } from '~/sqlite-core/dialect.ts';\nimport { SQLiteDialect, SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\nimport type { WithBuilder } from '~/sqlite-core/subquery.ts';\nimport { WithSubquery } from '~/subquery.ts';\nimport { SQLiteSelectBuilder } from './select.ts';\nimport type { SelectedFields } from './select.types.ts';\n\nexport class QueryBuilder {\n\tstatic readonly [entityKind]: string = 'SQLiteQueryBuilder';\n\n\tprivate dialect: SQLiteDialect | undefined;\n\tprivate dialectConfig: SQLiteDialectConfig | undefined;\n\n\tconstructor(dialect?: SQLiteDialect | SQLiteDialectConfig) {\n\t\tthis.dialect = is(dialect, SQLiteDialect) ? dialect : undefined;\n\t\tthis.dialectConfig = is(dialect, SQLiteDialect) ? undefined : dialect;\n\t}\n\n\t$with: WithBuilder = (alias: string, selection?: ColumnsSelection) => {\n\t\tconst queryBuilder = this;\n\t\tconst as = (\n\t\t\tqb:\n\t\t\t\t| TypedQueryBuilder<ColumnsSelection | undefined>\n\t\t\t\t| SQL\n\t\t\t\t| ((qb: QueryBuilder) => TypedQueryBuilder<ColumnsSelection | undefined> | SQL),\n\t\t) => {\n\t\t\tif (typeof qb === 'function') {\n\t\t\t\tqb = qb(queryBuilder);\n\t\t\t}\n\n\t\t\treturn new Proxy(\n\t\t\t\tnew WithSubquery(\n\t\t\t\t\tqb.getSQL(),\n\t\t\t\t\tselection ?? ('getSelectedFields' in qb ? qb.getSelectedFields() ?? {} : {}) as SelectedFields,\n\t\t\t\t\talias,\n\t\t\t\t\ttrue,\n\t\t\t\t),\n\t\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t\t) as any;\n\t\t};\n\t\treturn { as };\n\t};\n\n\twith(...queries: WithSubquery[]) {\n\t\tconst self = this;\n\n\t\tfunction select(): SQLiteSelectBuilder<undefined, 'sync', void, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(\n\t\t\tfields: TSelection,\n\t\t): SQLiteSelectBuilder<TSelection, 'sync', void, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(\n\t\t\tfields?: TSelection,\n\t\t): SQLiteSelectBuilder<TSelection | undefined, 'sync', void, 'qb'> {\n\t\t\treturn new SQLiteSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\twithList: queries,\n\t\t\t});\n\t\t}\n\n\t\tfunction selectDistinct(): SQLiteSelectBuilder<undefined, 'sync', void, 'qb'>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(\n\t\t\tfields: TSelection,\n\t\t): SQLiteSelectBuilder<TSelection, 'sync', void, 'qb'>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(\n\t\t\tfields?: TSelection,\n\t\t): SQLiteSelectBuilder<TSelection | undefined, 'sync', void, 'qb'> {\n\t\t\treturn new SQLiteSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\twithList: queries,\n\t\t\t\tdistinct: true,\n\t\t\t});\n\t\t}\n\n\t\treturn { select, selectDistinct };\n\t}\n\n\tselect(): SQLiteSelectBuilder<undefined, 'sync', void, 'qb'>;\n\tselect<TSelection extends SelectedFields>(\n\t\tfields: TSelection,\n\t): SQLiteSelectBuilder<TSelection, 'sync', void, 'qb'>;\n\tselect<TSelection extends SelectedFields>(\n\t\tfields?: TSelection,\n\t): SQLiteSelectBuilder<TSelection | undefined, 'sync', void, 'qb'> {\n\t\treturn new SQLiteSelectBuilder({ fields: fields ?? undefined, session: undefined, dialect: this.getDialect() });\n\t}\n\n\tselectDistinct(): SQLiteSelectBuilder<undefined, 'sync', void, 'qb'>;\n\tselectDistinct<TSelection extends SelectedFields>(\n\t\tfields: TSelection,\n\t): SQLiteSelectBuilder<TSelection, 'sync', void, 'qb'>;\n\tselectDistinct<TSelection extends SelectedFields>(\n\t\tfields?: TSelection,\n\t): SQLiteSelectBuilder<TSelection | undefined, 'sync', void, 'qb'> {\n\t\treturn new SQLiteSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t\tdistinct: true,\n\t\t});\n\t}\n\n\t// Lazy load dialect to avoid circular dependency\n\tprivate getDialect() {\n\t\tif (!this.dialect) {\n\t\t\tthis.dialect = new SQLiteSyncDialect(this.dialectConfig);\n\t\t}\n\n\t\treturn this.dialect;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA+B;AAE/B,6BAAsC;AAGtC,qBAAiD;AAEjD,sBAA6B;AAC7B,oBAAoC;AAG7B,MAAM,aAAa;AAAA,EACzB,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA,EACA;AAAA,EAER,YAAY,SAA+C;AAC1D,SAAK,cAAU,kBAAG,SAAS,4BAAa,IAAI,UAAU;AACtD,SAAK,oBAAgB,kBAAG,SAAS,4BAAa,IAAI,SAAY;AAAA,EAC/D;AAAA,EAEA,QAAqB,CAAC,OAAe,cAAiC;AACrE,UAAM,eAAe;AACrB,UAAM,KAAK,CACV,OAII;AACJ,UAAI,OAAO,OAAO,YAAY;AAC7B,aAAK,GAAG,YAAY;AAAA,MACrB;AAEA,aAAO,IAAI;AAAA,QACV,IAAI;AAAA,UACH,GAAG,OAAO;AAAA,UACV,cAAc,uBAAuB,KAAK,GAAG,kBAAkB,KAAK,CAAC,IAAI,CAAC;AAAA,UAC1E;AAAA,UACA;AAAA,QACD;AAAA,QACA,IAAI,6CAAsB,EAAE,OAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;AAAA,MACvF;AAAA,IACD;AACA,WAAO,EAAE,GAAG;AAAA,EACb;AAAA,EAEA,QAAQ,SAAyB;AAChC,UAAM,OAAO;AAMb,aAAS,OACR,QACkE;AAClE,aAAO,IAAI,kCAAoB;AAAA,QAC9B,QAAQ,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,KAAK,WAAW;AAAA,QACzB,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAMA,aAAS,eACR,QACkE;AAClE,aAAO,IAAI,kCAAoB;AAAA,QAC9B,QAAQ,UAAU;AAAA,QAClB,SAAS;AAAA,QACT,SAAS,KAAK,WAAW;AAAA,QACzB,UAAU;AAAA,QACV,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAEA,WAAO,EAAE,QAAQ,eAAe;AAAA,EACjC;AAAA,EAMA,OACC,QACkE;AAClE,WAAO,IAAI,kCAAoB,EAAE,QAAQ,UAAU,QAAW,SAAS,QAAW,SAAS,KAAK,WAAW,EAAE,CAAC;AAAA,EAC/G;AAAA,EAMA,eACC,QACkE;AAClE,WAAO,IAAI,kCAAoB;AAAA,MAC9B,QAAQ,UAAU;AAAA,MAClB,SAAS;AAAA,MACT,SAAS,KAAK,WAAW;AAAA,MACzB,UAAU;AAAA,IACX,CAAC;AAAA,EACF;AAAA;AAAA,EAGQ,aAAa;AACpB,QAAI,CAAC,KAAK,SAAS;AAClB,WAAK,UAAU,IAAI,iCAAkB,KAAK,aAAa;AAAA,IACxD;AAEA,WAAO,KAAK;AAAA,EACb;AACD;", "names": []}