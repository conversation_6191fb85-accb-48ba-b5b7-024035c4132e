/**
 * Database Schema Definition using Drizzle ORM
 * 
 * This file defines the database schema using Drizzle ORM for type-safe database operations.
 * It follows proper ORM patterns and provides excellent TypeScript integration.
 * 
 * Following DRY principle: Single schema definition
 * Following YAGNI principle: Only essential tables and relationships
 */

import { sqliteTable, text, integer, real, index } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

/**
 * Users table - Stores user authentication and profile information
 * 
 * Following proper ORM patterns: Clear table definition with constraints
 */
export const users = sqliteTable('users', {
  id: text('id').primaryKey(),
  email: text('email').notNull().unique(),
  name: text('name').notNull(),
  passwordHash: text('password_hash').notNull(),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  emailIdx: index('users_email_idx').on(table.email),
}));

/**
 * Companies table - Stores company information
 * Each user can have multiple companies
 * 
 * Following proper ORM patterns: Foreign key relationships
 */
export const companies = sqliteTable('companies', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description'),
  userId: text('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  userIdIdx: index('companies_user_id_idx').on(table.userId),
  nameIdx: index('companies_name_idx').on(table.name),
}));

/**
 * Financial Years table - Stores financial year periods for companies
 * Each company can have multiple financial years
 * 
 * Following proper ORM patterns: Boolean and date fields with constraints
 */
export const financialYears = sqliteTable('financial_years', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  startDate: text('start_date').notNull(), // ISO date string
  endDate: text('end_date').notNull(), // ISO date string
  isActive: integer('is_active', { mode: 'boolean' }).default(false).notNull(),
  companyId: text('company_id').notNull().references(() => companies.id, { onDelete: 'cascade' }),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  companyIdIdx: index('financial_years_company_id_idx').on(table.companyId),
  activeIdx: index('financial_years_active_idx').on(table.isActive),
}));

/**
 * Account Types table - Predefined account categories
 * These are the standard accounting categories (Asset, Liability, etc.)
 * 
 * Following proper ORM patterns: Enum-like constraints
 */
export const accountTypes = sqliteTable('account_types', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  category: text('category').notNull(), // ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE
  description: text('description'),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  categoryIdx: index('account_types_category_idx').on(table.category),
}));

/**
 * Accounts table - Chart of accounts for each company/financial year
 * These are the actual accounts used for recording transactions
 * 
 * Following proper ORM patterns: Self-referencing foreign keys for hierarchy
 */
export const accounts = sqliteTable('accounts', {
  id: text('id').primaryKey(),
  code: text('code').notNull(),
  name: text('name').notNull(),
  accountTypeId: text('account_type_id').notNull().references(() => accountTypes.id),
  parentAccountId: text('parent_account_id').references(() => accounts.id),
  isActive: integer('is_active', { mode: 'boolean' }).default(true).notNull(),
  companyId: text('company_id').notNull().references(() => companies.id, { onDelete: 'cascade' }),
  financialYearId: text('financial_year_id').notNull().references(() => financialYears.id, { onDelete: 'cascade' }),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  companyIdIdx: index('accounts_company_id_idx').on(table.companyId),
  financialYearIdIdx: index('accounts_financial_year_id_idx').on(table.financialYearId),
  codeIdx: index('accounts_code_idx').on(table.code),
  accountTypeIdIdx: index('accounts_account_type_id_idx').on(table.accountTypeId),
  // Unique constraint for account codes within company/year
  uniqueCodeIdx: index('accounts_unique_code_idx').on(table.companyId, table.financialYearId, table.code),
}));

/**
 * Journal Entries table - Main transaction records
 * Each journal entry represents a complete accounting transaction
 * 
 * Following proper ORM patterns: Decimal fields for monetary amounts
 */
export const journalEntries = sqliteTable('journal_entries', {
  id: text('id').primaryKey(),
  entryNumber: text('entry_number').notNull(),
  date: text('date').notNull(), // ISO date string
  description: text('description').notNull(),
  reference: text('reference'),
  totalDebit: real('total_debit').default(0).notNull(),
  totalCredit: real('total_credit').default(0).notNull(),
  status: text('status').default('DRAFT').notNull(), // DRAFT, POSTED, REVERSED
  companyId: text('company_id').notNull().references(() => companies.id, { onDelete: 'cascade' }),
  financialYearId: text('financial_year_id').notNull().references(() => financialYears.id, { onDelete: 'cascade' }),
  createdBy: text('created_by').notNull().references(() => users.id),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  companyIdIdx: index('journal_entries_company_id_idx').on(table.companyId),
  financialYearIdIdx: index('journal_entries_financial_year_id_idx').on(table.financialYearId),
  statusIdx: index('journal_entries_status_idx').on(table.status),
  dateIdx: index('journal_entries_date_idx').on(table.date),
  entryNumberIdx: index('journal_entries_entry_number_idx').on(table.entryNumber),
  // Unique constraint for entry numbers within company/year
  uniqueEntryNumberIdx: index('journal_entries_unique_entry_number_idx').on(table.companyId, table.financialYearId, table.entryNumber),
}));

/**
 * Journal Entry Lines table - Individual debit/credit lines
 * Each journal entry consists of multiple lines that must balance
 * 
 * Following proper ORM patterns: Proper line ordering and constraints
 */
export const journalEntryLines = sqliteTable('journal_entry_lines', {
  id: text('id').primaryKey(),
  journalEntryId: text('journal_entry_id').notNull().references(() => journalEntries.id, { onDelete: 'cascade' }),
  accountId: text('account_id').notNull().references(() => accounts.id),
  description: text('description'),
  debitAmount: real('debit_amount').default(0).notNull(),
  creditAmount: real('credit_amount').default(0).notNull(),
  lineNumber: integer('line_number').notNull(),
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  journalEntryIdIdx: index('journal_entry_lines_journal_entry_id_idx').on(table.journalEntryId),
  accountIdIdx: index('journal_entry_lines_account_id_idx').on(table.accountId),
  lineNumberIdx: index('journal_entry_lines_line_number_idx').on(table.lineNumber),
  // Unique constraint for line numbers within journal entry
  uniqueLineNumberIdx: index('journal_entry_lines_unique_line_number_idx').on(table.journalEntryId, table.lineNumber),
}));

/**
 * Type definitions for database operations
 * These provide type safety for insert, select, and update operations
 * 
 * Following proper ORM patterns: Type-safe database operations
 */

// Insert types (for creating new records)
export type InsertUser = typeof users.$inferInsert;
export type InsertCompany = typeof companies.$inferInsert;
export type InsertFinancialYear = typeof financialYears.$inferInsert;
export type InsertAccountType = typeof accountTypes.$inferInsert;
export type InsertAccount = typeof accounts.$inferInsert;
export type InsertJournalEntry = typeof journalEntries.$inferInsert;
export type InsertJournalEntryLine = typeof journalEntryLines.$inferInsert;

// Select types (for reading records)
export type SelectUser = typeof users.$inferSelect;
export type SelectCompany = typeof companies.$inferSelect;
export type SelectFinancialYear = typeof financialYears.$inferSelect;
export type SelectAccountType = typeof accountTypes.$inferSelect;
export type SelectAccount = typeof accounts.$inferSelect;
export type SelectJournalEntry = typeof journalEntries.$inferSelect;
export type SelectJournalEntryLine = typeof journalEntryLines.$inferSelect;

/**
 * Account category enum for type safety
 * 
 * Following proper ORM patterns: Type-safe enums
 */
export const AccountCategory = {
  ASSET: 'ASSET',
  LIABILITY: 'LIABILITY',
  EQUITY: 'EQUITY',
  REVENUE: 'REVENUE',
  EXPENSE: 'EXPENSE',
} as const;

export type AccountCategoryType = typeof AccountCategory[keyof typeof AccountCategory];

/**
 * Journal entry status enum for type safety
 * 
 * Following proper ORM patterns: Type-safe enums
 */
export const JournalEntryStatus = {
  DRAFT: 'DRAFT',
  POSTED: 'POSTED',
  REVERSED: 'REVERSED',
} as const;

export type JournalEntryStatusType = typeof JournalEntryStatus[keyof typeof JournalEntryStatus];

/**
 * Default account types data
 * These will be inserted during database initialization
 * 
 * Following DRY principle: Centralized default data
 */
export const defaultAccountTypes: InsertAccountType[] = [
  {
    id: 'current-assets',
    name: 'Current Assets',
    category: AccountCategory.ASSET,
    description: 'Assets that can be converted to cash within one year',
  },
  {
    id: 'fixed-assets',
    name: 'Fixed Assets',
    category: AccountCategory.ASSET,
    description: 'Long-term assets used in business operations',
  },
  {
    id: 'current-liabilities',
    name: 'Current Liabilities',
    category: AccountCategory.LIABILITY,
    description: 'Debts due within one year',
  },
  {
    id: 'long-term-liabilities',
    name: 'Long-term Liabilities',
    category: AccountCategory.LIABILITY,
    description: 'Debts due after one year',
  },
  {
    id: 'owners-equity',
    name: 'Owner\'s Equity',
    category: AccountCategory.EQUITY,
    description: 'Owner\'s stake in the business',
  },
  {
    id: 'revenue',
    name: 'Revenue',
    category: AccountCategory.REVENUE,
    description: 'Income from business operations',
  },
  {
    id: 'operating-expenses',
    name: 'Operating Expenses',
    category: AccountCategory.EXPENSE,
    description: 'Costs of running the business',
  },
  {
    id: 'other-income',
    name: 'Other Income',
    category: AccountCategory.REVENUE,
    description: 'Non-operating income',
  },
  {
    id: 'other-expenses',
    name: 'Other Expenses',
    category: AccountCategory.EXPENSE,
    description: 'Non-operating expenses',
  },
];
