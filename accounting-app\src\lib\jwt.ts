/**
 * JWT Authentication Utilities
 * 
 * This file provides secure JWT token management with proper encryption,
 * validation, and HTTP-only cookie handling for maximum security.
 * 
 * Following DRY principle: Single JWT implementation
 * Following YAGNI principle: Only essential JWT features
 */

import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

/**
 * JWT configuration constants
 */
export const JWT_CONFIG = {
  // Use environment variables with secure defaults
  SECRET: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
  REFRESH_SECRET: process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key-change-in-production',
  
  // Token expiration times
  ACCESS_TOKEN_EXPIRES_IN: '15m',  // Short-lived access token
  REFRESH_TOKEN_EXPIRES_IN: '7d',  // Longer-lived refresh token
  
  // Cookie configuration
  COOKIE_NAME: 'auth-token',
  REFRESH_COOKIE_NAME: 'refresh-token',
  COOKIE_MAX_AGE: 15 * 60, // 15 minutes in seconds
  REFRESH_COOKIE_MAX_AGE: 7 * 24 * 60 * 60, // 7 days in seconds
  
  // Security settings
  COOKIE_SECURE: process.env.NODE_ENV === 'production',
  COOKIE_SAME_SITE: 'strict' as const,
  COOKIE_HTTP_ONLY: true,
} as const;

/**
 * JWT payload interface
 */
export interface JWTPayload {
  userId: string;
  email: string;
  name: string;
  iat?: number;
  exp?: number;
}

/**
 * Token pair interface
 */
export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

/**
 * Generate JWT access token
 * 
 * @param payload - User data to encode in token
 * @returns Signed JWT token
 */
export function generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  try {
    return jwt.sign(
      payload,
      JWT_CONFIG.SECRET,
      {
        expiresIn: JWT_CONFIG.ACCESS_TOKEN_EXPIRES_IN,
        issuer: 'accounting-app',
        audience: 'accounting-app-users',
      }
    );
  } catch (error) {
    console.error('Error generating access token:', error);
    throw new Error('Failed to generate access token');
  }
}

/**
 * Generate JWT refresh token
 * 
 * @param payload - User data to encode in token
 * @returns Signed JWT refresh token
 */
export function generateRefreshToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  try {
    return jwt.sign(
      { userId: payload.userId }, // Only store user ID in refresh token
      JWT_CONFIG.REFRESH_SECRET,
      {
        expiresIn: JWT_CONFIG.REFRESH_TOKEN_EXPIRES_IN,
        issuer: 'accounting-app',
        audience: 'accounting-app-refresh',
      }
    );
  } catch (error) {
    console.error('Error generating refresh token:', error);
    throw new Error('Failed to generate refresh token');
  }
}

/**
 * Generate both access and refresh tokens
 * 
 * @param payload - User data to encode in tokens
 * @returns Token pair
 */
export function generateTokenPair(payload: Omit<JWTPayload, 'iat' | 'exp'>): TokenPair {
  return {
    accessToken: generateAccessToken(payload),
    refreshToken: generateRefreshToken(payload),
  };
}

/**
 * Verify JWT access token
 * 
 * @param token - JWT token to verify
 * @returns Decoded payload or null if invalid
 */
export function verifyAccessToken(token: string): JWTPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_CONFIG.SECRET, {
      issuer: 'accounting-app',
      audience: 'accounting-app-users',
    }) as JWTPayload;
    
    return decoded;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      console.log('Access token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      console.log('Invalid access token');
    } else {
      console.error('Error verifying access token:', error);
    }
    return null;
  }
}

/**
 * Verify JWT refresh token
 * 
 * @param token - JWT refresh token to verify
 * @returns Decoded payload or null if invalid
 */
export function verifyRefreshToken(token: string): { userId: string } | null {
  try {
    const decoded = jwt.verify(token, JWT_CONFIG.REFRESH_SECRET, {
      issuer: 'accounting-app',
      audience: 'accounting-app-refresh',
    }) as { userId: string };
    
    return decoded;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      console.log('Refresh token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      console.log('Invalid refresh token');
    } else {
      console.error('Error verifying refresh token:', error);
    }
    return null;
  }
}

/**
 * Set authentication cookies in response
 * 
 * @param response - Next.js response object
 * @param tokens - Token pair to set as cookies
 */
export function setAuthCookies(response: NextResponse, tokens: TokenPair): void {
  // Set access token cookie
  response.cookies.set(JWT_CONFIG.COOKIE_NAME, tokens.accessToken, {
    httpOnly: JWT_CONFIG.COOKIE_HTTP_ONLY,
    secure: JWT_CONFIG.COOKIE_SECURE,
    sameSite: JWT_CONFIG.COOKIE_SAME_SITE,
    maxAge: JWT_CONFIG.COOKIE_MAX_AGE,
    path: '/',
  });

  // Set refresh token cookie
  response.cookies.set(JWT_CONFIG.REFRESH_COOKIE_NAME, tokens.refreshToken, {
    httpOnly: JWT_CONFIG.COOKIE_HTTP_ONLY,
    secure: JWT_CONFIG.COOKIE_SECURE,
    sameSite: JWT_CONFIG.COOKIE_SAME_SITE,
    maxAge: JWT_CONFIG.REFRESH_COOKIE_MAX_AGE,
    path: '/api/auth', // Restrict refresh token to auth endpoints
  });
}

/**
 * Clear authentication cookies
 * 
 * @param response - Next.js response object
 */
export function clearAuthCookies(response: NextResponse): void {
  // Clear access token cookie
  response.cookies.set(JWT_CONFIG.COOKIE_NAME, '', {
    httpOnly: JWT_CONFIG.COOKIE_HTTP_ONLY,
    secure: JWT_CONFIG.COOKIE_SECURE,
    sameSite: JWT_CONFIG.COOKIE_SAME_SITE,
    maxAge: 0,
    path: '/',
  });

  // Clear refresh token cookie
  response.cookies.set(JWT_CONFIG.REFRESH_COOKIE_NAME, '', {
    httpOnly: JWT_CONFIG.COOKIE_HTTP_ONLY,
    secure: JWT_CONFIG.COOKIE_SECURE,
    sameSite: JWT_CONFIG.COOKIE_SAME_SITE,
    maxAge: 0,
    path: '/api/auth',
  });
}

/**
 * Get authentication token from request
 * 
 * @param request - Next.js request object
 * @returns JWT token or null if not found
 */
export function getAuthToken(request: NextRequest): string | null {
  // Try to get token from cookie first
  const tokenFromCookie = request.cookies.get(JWT_CONFIG.COOKIE_NAME)?.value;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  // Fallback to Authorization header
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  return null;
}

/**
 * Get refresh token from request
 * 
 * @param request - Next.js request object
 * @returns Refresh token or null if not found
 */
export function getRefreshToken(request: NextRequest): string | null {
  return request.cookies.get(JWT_CONFIG.REFRESH_COOKIE_NAME)?.value || null;
}

/**
 * Get current user from request
 * 
 * @param request - Next.js request object
 * @returns User payload or null if not authenticated
 */
export function getCurrentUser(request: NextRequest): JWTPayload | null {
  const token = getAuthToken(request);
  if (!token) {
    return null;
  }

  return verifyAccessToken(token);
}

/**
 * Check if user is authenticated
 * 
 * @param request - Next.js request object
 * @returns True if user is authenticated
 */
export function isAuthenticated(request: NextRequest): boolean {
  return getCurrentUser(request) !== null;
}

/**
 * Server-side authentication check using cookies()
 * For use in Server Components and API routes
 * 
 * @returns User payload or null if not authenticated
 */
export async function getServerUser(): Promise<JWTPayload | null> {
  try {
    const cookieStore = cookies();
    const token = cookieStore.get(JWT_CONFIG.COOKIE_NAME)?.value;
    
    if (!token) {
      return null;
    }

    return verifyAccessToken(token);
  } catch (error) {
    console.error('Error getting server user:', error);
    return null;
  }
}

/**
 * Validate JWT configuration
 * Ensures all required environment variables are set
 */
export function validateJWTConfig(): void {
  const requiredEnvVars = ['JWT_SECRET', 'JWT_REFRESH_SECRET'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.warn(
      `⚠️  Missing JWT environment variables: ${missingVars.join(', ')}\n` +
      '   Using default values. Please set these in production!'
    );
  }

  if (process.env.NODE_ENV === 'production') {
    if (JWT_CONFIG.SECRET === 'your-super-secret-jwt-key-change-in-production') {
      throw new Error('JWT_SECRET must be set in production environment');
    }
    if (JWT_CONFIG.REFRESH_SECRET === 'your-super-secret-refresh-key-change-in-production') {
      throw new Error('JWT_REFRESH_SECRET must be set in production environment');
    }
  }
}

/**
 * Initialize JWT configuration
 * Call this at application startup
 */
export function initializeJWT(): void {
  validateJWTConfig();
  console.log('🔐 JWT authentication system initialized');
}
