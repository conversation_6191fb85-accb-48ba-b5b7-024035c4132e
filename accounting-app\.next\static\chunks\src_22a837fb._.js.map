{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Typography,\n  Divider,\n  Collapse,\n  Avatar,\n  Select,\n  MenuItem,\n  FormControl,\n  InputLabel,\n} from '@mui/material';\nimport {\n  Dashboard as DashboardIcon,\n  Business as BusinessIcon,\n  AccountBalance as AccountBalanceIcon,\n  Receipt as ReceiptIcon,\n  Assessment as AssessmentIcon,\n  Settings as SettingsIcon,\n  ExpandLess,\n  ExpandMore,\n  CalendarToday as CalendarIcon,\n  Add as AddIcon,\n} from '@mui/icons-material';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useApp } from '@/contexts/AppContext';\n\ninterface SidebarProps {\n  onClose?: () => void;\n}\n\nexport function Sidebar({ onClose }: SidebarProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const { user } = useAuth();\n  const { \n    companies, \n    currentCompany, \n    financialYears, \n    currentFinancialYear,\n    setCurrentCompany,\n    setCurrentFinancialYear \n  } = useApp();\n\n  const [openAccounting, setOpenAccounting] = useState(true);\n  const [openReports, setOpenReports] = useState(false);\n\n  const handleNavigation = (path: string) => {\n    router.push(path);\n    onClose?.();\n  };\n\n  const menuItems = [\n    {\n      text: 'Dashboard',\n      icon: <DashboardIcon />,\n      path: '/dashboard',\n    },\n    {\n      text: 'Companies',\n      icon: <BusinessIcon />,\n      path: '/companies',\n    },\n  ];\n\n  const accountingItems = [\n    {\n      text: 'Chart of Accounts',\n      icon: <AccountBalanceIcon />,\n      path: '/accounting/accounts',\n    },\n    {\n      text: 'Journal Entries',\n      icon: <ReceiptIcon />,\n      path: '/accounting/journal',\n    },\n    {\n      text: 'Ledger',\n      icon: <AccountBalanceIcon />,\n      path: '/accounting/ledger',\n    },\n  ];\n\n  const reportItems = [\n    {\n      text: 'Trial Balance',\n      icon: <AssessmentIcon />,\n      path: '/reports/trial-balance',\n    },\n    {\n      text: 'Profit & Loss',\n      icon: <AssessmentIcon />,\n      path: '/reports/profit-loss',\n    },\n    {\n      text: 'Balance Sheet',\n      icon: <AssessmentIcon />,\n      path: '/reports/balance-sheet',\n    },\n  ];\n\n  return (\n    <Box sx={{ height: '100%', backgroundColor: '#2c3e50', color: '#ecf0f1' }}>\n      {/* Logo/Brand */}\n      <Box sx={{ p: 2, textAlign: 'center', borderBottom: '1px solid #34495e' }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 'bold', color: '#3498db' }}>\n          AccountingApp\n        </Typography>\n      </Box>\n\n      {/* User Info */}\n      {user && (\n        <Box sx={{ p: 2, borderBottom: '1px solid #34495e' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n            <Avatar sx={{ bgcolor: '#3498db', mr: 2 }}>\n              {user.name.charAt(0).toUpperCase()}\n            </Avatar>\n            <Box>\n              <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold' }}>\n                {user.name}\n              </Typography>\n              <Typography variant=\"caption\" sx={{ color: '#bdc3c7' }}>\n                {user.email}\n              </Typography>\n            </Box>\n          </Box>\n\n          {/* Company Selector */}\n          {companies.length > 0 && (\n            <FormControl fullWidth size=\"small\" sx={{ mb: 1 }}>\n              <InputLabel sx={{ color: '#bdc3c7' }}>Company</InputLabel>\n              <Select\n                value={currentCompany?.id || ''}\n                onChange={(e) => {\n                  const company = companies.find(c => c.id === e.target.value);\n                  setCurrentCompany(company || null);\n                }}\n                sx={{\n                  color: '#ecf0f1',\n                  '& .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#34495e',\n                  },\n                  '&:hover .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#3498db',\n                  },\n                }}\n              >\n                {companies.map((company) => (\n                  <MenuItem key={company.id} value={company.id}>\n                    {company.name}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          )}\n\n          {/* Financial Year Selector */}\n          {financialYears.length > 0 && (\n            <FormControl fullWidth size=\"small\">\n              <InputLabel sx={{ color: '#bdc3c7' }}>Financial Year</InputLabel>\n              <Select\n                value={currentFinancialYear?.id || ''}\n                onChange={(e) => {\n                  const year = financialYears.find(y => y.id === e.target.value);\n                  setCurrentFinancialYear(year || null);\n                }}\n                sx={{\n                  color: '#ecf0f1',\n                  '& .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#34495e',\n                  },\n                  '&:hover .MuiOutlinedInput-notchedOutline': {\n                    borderColor: '#3498db',\n                  },\n                }}\n              >\n                {financialYears.map((year) => (\n                  <MenuItem key={year.id} value={year.id}>\n                    {year.name}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          )}\n        </Box>\n      )}\n\n      {/* Navigation Menu */}\n      <List sx={{ flexGrow: 1 }}>\n        {menuItems.map((item) => (\n          <ListItem key={item.text} disablePadding>\n            <ListItemButton\n              onClick={() => handleNavigation(item.path)}\n              selected={pathname === item.path}\n              sx={{\n                '&.Mui-selected': {\n                  backgroundColor: '#3498db',\n                  '&:hover': {\n                    backgroundColor: '#2980b9',\n                  },\n                },\n                '&:hover': {\n                  backgroundColor: '#34495e',\n                },\n              }}\n            >\n              <ListItemIcon sx={{ color: 'inherit' }}>\n                {item.icon}\n              </ListItemIcon>\n              <ListItemText primary={item.text} />\n            </ListItemButton>\n          </ListItem>\n        ))}\n\n        <Divider sx={{ my: 1, borderColor: '#34495e' }} />\n\n        {/* Accounting Section */}\n        <ListItem disablePadding>\n          <ListItemButton\n            onClick={() => setOpenAccounting(!openAccounting)}\n            sx={{\n              '&:hover': {\n                backgroundColor: '#34495e',\n              },\n            }}\n          >\n            <ListItemIcon sx={{ color: 'inherit' }}>\n              <AccountBalanceIcon />\n            </ListItemIcon>\n            <ListItemText primary=\"Accounting\" />\n            {openAccounting ? <ExpandLess /> : <ExpandMore />}\n          </ListItemButton>\n        </ListItem>\n        <Collapse in={openAccounting} timeout=\"auto\" unmountOnExit>\n          <List component=\"div\" disablePadding>\n            {accountingItems.map((item) => (\n              <ListItem key={item.text} disablePadding>\n                <ListItemButton\n                  onClick={() => handleNavigation(item.path)}\n                  selected={pathname === item.path}\n                  sx={{\n                    pl: 4,\n                    '&.Mui-selected': {\n                      backgroundColor: '#3498db',\n                      '&:hover': {\n                        backgroundColor: '#2980b9',\n                      },\n                    },\n                    '&:hover': {\n                      backgroundColor: '#34495e',\n                    },\n                  }}\n                >\n                  <ListItemIcon sx={{ color: 'inherit', minWidth: 36 }}>\n                    {item.icon}\n                  </ListItemIcon>\n                  <ListItemText primary={item.text} />\n                </ListItemButton>\n              </ListItem>\n            ))}\n          </List>\n        </Collapse>\n\n        {/* Reports Section */}\n        <ListItem disablePadding>\n          <ListItemButton\n            onClick={() => setOpenReports(!openReports)}\n            sx={{\n              '&:hover': {\n                backgroundColor: '#34495e',\n              },\n            }}\n          >\n            <ListItemIcon sx={{ color: 'inherit' }}>\n              <AssessmentIcon />\n            </ListItemIcon>\n            <ListItemText primary=\"Reports\" />\n            {openReports ? <ExpandLess /> : <ExpandMore />}\n          </ListItemButton>\n        </ListItem>\n        <Collapse in={openReports} timeout=\"auto\" unmountOnExit>\n          <List component=\"div\" disablePadding>\n            {reportItems.map((item) => (\n              <ListItem key={item.text} disablePadding>\n                <ListItemButton\n                  onClick={() => handleNavigation(item.path)}\n                  selected={pathname === item.path}\n                  sx={{\n                    pl: 4,\n                    '&.Mui-selected': {\n                      backgroundColor: '#3498db',\n                      '&:hover': {\n                        backgroundColor: '#2980b9',\n                      },\n                    },\n                    '&:hover': {\n                      backgroundColor: '#34495e',\n                    },\n                  }}\n                >\n                  <ListItemIcon sx={{ color: 'inherit', minWidth: 36 }}>\n                    {item.icon}\n                  </ListItemIcon>\n                  <ListItemText primary={item.text} />\n                </ListItemButton>\n              </ListItem>\n            ))}\n          </List>\n        </Collapse>\n\n        <Divider sx={{ my: 1, borderColor: '#34495e' }} />\n\n        {/* Settings */}\n        <ListItem disablePadding>\n          <ListItemButton\n            onClick={() => handleNavigation('/settings')}\n            selected={pathname === '/settings'}\n            sx={{\n              '&.Mui-selected': {\n                backgroundColor: '#3498db',\n                '&:hover': {\n                  backgroundColor: '#2980b9',\n                },\n              },\n              '&:hover': {\n                backgroundColor: '#34495e',\n              },\n            }}\n          >\n            <ListItemIcon sx={{ color: 'inherit' }}>\n              <SettingsIcon />\n            </ListItemIcon>\n            <ListItemText primary=\"Settings\" />\n          </ListItemButton>\n        </ListItem>\n      </List>\n    </Box>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;;;AAjCA;;;;;;;;;;;;;;AAuCO,SAAS,QAAQ,EAAE,OAAO,EAAgB;;IAC/C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EACJ,SAAS,EACT,cAAc,EACd,cAAc,EACd,oBAAoB,EACpB,iBAAiB,EACjB,uBAAuB,EACxB,GAAG,CAAA,GAAA,iIAAA,CAAA,SAAM,AAAD;IAET,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,mBAAmB,CAAC;QACxB,OAAO,IAAI,CAAC;QACZ;IACF;IAEA,MAAM,YAAY;QAChB;YACE,MAAM;YACN,oBAAM,6LAAC,iKAAA,CAAA,UAAa;;;;;YACpB,MAAM;QACR;QACA;YACE,MAAM;YACN,oBAAM,6LAAC,gKAAA,CAAA,UAAY;;;;;YACnB,MAAM;QACR;KACD;IAED,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,oBAAM,6LAAC,sKAAA,CAAA,UAAkB;;;;;YACzB,MAAM;QACR;QACA;YACE,MAAM;YACN,oBAAM,6LAAC,+JAAA,CAAA,UAAW;;;;;YAClB,MAAM;QACR;QACA;YACE,MAAM;YACN,oBAAM,6LAAC,sKAAA,CAAA,UAAkB;;;;;YACzB,MAAM;QACR;KACD;IAED,MAAM,cAAc;QAClB;YACE,MAAM;YACN,oBAAM,6LAAC,kKAAA,CAAA,UAAc;;;;;YACrB,MAAM;QACR;QACA;YACE,MAAM;YACN,oBAAM,6LAAC,kKAAA,CAAA,UAAc;;;;;YACrB,MAAM;QACR;QACA;YACE,MAAM;YACN,oBAAM,6LAAC,kKAAA,CAAA,UAAc;;;;;YACrB,MAAM;QACR;KACD;IAED,qBACE,6LAAC,2LAAA,CAAA,MAAG;QAAC,IAAI;YAAE,QAAQ;YAAQ,iBAAiB;YAAW,OAAO;QAAU;;0BAEtE,6LAAC,2LAAA,CAAA,MAAG;gBAAC,IAAI;oBAAE,GAAG;oBAAG,WAAW;oBAAU,cAAc;gBAAoB;0BACtE,cAAA,6LAAC,gNAAA,CAAA,aAAU;oBAAC,SAAQ;oBAAK,IAAI;wBAAE,YAAY;wBAAQ,OAAO;oBAAU;8BAAG;;;;;;;;;;;YAMxE,sBACC,6LAAC,2LAAA,CAAA,MAAG;gBAAC,IAAI;oBAAE,GAAG;oBAAG,cAAc;gBAAoB;;kCACjD,6LAAC,2LAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,IAAI;wBAAE;;0CACtD,6LAAC,oMAAA,CAAA,SAAM;gCAAC,IAAI;oCAAE,SAAS;oCAAW,IAAI;gCAAE;0CACrC,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;0CAElC,6LAAC,2LAAA,CAAA,MAAG;;kDACF,6LAAC,gNAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAY,IAAI;4CAAE,YAAY;wCAAO;kDACtD,KAAK,IAAI;;;;;;kDAEZ,6LAAC,gNAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAU,IAAI;4CAAE,OAAO;wCAAU;kDAClD,KAAK,KAAK;;;;;;;;;;;;;;;;;;oBAMhB,UAAU,MAAM,GAAG,mBAClB,6LAAC,mNAAA,CAAA,cAAW;wBAAC,SAAS;wBAAC,MAAK;wBAAQ,IAAI;4BAAE,IAAI;wBAAE;;0CAC9C,6LAAC,gNAAA,CAAA,aAAU;gCAAC,IAAI;oCAAE,OAAO;gCAAU;0CAAG;;;;;;0CACtC,6LAAC,oMAAA,CAAA,SAAM;gCACL,OAAO,gBAAgB,MAAM;gCAC7B,UAAU,CAAC;oCACT,MAAM,UAAU,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK;oCAC3D,kBAAkB,WAAW;gCAC/B;gCACA,IAAI;oCACF,OAAO;oCACP,sCAAsC;wCACpC,aAAa;oCACf;oCACA,4CAA4C;wCAC1C,aAAa;oCACf;gCACF;0CAEC,UAAU,GAAG,CAAC,CAAC,wBACd,6LAAC,0MAAA,CAAA,WAAQ;wCAAkB,OAAO,QAAQ,EAAE;kDACzC,QAAQ,IAAI;uCADA,QAAQ,EAAE;;;;;;;;;;;;;;;;oBAShC,eAAe,MAAM,GAAG,mBACvB,6LAAC,mNAAA,CAAA,cAAW;wBAAC,SAAS;wBAAC,MAAK;;0CAC1B,6LAAC,gNAAA,CAAA,aAAU;gCAAC,IAAI;oCAAE,OAAO;gCAAU;0CAAG;;;;;;0CACtC,6LAAC,oMAAA,CAAA,SAAM;gCACL,OAAO,sBAAsB,MAAM;gCACnC,UAAU,CAAC;oCACT,MAAM,OAAO,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK;oCAC7D,wBAAwB,QAAQ;gCAClC;gCACA,IAAI;oCACF,OAAO;oCACP,sCAAsC;wCACpC,aAAa;oCACf;oCACA,4CAA4C;wCAC1C,aAAa;oCACf;gCACF;0CAEC,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,0MAAA,CAAA,WAAQ;wCAAe,OAAO,KAAK,EAAE;kDACnC,KAAK,IAAI;uCADG,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAWlC,6LAAC,8LAAA,CAAA,OAAI;gBAAC,IAAI;oBAAE,UAAU;gBAAE;;oBACrB,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,0MAAA,CAAA,WAAQ;4BAAiB,cAAc;sCACtC,cAAA,6LAAC,4NAAA,CAAA,iBAAc;gCACb,SAAS,IAAM,iBAAiB,KAAK,IAAI;gCACzC,UAAU,aAAa,KAAK,IAAI;gCAChC,IAAI;oCACF,kBAAkB;wCAChB,iBAAiB;wCACjB,WAAW;4CACT,iBAAiB;wCACnB;oCACF;oCACA,WAAW;wCACT,iBAAiB;oCACnB;gCACF;;kDAEA,6LAAC,sNAAA,CAAA,eAAY;wCAAC,IAAI;4CAAE,OAAO;wCAAU;kDAClC,KAAK,IAAI;;;;;;kDAEZ,6LAAC,sNAAA,CAAA,eAAY;wCAAC,SAAS,KAAK,IAAI;;;;;;;;;;;;2BAnBrB,KAAK,IAAI;;;;;kCAwB1B,6LAAC,uMAAA,CAAA,UAAO;wBAAC,IAAI;4BAAE,IAAI;4BAAG,aAAa;wBAAU;;;;;;kCAG7C,6LAAC,0MAAA,CAAA,WAAQ;wBAAC,cAAc;kCACtB,cAAA,6LAAC,4NAAA,CAAA,iBAAc;4BACb,SAAS,IAAM,kBAAkB,CAAC;4BAClC,IAAI;gCACF,WAAW;oCACT,iBAAiB;gCACnB;4BACF;;8CAEA,6LAAC,sNAAA,CAAA,eAAY;oCAAC,IAAI;wCAAE,OAAO;oCAAU;8CACnC,cAAA,6LAAC,sKAAA,CAAA,UAAkB;;;;;;;;;;8CAErB,6LAAC,sNAAA,CAAA,eAAY;oCAAC,SAAQ;;;;;;gCACrB,+BAAiB,6LAAC,kKAAA,CAAA,UAAU;;;;yDAAM,6LAAC,kKAAA,CAAA,UAAU;;;;;;;;;;;;;;;;kCAGlD,6LAAC,0MAAA,CAAA,WAAQ;wBAAC,IAAI;wBAAgB,SAAQ;wBAAO,aAAa;kCACxD,cAAA,6LAAC,8LAAA,CAAA,OAAI;4BAAC,WAAU;4BAAM,cAAc;sCACjC,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,0MAAA,CAAA,WAAQ;oCAAiB,cAAc;8CACtC,cAAA,6LAAC,4NAAA,CAAA,iBAAc;wCACb,SAAS,IAAM,iBAAiB,KAAK,IAAI;wCACzC,UAAU,aAAa,KAAK,IAAI;wCAChC,IAAI;4CACF,IAAI;4CACJ,kBAAkB;gDAChB,iBAAiB;gDACjB,WAAW;oDACT,iBAAiB;gDACnB;4CACF;4CACA,WAAW;gDACT,iBAAiB;4CACnB;wCACF;;0DAEA,6LAAC,sNAAA,CAAA,eAAY;gDAAC,IAAI;oDAAE,OAAO;oDAAW,UAAU;gDAAG;0DAChD,KAAK,IAAI;;;;;;0DAEZ,6LAAC,sNAAA,CAAA,eAAY;gDAAC,SAAS,KAAK,IAAI;;;;;;;;;;;;mCApBrB,KAAK,IAAI;;;;;;;;;;;;;;;kCA4B9B,6LAAC,0MAAA,CAAA,WAAQ;wBAAC,cAAc;kCACtB,cAAA,6LAAC,4NAAA,CAAA,iBAAc;4BACb,SAAS,IAAM,eAAe,CAAC;4BAC/B,IAAI;gCACF,WAAW;oCACT,iBAAiB;gCACnB;4BACF;;8CAEA,6LAAC,sNAAA,CAAA,eAAY;oCAAC,IAAI;wCAAE,OAAO;oCAAU;8CACnC,cAAA,6LAAC,kKAAA,CAAA,UAAc;;;;;;;;;;8CAEjB,6LAAC,sNAAA,CAAA,eAAY;oCAAC,SAAQ;;;;;;gCACrB,4BAAc,6LAAC,kKAAA,CAAA,UAAU;;;;yDAAM,6LAAC,kKAAA,CAAA,UAAU;;;;;;;;;;;;;;;;kCAG/C,6LAAC,0MAAA,CAAA,WAAQ;wBAAC,IAAI;wBAAa,SAAQ;wBAAO,aAAa;kCACrD,cAAA,6LAAC,8LAAA,CAAA,OAAI;4BAAC,WAAU;4BAAM,cAAc;sCACjC,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC,0MAAA,CAAA,WAAQ;oCAAiB,cAAc;8CACtC,cAAA,6LAAC,4NAAA,CAAA,iBAAc;wCACb,SAAS,IAAM,iBAAiB,KAAK,IAAI;wCACzC,UAAU,aAAa,KAAK,IAAI;wCAChC,IAAI;4CACF,IAAI;4CACJ,kBAAkB;gDAChB,iBAAiB;gDACjB,WAAW;oDACT,iBAAiB;gDACnB;4CACF;4CACA,WAAW;gDACT,iBAAiB;4CACnB;wCACF;;0DAEA,6LAAC,sNAAA,CAAA,eAAY;gDAAC,IAAI;oDAAE,OAAO;oDAAW,UAAU;gDAAG;0DAChD,KAAK,IAAI;;;;;;0DAEZ,6LAAC,sNAAA,CAAA,eAAY;gDAAC,SAAS,KAAK,IAAI;;;;;;;;;;;;mCApBrB,KAAK,IAAI;;;;;;;;;;;;;;;kCA2B9B,6LAAC,uMAAA,CAAA,UAAO;wBAAC,IAAI;4BAAE,IAAI;4BAAG,aAAa;wBAAU;;;;;;kCAG7C,6LAAC,0MAAA,CAAA,WAAQ;wBAAC,cAAc;kCACtB,cAAA,6LAAC,4NAAA,CAAA,iBAAc;4BACb,SAAS,IAAM,iBAAiB;4BAChC,UAAU,aAAa;4BACvB,IAAI;gCACF,kBAAkB;oCAChB,iBAAiB;oCACjB,WAAW;wCACT,iBAAiB;oCACnB;gCACF;gCACA,WAAW;oCACT,iBAAiB;gCACnB;4BACF;;8CAEA,6LAAC,sNAAA,CAAA,eAAY;oCAAC,IAAI;wCAAE,OAAO;oCAAU;8CACnC,cAAA,6LAAC,gKAAA,CAAA,UAAY;;;;;;;;;;8CAEf,6LAAC,sNAAA,CAAA,eAAY;oCAAC,SAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;GAnTgB;;QACC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACX,kIAAA,CAAA,UAAO;QAQpB,iIAAA,CAAA,SAAM;;;KAXI", "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  IconButton,\n  Menu,\n  MenuItem,\n  Avatar,\n  Typography,\n  Divider,\n  Badge,\n} from '@mui/material';\nimport {\n  Notifications as NotificationsIcon,\n  AccountCircle as AccountCircleIcon,\n  Settings as SettingsIcon,\n  Logout as LogoutIcon,\n} from '@mui/icons-material';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\n\nexport function Header() {\n  const { user, signOut } = useAuth();\n  const router = useRouter();\n  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\n  const [notificationAnchor, setNotificationAnchor] = useState<null | HTMLElement>(null);\n\n  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleProfileMenuClose = () => {\n    setAnchorEl(null);\n  };\n\n  const handleNotificationMenuOpen = (event: React.MouseEvent<HTMLElement>) => {\n    setNotificationAnchor(event.currentTarget);\n  };\n\n  const handleNotificationMenuClose = () => {\n    setNotificationAnchor(null);\n  };\n\n  const handleSignOut = async () => {\n    await signOut();\n    handleProfileMenuClose();\n    router.push('/auth/login');\n  };\n\n  const handleSettings = () => {\n    router.push('/settings');\n    handleProfileMenuClose();\n  };\n\n  const handleProfile = () => {\n    router.push('/profile');\n    handleProfileMenuClose();\n  };\n\n  if (!user) return null;\n\n  return (\n    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n      {/* Notifications */}\n      <IconButton\n        size=\"large\"\n        color=\"inherit\"\n        onClick={handleNotificationMenuOpen}\n      >\n        <Badge badgeContent={3} color=\"error\">\n          <NotificationsIcon />\n        </Badge>\n      </IconButton>\n\n      {/* Profile Menu */}\n      <IconButton\n        size=\"large\"\n        edge=\"end\"\n        onClick={handleProfileMenuOpen}\n        color=\"inherit\"\n        sx={{ ml: 1 }}\n      >\n        <Avatar sx={{ width: 32, height: 32, bgcolor: '#3498db' }}>\n          {user.name.charAt(0).toUpperCase()}\n        </Avatar>\n      </IconButton>\n\n      {/* Notification Menu */}\n      <Menu\n        anchorEl={notificationAnchor}\n        open={Boolean(notificationAnchor)}\n        onClose={handleNotificationMenuClose}\n        PaperProps={{\n          sx: { width: 300, maxHeight: 400 }\n        }}\n      >\n        <Box sx={{ p: 2 }}>\n          <Typography variant=\"h6\">Notifications</Typography>\n        </Box>\n        <Divider />\n        <MenuItem onClick={handleNotificationMenuClose}>\n          <Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n              New financial year created\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              2 minutes ago\n            </Typography>\n          </Box>\n        </MenuItem>\n        <MenuItem onClick={handleNotificationMenuClose}>\n          <Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n              Journal entry approved\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              1 hour ago\n            </Typography>\n          </Box>\n        </MenuItem>\n        <MenuItem onClick={handleNotificationMenuClose}>\n          <Box>\n            <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n              Monthly report generated\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              3 hours ago\n            </Typography>\n          </Box>\n        </MenuItem>\n      </Menu>\n\n      {/* Profile Menu */}\n      <Menu\n        anchorEl={anchorEl}\n        open={Boolean(anchorEl)}\n        onClose={handleProfileMenuClose}\n        PaperProps={{\n          sx: { width: 250 }\n        }}\n      >\n        <Box sx={{ p: 2 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n            <Avatar sx={{ bgcolor: '#3498db', mr: 2 }}>\n              {user.name.charAt(0).toUpperCase()}\n            </Avatar>\n            <Box>\n              <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold' }}>\n                {user.name}\n              </Typography>\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                {user.email}\n              </Typography>\n            </Box>\n          </Box>\n        </Box>\n        <Divider />\n        <MenuItem onClick={handleProfile}>\n          <AccountCircleIcon sx={{ mr: 2 }} />\n          Profile\n        </MenuItem>\n        <MenuItem onClick={handleSettings}>\n          <SettingsIcon sx={{ mr: 2 }} />\n          Settings\n        </MenuItem>\n        <Divider />\n        <MenuItem onClick={handleSignOut}>\n          <LogoutIcon sx={{ mr: 2 }} />\n          Sign Out\n        </MenuItem>\n      </Menu>\n    </Box>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAAA;AAAA;AAAA;AAMA;AACA;;;AApBA;;;;;;;;;AAsBO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAEjF,MAAM,wBAAwB,CAAC;QAC7B,YAAY,MAAM,aAAa;IACjC;IAEA,MAAM,yBAAyB;QAC7B,YAAY;IACd;IAEA,MAAM,6BAA6B,CAAC;QAClC,sBAAsB,MAAM,aAAa;IAC3C;IAEA,MAAM,8BAA8B;QAClC,sBAAsB;IACxB;IAEA,MAAM,gBAAgB;QACpB,MAAM;QACN;QACA,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB;QACrB,OAAO,IAAI,CAAC;QACZ;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAO,IAAI,CAAC;QACZ;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC,2LAAA,CAAA,MAAG;QAAC,IAAI;YAAE,SAAS;YAAQ,YAAY;YAAU,KAAK;QAAE;;0BAEvD,6LAAC,gNAAA,CAAA,aAAU;gBACT,MAAK;gBACL,OAAM;gBACN,SAAS;0BAET,cAAA,6LAAC,iMAAA,CAAA,QAAK;oBAAC,cAAc;oBAAG,OAAM;8BAC5B,cAAA,6LAAC,qKAAA,CAAA,UAAiB;;;;;;;;;;;;;;;0BAKtB,6LAAC,gNAAA,CAAA,aAAU;gBACT,MAAK;gBACL,MAAK;gBACL,SAAS;gBACT,OAAM;gBACN,IAAI;oBAAE,IAAI;gBAAE;0BAEZ,cAAA,6LAAC,oMAAA,CAAA,SAAM;oBAAC,IAAI;wBAAE,OAAO;wBAAI,QAAQ;wBAAI,SAAS;oBAAU;8BACrD,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;0BAKpC,6LAAC,8LAAA,CAAA,OAAI;gBACH,UAAU;gBACV,MAAM,QAAQ;gBACd,SAAS;gBACT,YAAY;oBACV,IAAI;wBAAE,OAAO;wBAAK,WAAW;oBAAI;gBACnC;;kCAEA,6LAAC,2LAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE,GAAG;wBAAE;kCACd,cAAA,6LAAC,gNAAA,CAAA,aAAU;4BAAC,SAAQ;sCAAK;;;;;;;;;;;kCAE3B,6LAAC,uMAAA,CAAA,UAAO;;;;;kCACR,6LAAC,0MAAA,CAAA,WAAQ;wBAAC,SAAS;kCACjB,cAAA,6LAAC,2LAAA,CAAA,MAAG;;8CACF,6LAAC,gNAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAQ,IAAI;wCAAE,YAAY;oCAAO;8CAAG;;;;;;8CAGxD,6LAAC,gNAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAU,OAAM;8CAAiB;;;;;;;;;;;;;;;;;kCAKzD,6LAAC,0MAAA,CAAA,WAAQ;wBAAC,SAAS;kCACjB,cAAA,6LAAC,2LAAA,CAAA,MAAG;;8CACF,6LAAC,gNAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAQ,IAAI;wCAAE,YAAY;oCAAO;8CAAG;;;;;;8CAGxD,6LAAC,gNAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAU,OAAM;8CAAiB;;;;;;;;;;;;;;;;;kCAKzD,6LAAC,0MAAA,CAAA,WAAQ;wBAAC,SAAS;kCACjB,cAAA,6LAAC,2LAAA,CAAA,MAAG;;8CACF,6LAAC,gNAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAQ,IAAI;wCAAE,YAAY;oCAAO;8CAAG;;;;;;8CAGxD,6LAAC,gNAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAU,OAAM;8CAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAQ3D,6LAAC,8LAAA,CAAA,OAAI;gBACH,UAAU;gBACV,MAAM,QAAQ;gBACd,SAAS;gBACT,YAAY;oBACV,IAAI;wBAAE,OAAO;oBAAI;gBACnB;;kCAEA,6LAAC,2LAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE,GAAG;wBAAE;kCACd,cAAA,6LAAC,2LAAA,CAAA,MAAG;4BAAC,IAAI;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,IAAI;4BAAE;;8CACtD,6LAAC,oMAAA,CAAA,SAAM;oCAAC,IAAI;wCAAE,SAAS;wCAAW,IAAI;oCAAE;8CACrC,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;8CAElC,6LAAC,2LAAA,CAAA,MAAG;;sDACF,6LAAC,gNAAA,CAAA,aAAU;4CAAC,SAAQ;4CAAY,IAAI;gDAAE,YAAY;4CAAO;sDACtD,KAAK,IAAI;;;;;;sDAEZ,6LAAC,gNAAA,CAAA,aAAU;4CAAC,SAAQ;4CAAU,OAAM;sDACjC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;kCAKnB,6LAAC,uMAAA,CAAA,UAAO;;;;;kCACR,6LAAC,0MAAA,CAAA,WAAQ;wBAAC,SAAS;;0CACjB,6LAAC,qKAAA,CAAA,UAAiB;gCAAC,IAAI;oCAAE,IAAI;gCAAE;;;;;;4BAAK;;;;;;;kCAGtC,6LAAC,0MAAA,CAAA,WAAQ;wBAAC,SAAS;;0CACjB,6LAAC,gKAAA,CAAA,UAAY;gCAAC,IAAI;oCAAE,IAAI;gCAAE;;;;;;4BAAK;;;;;;;kCAGjC,6LAAC,uMAAA,CAAA,UAAO;;;;;kCACR,6LAAC,0MAAA,CAAA,WAAQ;wBAAC,SAAS;;0CACjB,6LAAC,8JAAA,CAAA,UAAU;gCAAC,IAAI;oCAAE,IAAI;gCAAE;;;;;;4BAAK;;;;;;;;;;;;;;;;;;;AAMvC;GAxJgB;;QACY,kIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KAFV", "debugId": null}}, {"offset": {"line": 1149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/src/components/layout/Breadcrumbs.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  Breadcrumbs as MuiBreadcrum<PERSON>,\n  Link,\n  Typography,\n  Box,\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  NavigateNext as NavigateNextIcon,\n} from '@mui/icons-material';\nimport { usePathname, useRouter } from 'next/navigation';\n\nexport function Breadcrumbs() {\n  const pathname = usePathname();\n  const router = useRouter();\n\n  // Generate breadcrumb items from pathname\n  const pathSegments = pathname.split('/').filter(Boolean);\n  \n  const breadcrumbItems = [\n    {\n      label: 'Home',\n      path: '/dashboard',\n      icon: <HomeIcon sx={{ mr: 0.5, fontSize: 16 }} />,\n    },\n  ];\n\n  // Map path segments to readable labels\n  const segmentLabels: { [key: string]: string } = {\n    dashboard: 'Dashboard',\n    companies: 'Companies',\n    accounting: 'Accounting',\n    accounts: 'Chart of Accounts',\n    journal: 'Journal Entries',\n    ledger: 'Ledger',\n    reports: 'Reports',\n    'trial-balance': 'Trial Balance',\n    'profit-loss': 'Profit & Loss',\n    'balance-sheet': 'Balance Sheet',\n    settings: 'Settings',\n    profile: 'Profile',\n    auth: 'Authentication',\n    login: 'Login',\n    register: 'Register',\n  };\n\n  // Build breadcrumb path\n  let currentPath = '';\n  pathSegments.forEach((segment, index) => {\n    currentPath += `/${segment}`;\n    const label = segmentLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);\n    \n    breadcrumbItems.push({\n      label,\n      path: currentPath,\n      icon: null,\n    });\n  });\n\n  // Don't show breadcrumbs on auth pages\n  if (pathname.startsWith('/auth')) {\n    return null;\n  }\n\n  return (\n    <Box sx={{ mb: 2 }}>\n      <MuiBreadcrumbs\n        separator={<NavigateNextIcon fontSize=\"small\" />}\n        aria-label=\"breadcrumb\"\n        sx={{\n          '& .MuiBreadcrumbs-separator': {\n            color: '#666',\n          },\n        }}\n      >\n        {breadcrumbItems.map((item, index) => {\n          const isLast = index === breadcrumbItems.length - 1;\n          \n          if (isLast) {\n            return (\n              <Typography\n                key={item.path}\n                color=\"text.primary\"\n                sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  fontWeight: 'bold',\n                }}\n              >\n                {item.icon}\n                {item.label}\n              </Typography>\n            );\n          }\n\n          return (\n            <Link\n              key={item.path}\n              underline=\"hover\"\n              color=\"inherit\"\n              onClick={() => router.push(item.path)}\n              sx={{\n                display: 'flex',\n                alignItems: 'center',\n                cursor: 'pointer',\n                '&:hover': {\n                  color: '#3498db',\n                },\n              }}\n            >\n              {item.icon}\n              {item.label}\n            </Link>\n          );\n        })}\n      </MuiBreadcrumbs>\n    </Box>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAMA;AAAA;AAIA;;;AAbA;;;;;AAeO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,0CAA0C;IAC1C,MAAM,eAAe,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAEhD,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,MAAM;YACN,oBAAM,6LAAC,4JAAA,CAAA,UAAQ;gBAAC,IAAI;oBAAE,IAAI;oBAAK,UAAU;gBAAG;;;;;;QAC9C;KACD;IAED,uCAAuC;IACvC,MAAM,gBAA2C;QAC/C,WAAW;QACX,WAAW;QACX,YAAY;QACZ,UAAU;QACV,SAAS;QACT,QAAQ;QACR,SAAS;QACT,iBAAiB;QACjB,eAAe;QACf,iBAAiB;QACjB,UAAU;QACV,SAAS;QACT,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IAEA,wBAAwB;IACxB,IAAI,cAAc;IAClB,aAAa,OAAO,CAAC,CAAC,SAAS;QAC7B,eAAe,CAAC,CAAC,EAAE,SAAS;QAC5B,MAAM,QAAQ,aAAa,CAAC,QAAQ,IAAI,QAAQ,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,KAAK,CAAC;QAExF,gBAAgB,IAAI,CAAC;YACnB;YACA,MAAM;YACN,MAAM;QACR;IACF;IAEA,uCAAuC;IACvC,IAAI,SAAS,UAAU,CAAC,UAAU;QAChC,OAAO;IACT;IAEA,qBACE,6LAAC,2LAAA,CAAA,MAAG;QAAC,IAAI;YAAE,IAAI;QAAE;kBACf,cAAA,6LAAC,mNAAA,CAAA,cAAc;YACb,yBAAW,6LAAC,oKAAA,CAAA,UAAgB;gBAAC,UAAS;;;;;;YACtC,cAAW;YACX,IAAI;gBACF,+BAA+B;oBAC7B,OAAO;gBACT;YACF;sBAEC,gBAAgB,GAAG,CAAC,CAAC,MAAM;gBAC1B,MAAM,SAAS,UAAU,gBAAgB,MAAM,GAAG;gBAElD,IAAI,QAAQ;oBACV,qBACE,6LAAC,gNAAA,CAAA,aAAU;wBAET,OAAM;wBACN,IAAI;4BACF,SAAS;4BACT,YAAY;4BACZ,YAAY;wBACd;;4BAEC,KAAK,IAAI;4BACT,KAAK,KAAK;;uBATN,KAAK,IAAI;;;;;gBAYpB;gBAEA,qBACE,6LAAC,8LAAA,CAAA,OAAI;oBAEH,WAAU;oBACV,OAAM;oBACN,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;oBACpC,IAAI;wBACF,SAAS;wBACT,YAAY;wBACZ,QAAQ;wBACR,WAAW;4BACT,OAAO;wBACT;oBACF;;wBAEC,KAAK,IAAI;wBACT,KAAK,KAAK;;mBAdN,KAAK,IAAI;;;;;YAiBpB;;;;;;;;;;;AAIR;GA1GgB;;QACG,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;;;KAFV", "debugId": null}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/src/components/layout/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  Drawer,\n  AppBar,\n  Toolbar,\n  Typography,\n  IconButton,\n  useTheme,\n  useMediaQuery,\n} from '@mui/material';\nimport {\n  Menu as MenuIcon,\n  ChevronLeft as ChevronLeftIcon,\n} from '@mui/icons-material';\nimport { Sidebar } from './Sidebar';\nimport { Header } from './Header';\nimport { Breadcrumbs } from './Breadcrumbs';\n\nconst drawerWidth = 280;\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nexport function AdminLayout({ children, title = 'Dashboard' }: AdminLayoutProps) {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const [mobileOpen, setMobileOpen] = useState(false);\n  const [desktopOpen, setDesktopOpen] = useState(true);\n\n  const handleDrawerToggle = () => {\n    if (isMobile) {\n      setMobileOpen(!mobileOpen);\n    } else {\n      setDesktopOpen(!desktopOpen);\n    }\n  };\n\n  const drawer = <Sidebar onClose={() => setMobileOpen(false)} />;\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      {/* App Bar */}\n      <AppBar\n        position=\"fixed\"\n        sx={{\n          width: { \n            md: desktopOpen ? `calc(100% - ${drawerWidth}px)` : '100%' \n          },\n          ml: { \n            md: desktopOpen ? `${drawerWidth}px` : 0 \n          },\n          backgroundColor: '#fff',\n          color: '#333',\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n          borderBottom: '1px solid #e0e0e0',\n        }}\n      >\n        <Toolbar>\n          <IconButton\n            color=\"inherit\"\n            aria-label=\"toggle drawer\"\n            edge=\"start\"\n            onClick={handleDrawerToggle}\n            sx={{ mr: 2 }}\n          >\n            {(isMobile ? mobileOpen : desktopOpen) ? <ChevronLeftIcon /> : <MenuIcon />}\n          </IconButton>\n          <Typography variant=\"h6\" noWrap component=\"div\" sx={{ flexGrow: 1 }}>\n            {title}\n          </Typography>\n          <Header />\n        </Toolbar>\n      </AppBar>\n\n      {/* Navigation Drawer */}\n      <Box\n        component=\"nav\"\n        sx={{ width: { md: desktopOpen ? drawerWidth : 0 }, flexShrink: { md: 0 } }}\n      >\n        {/* Mobile drawer */}\n        <Drawer\n          variant=\"temporary\"\n          open={mobileOpen}\n          onClose={() => setMobileOpen(false)}\n          ModalProps={{\n            keepMounted: true, // Better open performance on mobile.\n          }}\n          sx={{\n            display: { xs: 'block', md: 'none' },\n            '& .MuiDrawer-paper': {\n              boxSizing: 'border-box',\n              width: drawerWidth,\n            },\n          }}\n        >\n          {drawer}\n        </Drawer>\n\n        {/* Desktop drawer */}\n        <Drawer\n          variant=\"persistent\"\n          open={desktopOpen}\n          sx={{\n            display: { xs: 'none', md: 'block' },\n            '& .MuiDrawer-paper': {\n              boxSizing: 'border-box',\n              width: drawerWidth,\n            },\n          }}\n        >\n          {drawer}\n        </Drawer>\n      </Box>\n\n      {/* Main content */}\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: 3,\n          width: { \n            md: desktopOpen ? `calc(100% - ${drawerWidth}px)` : '100%' \n          },\n          mt: '64px', // Height of AppBar\n          backgroundColor: '#f5f5f5',\n          minHeight: 'calc(100vh - 64px)',\n        }}\n      >\n        <Breadcrumbs />\n        {children}\n      </Box>\n    </Box>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAAA;AAIA;AACA;AACA;;;AAnBA;;;;;;;;AAqBA,MAAM,cAAc;AAOb,SAAS,YAAY,EAAE,QAAQ,EAAE,QAAQ,WAAW,EAAoB;;IAC7E,MAAM,QAAQ,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,WAAW,CAAA,GAAA,iNAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,qBAAqB;QACzB,IAAI,UAAU;YACZ,cAAc,CAAC;QACjB,OAAO;YACL,eAAe,CAAC;QAClB;IACF;IAEA,MAAM,uBAAS,6LAAC,0IAAA,CAAA,UAAO;QAAC,SAAS,IAAM,cAAc;;;;;;IAErD,qBACE,6LAAC,2LAAA,CAAA,MAAG;QAAC,IAAI;YAAE,SAAS;QAAO;;0BAEzB,6LAAC,oMAAA,CAAA,SAAM;gBACL,UAAS;gBACT,IAAI;oBACF,OAAO;wBACL,IAAI,cAAc,CAAC,YAAY,EAAE,YAAY,GAAG,CAAC,GAAG;oBACtD;oBACA,IAAI;wBACF,IAAI,cAAc,GAAG,YAAY,EAAE,CAAC,GAAG;oBACzC;oBACA,iBAAiB;oBACjB,OAAO;oBACP,WAAW;oBACX,cAAc;gBAChB;0BAEA,cAAA,6LAAC,uMAAA,CAAA,UAAO;;sCACN,6LAAC,gNAAA,CAAA,aAAU;4BACT,OAAM;4BACN,cAAW;4BACX,MAAK;4BACL,SAAS;4BACT,IAAI;gCAAE,IAAI;4BAAE;sCAEX,CAAC,WAAW,aAAa,WAAW,kBAAI,6LAAC,mKAAA,CAAA,UAAe;;;;qDAAM,6LAAC,4JAAA,CAAA,UAAQ;;;;;;;;;;sCAE1E,6LAAC,gNAAA,CAAA,aAAU;4BAAC,SAAQ;4BAAK,MAAM;4BAAC,WAAU;4BAAM,IAAI;gCAAE,UAAU;4BAAE;sCAC/D;;;;;;sCAEH,6LAAC,yIAAA,CAAA,SAAM;;;;;;;;;;;;;;;;0BAKX,6LAAC,2LAAA,CAAA,MAAG;gBACF,WAAU;gBACV,IAAI;oBAAE,OAAO;wBAAE,IAAI,cAAc,cAAc;oBAAE;oBAAG,YAAY;wBAAE,IAAI;oBAAE;gBAAE;;kCAG1E,6LAAC,oMAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAM;wBACN,SAAS,IAAM,cAAc;wBAC7B,YAAY;4BACV,aAAa;wBACf;wBACA,IAAI;4BACF,SAAS;gCAAE,IAAI;gCAAS,IAAI;4BAAO;4BACnC,sBAAsB;gCACpB,WAAW;gCACX,OAAO;4BACT;wBACF;kCAEC;;;;;;kCAIH,6LAAC,oMAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAM;wBACN,IAAI;4BACF,SAAS;gCAAE,IAAI;gCAAQ,IAAI;4BAAQ;4BACnC,sBAAsB;gCACpB,WAAW;gCACX,OAAO;4BACT;wBACF;kCAEC;;;;;;;;;;;;0BAKL,6LAAC,2LAAA,CAAA,MAAG;gBACF,WAAU;gBACV,IAAI;oBACF,UAAU;oBACV,GAAG;oBACH,OAAO;wBACL,IAAI,cAAc,CAAC,YAAY,EAAE,YAAY,GAAG,CAAC,GAAG;oBACtD;oBACA,IAAI;oBACJ,iBAAiB;oBACjB,WAAW;gBACb;;kCAEA,6LAAC,8IAAA,CAAA,cAAW;;;;;oBACX;;;;;;;;;;;;;AAIT;GA9GgB;;QACA,wMAAA,CAAA,WAAQ;QACL,iNAAA,CAAA,gBAAa;;;KAFhB", "debugId": null}}, {"offset": {"line": 1541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/src/app/companies/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  Button,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  IconButton,\n  Chip,\n  Alert,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Business as BusinessIcon,\n  Edit as EditIcon,\n  CalendarToday as CalendarIcon,\n} from '@mui/icons-material';\nimport { AdminLayout } from '@/components/layout/AdminLayout';\nimport { useApp } from '@/contexts/AppContext';\n\nexport default function CompaniesPage() {\n  const { companies, currentCompany, setCurrentCompany, createCompany, loading } = useApp();\n  const [open, setOpen] = useState(false);\n  const [name, setName] = useState('');\n  const [description, setDescription] = useState('');\n  const [error, setError] = useState('');\n  const [submitting, setSubmitting] = useState(false);\n\n  const handleSubmit = async () => {\n    if (!name.trim()) {\n      setError('Company name is required');\n      return;\n    }\n\n    setSubmitting(true);\n    setError('');\n\n    try {\n      const result = await createCompany(name.trim(), description.trim() || undefined);\n      if (result.error) {\n        setError(result.error);\n      } else {\n        setOpen(false);\n        setName('');\n        setDescription('');\n      }\n    } catch (err) {\n      setError('An unexpected error occurred');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const handleClose = () => {\n    setOpen(false);\n    setName('');\n    setDescription('');\n    setError('');\n  };\n\n  return (\n    <AdminLayout title=\"Companies\">\n      <Box>\n        {/* Header */}\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>\n          <Box>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              Companies\n            </Typography>\n            <Typography variant=\"body1\" color=\"text.secondary\">\n              Manage your companies and their financial years\n            </Typography>\n          </Box>\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={() => setOpen(true)}\n            sx={{\n              background: 'linear-gradient(45deg, #3498db, #2980b9)',\n              '&:hover': {\n                background: 'linear-gradient(45deg, #2980b9, #1f5f8b)',\n              },\n            }}\n          >\n            Add Company\n          </Button>\n        </Box>\n\n        {/* Companies Grid */}\n        {companies.length === 0 ? (\n          <Card sx={{ textAlign: 'center', py: 8 }}>\n            <CardContent>\n              <BusinessIcon sx={{ fontSize: 80, color: '#bdc3c7', mb: 2 }} />\n              <Typography variant=\"h6\" sx={{ mb: 2, color: '#7f8c8d' }}>\n                No companies found\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                Create your first company to get started with accounting\n              </Typography>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => setOpen(true)}\n              >\n                Create Company\n              </Button>\n            </CardContent>\n          </Card>\n        ) : (\n          <Grid container spacing={3}>\n            {companies.map((company) => (\n              <Grid item xs={12} sm={6} md={4} key={company.id}>\n                <Card\n                  sx={{\n                    height: '100%',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s',\n                    border: currentCompany?.id === company.id ? '2px solid #3498db' : '1px solid #e0e0e0',\n                    '&:hover': {\n                      transform: 'translateY(-2px)',\n                      boxShadow: 3,\n                    },\n                  }}\n                  onClick={() => setCurrentCompany(company)}\n                >\n                  <CardContent>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                      <BusinessIcon sx={{ fontSize: 40, color: '#3498db', mr: 2 }} />\n                      <Box sx={{ flexGrow: 1 }}>\n                        <Typography variant=\"h6\" sx={{ fontWeight: 'bold' }}>\n                          {company.name}\n                        </Typography>\n                        {currentCompany?.id === company.id && (\n                          <Chip\n                            label=\"Current\"\n                            size=\"small\"\n                            color=\"primary\"\n                            sx={{ mt: 0.5 }}\n                          />\n                        )}\n                      </Box>\n                      <IconButton size=\"small\">\n                        <EditIcon />\n                      </IconButton>\n                    </Box>\n\n                    {company.description && (\n                      <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                        {company.description}\n                      </Typography>\n                    )}\n\n                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                      <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                        <CalendarIcon sx={{ fontSize: 16, color: '#7f8c8d', mr: 0.5 }} />\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Created {new Date(company.created_at).toLocaleDateString()}\n                        </Typography>\n                      </Box>\n                    </Box>\n                  </CardContent>\n                </Card>\n              </Grid>\n            ))}\n          </Grid>\n        )}\n\n        {/* Add Company Dialog */}\n        <Dialog open={open} onClose={handleClose} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Add New Company</DialogTitle>\n          <DialogContent>\n            {error && (\n              <Alert severity=\"error\" sx={{ mb: 2 }}>\n                {error}\n              </Alert>\n            )}\n            <TextField\n              autoFocus\n              margin=\"dense\"\n              label=\"Company Name\"\n              fullWidth\n              variant=\"outlined\"\n              value={name}\n              onChange={(e) => setName(e.target.value)}\n              sx={{ mb: 2 }}\n            />\n            <TextField\n              margin=\"dense\"\n              label=\"Description (Optional)\"\n              fullWidth\n              multiline\n              rows={3}\n              variant=\"outlined\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n            />\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleClose}>Cancel</Button>\n            <Button\n              onClick={handleSubmit}\n              variant=\"contained\"\n              disabled={submitting}\n            >\n              {submitting ? 'Creating...' : 'Create Company'}\n            </Button>\n          </DialogActions>\n        </Dialog>\n      </Box>\n    </AdminLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAAA;AAAA;AAAA;AAMA;AACA;;;AA1BA;;;;;;;;;AA4Be,SAAS;;IACtB,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,SAAM,AAAD;IACtF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,SAAS;YACT;QACF;QAEA,cAAc;QACd,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,cAAc,KAAK,IAAI,IAAI,YAAY,IAAI,MAAM;YACtE,IAAI,OAAO,KAAK,EAAE;gBAChB,SAAS,OAAO,KAAK;YACvB,OAAO;gBACL,QAAQ;gBACR,QAAQ;gBACR,eAAe;YACjB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,cAAc;QAClB,QAAQ;QACR,QAAQ;QACR,eAAe;QACf,SAAS;IACX;IAEA,qBACE,6LAAC,8IAAA,CAAA,cAAW;QAAC,OAAM;kBACjB,cAAA,6LAAC,2LAAA,CAAA,MAAG;;8BAEF,6LAAC,2LAAA,CAAA,MAAG;oBAAC,IAAI;wBAAE,SAAS;wBAAQ,gBAAgB;wBAAiB,YAAY;wBAAU,IAAI;oBAAE;;sCACvF,6LAAC,2LAAA,CAAA,MAAG;;8CACF,6LAAC,gNAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAK,IAAI;wCAAE,YAAY;wCAAQ,IAAI;oCAAE;8CAAG;;;;;;8CAG5D,6LAAC,gNAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAQ,OAAM;8CAAiB;;;;;;;;;;;;sCAIrD,6LAAC,oMAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,yBAAW,6LAAC,2JAAA,CAAA,UAAO;;;;;4BACnB,SAAS,IAAM,QAAQ;4BACvB,IAAI;gCACF,YAAY;gCACZ,WAAW;oCACT,YAAY;gCACd;4BACF;sCACD;;;;;;;;;;;;gBAMF,UAAU,MAAM,KAAK,kBACpB,6LAAC,8LAAA,CAAA,OAAI;oBAAC,IAAI;wBAAE,WAAW;wBAAU,IAAI;oBAAE;8BACrC,cAAA,6LAAC,mNAAA,CAAA,cAAW;;0CACV,6LAAC,gKAAA,CAAA,UAAY;gCAAC,IAAI;oCAAE,UAAU;oCAAI,OAAO;oCAAW,IAAI;gCAAE;;;;;;0CAC1D,6LAAC,gNAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAK,IAAI;oCAAE,IAAI;oCAAG,OAAO;gCAAU;0CAAG;;;;;;0CAG1D,6LAAC,gNAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAQ,OAAM;gCAAiB,IAAI;oCAAE,IAAI;gCAAE;0CAAG;;;;;;0CAGlE,6LAAC,oMAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,yBAAW,6LAAC,2JAAA,CAAA,UAAO;;;;;gCACnB,SAAS,IAAM,QAAQ;0CACxB;;;;;;;;;;;;;;;;yCAML,6LAAC,8LAAA,CAAA,OAAI;oBAAC,SAAS;oBAAC,SAAS;8BACtB,UAAU,GAAG,CAAC,CAAC,wBACd,6LAAC,8LAAA,CAAA,OAAI;4BAAC,IAAI;4BAAC,IAAI;4BAAI,IAAI;4BAAG,IAAI;sCAC5B,cAAA,6LAAC,8LAAA,CAAA,OAAI;gCACH,IAAI;oCACF,QAAQ;oCACR,QAAQ;oCACR,YAAY;oCACZ,QAAQ,gBAAgB,OAAO,QAAQ,EAAE,GAAG,sBAAsB;oCAClE,WAAW;wCACT,WAAW;wCACX,WAAW;oCACb;gCACF;gCACA,SAAS,IAAM,kBAAkB;0CAEjC,cAAA,6LAAC,mNAAA,CAAA,cAAW;;sDACV,6LAAC,2LAAA,CAAA,MAAG;4CAAC,IAAI;gDAAE,SAAS;gDAAQ,YAAY;gDAAU,IAAI;4CAAE;;8DACtD,6LAAC,gKAAA,CAAA,UAAY;oDAAC,IAAI;wDAAE,UAAU;wDAAI,OAAO;wDAAW,IAAI;oDAAE;;;;;;8DAC1D,6LAAC,2LAAA,CAAA,MAAG;oDAAC,IAAI;wDAAE,UAAU;oDAAE;;sEACrB,6LAAC,gNAAA,CAAA,aAAU;4DAAC,SAAQ;4DAAK,IAAI;gEAAE,YAAY;4DAAO;sEAC/C,QAAQ,IAAI;;;;;;wDAEd,gBAAgB,OAAO,QAAQ,EAAE,kBAChC,6LAAC,8LAAA,CAAA,OAAI;4DACH,OAAM;4DACN,MAAK;4DACL,OAAM;4DACN,IAAI;gEAAE,IAAI;4DAAI;;;;;;;;;;;;8DAIpB,6LAAC,gNAAA,CAAA,aAAU;oDAAC,MAAK;8DACf,cAAA,6LAAC,4JAAA,CAAA,UAAQ;;;;;;;;;;;;;;;;wCAIZ,QAAQ,WAAW,kBAClB,6LAAC,gNAAA,CAAA,aAAU;4CAAC,SAAQ;4CAAQ,OAAM;4CAAiB,IAAI;gDAAE,IAAI;4CAAE;sDAC5D,QAAQ,WAAW;;;;;;sDAIxB,6LAAC,2LAAA,CAAA,MAAG;4CAAC,IAAI;gDAAE,SAAS;gDAAQ,YAAY;gDAAU,gBAAgB;4CAAgB;sDAChF,cAAA,6LAAC,2LAAA,CAAA,MAAG;gDAAC,IAAI;oDAAE,SAAS;oDAAQ,YAAY;gDAAS;;kEAC/C,6LAAC,qKAAA,CAAA,UAAY;wDAAC,IAAI;4DAAE,UAAU;4DAAI,OAAO;4DAAW,IAAI;wDAAI;;;;;;kEAC5D,6LAAC,gNAAA,CAAA,aAAU;wDAAC,SAAQ;wDAAU,OAAM;;4DAAiB;4DAC1C,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA7C9B,QAAQ,EAAE;;;;;;;;;;8BAyDtD,6LAAC,oMAAA,CAAA,SAAM;oBAAC,MAAM;oBAAM,SAAS;oBAAa,UAAS;oBAAK,SAAS;;sCAC/D,6LAAC,mNAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,6LAAC,yNAAA,CAAA,gBAAa;;gCACX,uBACC,6LAAC,iMAAA,CAAA,QAAK;oCAAC,UAAS;oCAAQ,IAAI;wCAAE,IAAI;oCAAE;8CACjC;;;;;;8CAGL,6LAAC,6MAAA,CAAA,YAAS;oCACR,SAAS;oCACT,QAAO;oCACP,OAAM;oCACN,SAAS;oCACT,SAAQ;oCACR,OAAO;oCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oCACvC,IAAI;wCAAE,IAAI;oCAAE;;;;;;8CAEd,6LAAC,6MAAA,CAAA,YAAS;oCACR,QAAO;oCACP,OAAM;oCACN,SAAS;oCACT,SAAS;oCACT,MAAM;oCACN,SAAQ;oCACR,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sCAGlD,6LAAC,yNAAA,CAAA,gBAAa;;8CACZ,6LAAC,oMAAA,CAAA,SAAM;oCAAC,SAAS;8CAAa;;;;;;8CAC9B,6LAAC,oMAAA,CAAA,SAAM;oCACL,SAAS;oCACT,SAAQ;oCACR,UAAU;8CAET,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;GA/LwB;;QAC2D,iIAAA,CAAA,SAAM;;;KADjE", "debugId": null}}]}