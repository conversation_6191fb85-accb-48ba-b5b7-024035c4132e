{"version": 3, "sources": ["../../../src/singlestore-core/columns/serial.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilder<PERSON>untimeConfig,\n\tHasDefault,\n\tIsAutoincrement,\n\tIsP<PERSON>ry<PERSON>ey,\n\tMakeColumnConfig,\n\tNotNull,\n} from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { SingleStoreColumnBuilderWithAutoIncrement, SingleStoreColumnWithAutoIncrement } from './common.ts';\n\nexport type SingleStoreSerialBuilderInitial<TName extends string> = IsAutoincrement<\n\tIsPrimaryKey<\n\t\tNotNull<\n\t\t\tHasDefault<\n\t\t\t\tSingleStoreSerialBuilder<{\n\t\t\t\t\tname: TName;\n\t\t\t\t\tdataType: 'number';\n\t\t\t\t\tcolumnType: 'SingleStoreSerial';\n\t\t\t\t\tdata: number;\n\t\t\t\t\tdriverParam: number;\n\t\t\t\t\tenumValues: undefined;\n\t\t\t\t\tgenerated: undefined;\n\t\t\t\t}>\n\t\t\t>\n\t\t>\n\t>\n>;\n\nexport class SingleStoreSerialBuilder<T extends ColumnBuilderBaseConfig<'number', 'SingleStoreSerial'>>\n\textends SingleStoreColumnBuilderWithAutoIncrement<T>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreSerialBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'SingleStoreSerial');\n\t\tthis.config.hasDefault = true;\n\t\tthis.config.autoIncrement = true;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreSerial<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreSerial<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreSerial<\n\tT extends ColumnBaseConfig<'number', 'SingleStoreSerial'>,\n> extends SingleStoreColumnWithAutoIncrement<T> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreSerial';\n\n\tgetSQLType(): string {\n\t\treturn 'serial';\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number(value);\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function serial(): SingleStoreSerialBuilderInitial<''>;\nexport function serial<TName extends string>(name: TName): SingleStoreSerialBuilderInitial<TName>;\nexport function serial(name?: string) {\n\treturn new SingleStoreSerialBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,oBAA2B;AAE3B,oBAA8F;AAoBvF,MAAM,iCACJ,wDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,mBAAmB;AACzC,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,gBAAgB;AAAA,EAC7B;AAAA;AAAA,EAGS,MACR,OACqD;AACrD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,0BAEH,iDAAsC;AAAA,EAC/C,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,KAAK;AAAA,IACpB;AACA,WAAO;AAAA,EACR;AACD;AAIO,SAAS,OAAO,MAAe;AACrC,SAAO,IAAI,yBAAyB,QAAQ,EAAE;AAC/C;", "names": []}