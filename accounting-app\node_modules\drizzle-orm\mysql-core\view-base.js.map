{"version": 3, "sources": ["../../src/mysql-core/view-base.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { ColumnsSelection } from '~/sql/sql.ts';\nimport { View } from '~/sql/sql.ts';\n\nexport abstract class MySqlViewBase<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> extends View<TName, TExisting, TSelectedFields> {\n\tstatic override readonly [entityKind]: string = 'MySqlViewBase';\n\n\tdeclare readonly _: View<TName, TExisting, TSelectedFields>['_'] & {\n\t\treadonly viewBrand: 'MySqlViewBase';\n\t};\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAE3B,SAAS,YAAY;AAEd,MAAe,sBAIZ,KAAwC;AAAA,EACjD,QAA0B,UAAU,IAAY;AAKjD;", "names": []}