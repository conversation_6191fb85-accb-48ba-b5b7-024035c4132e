{"version": 3, "sources": ["../../src/singlestore-core/view.ts"], "sourcesContent": ["import type { BuildColumns } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { AddAliasToSelection } from '~/query-builders/select.types.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, SQL } from '~/sql/sql.ts';\nimport { getTableColumns } from '~/utils.ts';\nimport type { SingleStoreColumn, SingleStoreColumnBuilderBase } from './columns/index.ts';\nimport { QueryBuilder } from './query-builders/query-builder.ts';\nimport type { SelectedFields } from './query-builders/select.types.ts';\nimport { singlestoreTable } from './table.ts';\nimport { SingleStoreViewBase } from './view-base.ts';\nimport { SingleStoreViewConfig } from './view-common.ts';\n\nexport interface ViewBuilderConfig {\n\talgorithm?: 'undefined' | 'merge' | 'temptable';\n\tdefiner?: string;\n\tsqlSecurity?: 'definer' | 'invoker';\n\twithCheckOption?: 'cascaded' | 'local';\n}\n\nexport class ViewBuilderCore<TConfig extends { name: string; columns?: unknown }> {\n\tstatic readonly [entityKind]: string = 'SingleStoreViewBuilder';\n\n\tdeclare readonly _: {\n\t\treadonly name: TConfig['name'];\n\t\treadonly columns: TConfig['columns'];\n\t};\n\n\tconstructor(\n\t\tprotected name: TConfig['name'],\n\t\tprotected schema: string | undefined,\n\t) {}\n\n\tprotected config: ViewBuilderConfig = {};\n\n\talgorithm(\n\t\talgorithm: Exclude<ViewBuilderConfig['algorithm'], undefined>,\n\t): this {\n\t\tthis.config.algorithm = algorithm;\n\t\treturn this;\n\t}\n\n\tdefiner(\n\t\tdefiner: Exclude<ViewBuilderConfig['definer'], undefined>,\n\t): this {\n\t\tthis.config.definer = definer;\n\t\treturn this;\n\t}\n\n\tsqlSecurity(\n\t\tsqlSecurity: Exclude<ViewBuilderConfig['sqlSecurity'], undefined>,\n\t): this {\n\t\tthis.config.sqlSecurity = sqlSecurity;\n\t\treturn this;\n\t}\n\n\twithCheckOption(\n\t\twithCheckOption?: Exclude<ViewBuilderConfig['withCheckOption'], undefined>,\n\t): this {\n\t\tthis.config.withCheckOption = withCheckOption ?? 'cascaded';\n\t\treturn this;\n\t}\n}\n\nexport class ViewBuilder<TName extends string = string> extends ViewBuilderCore<{ name: TName }> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreViewBuilder';\n\n\tas<TSelectedFields extends SelectedFields>(\n\t\tqb: TypedQueryBuilder<TSelectedFields> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelectedFields>),\n\t): SingleStoreViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'singlestore'>> {\n\t\tif (typeof qb === 'function') {\n\t\t\tqb = qb(new QueryBuilder());\n\t\t}\n\t\tconst selectionProxy = new SelectionProxyHandler<TSelectedFields>({\n\t\t\talias: this.name,\n\t\t\tsqlBehavior: 'error',\n\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\treplaceOriginalName: true,\n\t\t});\n\t\tconst aliasedSelection = new Proxy(qb.getSelectedFields(), selectionProxy);\n\t\treturn new Proxy(\n\t\t\tnew SingleStoreView({\n\t\t\t\tsinglestoreConfig: this.config,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: aliasedSelection,\n\t\t\t\t\tquery: qb.getSQL().inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tselectionProxy as any,\n\t\t) as SingleStoreViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'singlestore'>>;\n\t}\n}\n\nexport class ManualViewBuilder<\n\tTName extends string = string,\n\tTColumns extends Record<string, SingleStoreColumnBuilderBase> = Record<string, SingleStoreColumnBuilderBase>,\n> extends ViewBuilderCore<{ name: TName; columns: TColumns }> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreManualViewBuilder';\n\n\tprivate columns: Record<string, SingleStoreColumn>;\n\n\tconstructor(\n\t\tname: TName,\n\t\tcolumns: TColumns,\n\t\tschema: string | undefined,\n\t) {\n\t\tsuper(name, schema);\n\t\tthis.columns = getTableColumns(singlestoreTable(name, columns)) as BuildColumns<TName, TColumns, 'singlestore'>;\n\t}\n\n\texisting(): SingleStoreViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'singlestore'>> {\n\t\treturn new Proxy(\n\t\t\tnew SingleStoreView({\n\t\t\t\tsinglestoreConfig: undefined,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: undefined,\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as SingleStoreViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'singlestore'>>;\n\t}\n\n\tas(query: SQL): SingleStoreViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'singlestore'>> {\n\t\treturn new Proxy(\n\t\t\tnew SingleStoreView({\n\t\t\t\tsinglestoreConfig: this.config,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: query.inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as SingleStoreViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'singlestore'>>;\n\t}\n}\n\nexport class SingleStoreView<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> extends SingleStoreViewBase<TName, TExisting, TSelectedFields> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreView';\n\n\tdeclare protected $SingleStoreViewBrand: 'SingleStoreView';\n\n\t[SingleStoreViewConfig]: ViewBuilderConfig | undefined;\n\n\tconstructor({ singlestoreConfig, config }: {\n\t\tsinglestoreConfig: ViewBuilderConfig | undefined;\n\t\tconfig: {\n\t\t\tname: TName;\n\t\t\tschema: string | undefined;\n\t\t\tselectedFields: SelectedFields;\n\t\t\tquery: SQL | undefined;\n\t\t};\n\t}) {\n\t\tsuper(config);\n\t\tthis[SingleStoreViewConfig] = singlestoreConfig;\n\t}\n}\n\nexport type SingleStoreViewWithSelection<\n\tTName extends string,\n\tTExisting extends boolean,\n\tTSelectedFields extends ColumnsSelection,\n> = SingleStoreView<TName, TExisting, TSelectedFields> & TSelectedFields;\n\n// TODO: needs to be implemented differently compared to MySQL.\n// /** @internal */\n// export function singlestoreViewWithSchema(\n// \tname: string,\n// \tselection: Record<string, SingleStoreColumnBuilderBase> | undefined,\n// \tschema: string | undefined,\n// ): ViewBuilder | ManualViewBuilder {\n// \tif (selection) {\n// \t\treturn new ManualViewBuilder(name, selection, schema);\n// \t}\n// \treturn new ViewBuilder(name, schema);\n// }\n\n// export function singlestoreView<TName extends string>(name: TName): ViewBuilder<TName>;\n// export function singlestoreView<TName extends string, TColumns extends Record<string, SingleStoreColumnBuilderBase>>(\n// \tname: TName,\n// \tcolumns: TColumns,\n// ): ManualViewBuilder<TName, TColumns>;\n// export function singlestoreView(\n// \tname: string,\n// \tselection?: Record<string, SingleStoreColumnBuilderBase>,\n// ): ViewBuilder | ManualViewBuilder {\n// \treturn singlestoreViewWithSchema(name, selection, undefined);\n// }\n"], "mappings": "AACA,SAAS,kBAAkB;AAG3B,SAAS,6BAA6B;AAEtC,SAAS,uBAAuB;AAEhC,SAAS,oBAAoB;AAE7B,SAAS,wBAAwB;AACjC,SAAS,2BAA2B;AACpC,SAAS,6BAA6B;AAS/B,MAAM,gBAAqE;AAAA,EAQjF,YACW,MACA,QACT;AAFS;AACA;AAAA,EACR;AAAA,EAVH,QAAiB,UAAU,IAAY;AAAA,EAY7B,SAA4B,CAAC;AAAA,EAEvC,UACC,WACO;AACP,SAAK,OAAO,YAAY;AACxB,WAAO;AAAA,EACR;AAAA,EAEA,QACC,SACO;AACP,SAAK,OAAO,UAAU;AACtB,WAAO;AAAA,EACR;AAAA,EAEA,YACC,aACO;AACP,SAAK,OAAO,cAAc;AAC1B,WAAO;AAAA,EACR;AAAA,EAEA,gBACC,iBACO;AACP,SAAK,OAAO,kBAAkB,mBAAmB;AACjD,WAAO;AAAA,EACR;AACD;AAEO,MAAM,oBAAmD,gBAAiC;AAAA,EAChG,QAA0B,UAAU,IAAY;AAAA,EAEhD,GACC,IACyG;AACzG,QAAI,OAAO,OAAO,YAAY;AAC7B,WAAK,GAAG,IAAI,aAAa,CAAC;AAAA,IAC3B;AACA,UAAM,iBAAiB,IAAI,sBAAuC;AAAA,MACjE,OAAO,KAAK;AAAA,MACZ,aAAa;AAAA,MACb,oBAAoB;AAAA,MACpB,qBAAqB;AAAA,IACtB,CAAC;AACD,UAAM,mBAAmB,IAAI,MAAM,GAAG,kBAAkB,GAAG,cAAc;AACzE,WAAO,IAAI;AAAA,MACV,IAAI,gBAAgB;AAAA,QACnB,mBAAmB,KAAK;AAAA,QACxB,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB;AAAA,UAChB,OAAO,GAAG,OAAO,EAAE,aAAa;AAAA,QACjC;AAAA,MACD,CAAC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,0BAGH,gBAAoD;AAAA,EAC7D,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EAER,YACC,MACA,SACA,QACC;AACD,UAAM,MAAM,MAAM;AAClB,SAAK,UAAU,gBAAgB,iBAAiB,MAAM,OAAO,CAAC;AAAA,EAC/D;AAAA,EAEA,WAAoG;AACnG,WAAO,IAAI;AAAA,MACV,IAAI,gBAAgB;AAAA,QACnB,mBAAmB;AAAA,QACnB,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB,KAAK;AAAA,UACrB,OAAO;AAAA,QACR;AAAA,MACD,CAAC;AAAA,MACD,IAAI,sBAAsB;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EAEA,GAAG,OAAsG;AACxG,WAAO,IAAI;AAAA,MACV,IAAI,gBAAgB;AAAA,QACnB,mBAAmB,KAAK;AAAA,QACxB,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB,KAAK;AAAA,UACrB,OAAO,MAAM,aAAa;AAAA,QAC3B;AAAA,MACD,CAAC;AAAA,MACD,IAAI,sBAAsB;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,EACD;AACD;AAEO,MAAM,wBAIH,oBAAuD;AAAA,EAChE,QAA0B,UAAU,IAAY;AAAA,EAIhD,CAAC,qBAAqB;AAAA,EAEtB,YAAY,EAAE,mBAAmB,OAAO,GAQrC;AACF,UAAM,MAAM;AACZ,SAAK,qBAAqB,IAAI;AAAA,EAC/B;AACD;", "names": []}