{"version": 3, "sources": ["../../src/sqlite-core/db.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { ExtractTablesWithRelations, RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport { type ColumnsSelection, type SQL, sql, type SQLWrapper } from '~/sql/sql.ts';\nimport type { SQLiteAsyncDialect, SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\nimport {\n\tQueryBuilder,\n\tSQLiteDeleteBase,\n\tSQLiteInsertBuilder,\n\tSQLiteSelectBuilder,\n\tSQLiteUpdateBuilder,\n} from '~/sqlite-core/query-builders/index.ts';\nimport type {\n\tDBResult,\n\tResult,\n\tSQLiteSession,\n\tSQLiteTransaction,\n\tSQLiteTransactionConfig,\n} from '~/sqlite-core/session.ts';\nimport type { SQLiteTable } from '~/sqlite-core/table.ts';\nimport { WithSubquery } from '~/subquery.ts';\nimport type { DrizzleTypeError } from '~/utils.ts';\nimport { SQLiteCountBuilder } from './query-builders/count.ts';\nimport { RelationalQueryBuilder } from './query-builders/query.ts';\nimport { SQLiteRaw } from './query-builders/raw.ts';\nimport type { SelectedFields } from './query-builders/select.types.ts';\nimport type { WithBuilder } from './subquery.ts';\nimport type { SQLiteViewBase } from './view-base.ts';\n\nexport class BaseSQLiteDatabase<\n\tTResultKind extends 'sync' | 'async',\n\tTRunResult,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = ExtractTablesWithRelations<TFullSchema>,\n> {\n\tstatic readonly [entityKind]: string = 'BaseSQLiteDatabase';\n\n\tdeclare readonly _: {\n\t\treadonly schema: TSchema | undefined;\n\t\treadonly fullSchema: TFullSchema;\n\t\treadonly tableNamesMap: Record<string, string>;\n\t};\n\n\tquery: TFullSchema extends Record<string, never>\n\t\t? DrizzleTypeError<'Seems like the schema generic is missing - did you forget to add it to your DB type?'>\n\t\t: {\n\t\t\t[K in keyof TSchema]: RelationalQueryBuilder<TResultKind, TFullSchema, TSchema, TSchema[K]>;\n\t\t};\n\n\tconstructor(\n\t\tprivate resultKind: TResultKind,\n\t\t/** @internal */\n\t\treadonly dialect: { sync: SQLiteSyncDialect; async: SQLiteAsyncDialect }[TResultKind],\n\t\t/** @internal */\n\t\treadonly session: SQLiteSession<TResultKind, TRunResult, TFullSchema, TSchema>,\n\t\tschema: RelationalSchemaConfig<TSchema> | undefined,\n\t) {\n\t\tthis._ = schema\n\t\t\t? {\n\t\t\t\tschema: schema.schema,\n\t\t\t\tfullSchema: schema.fullSchema as TFullSchema,\n\t\t\t\ttableNamesMap: schema.tableNamesMap,\n\t\t\t}\n\t\t\t: {\n\t\t\t\tschema: undefined,\n\t\t\t\tfullSchema: {} as TFullSchema,\n\t\t\t\ttableNamesMap: {},\n\t\t\t};\n\t\tthis.query = {} as typeof this['query'];\n\t\tconst query = this.query as {\n\t\t\t[K in keyof TSchema]: RelationalQueryBuilder<TResultKind, TFullSchema, TSchema, TSchema[K]>;\n\t\t};\n\t\tif (this._.schema) {\n\t\t\tfor (const [tableName, columns] of Object.entries(this._.schema)) {\n\t\t\t\tquery[tableName as keyof TSchema] = new RelationalQueryBuilder(\n\t\t\t\t\tresultKind,\n\t\t\t\t\tschema!.fullSchema,\n\t\t\t\t\tthis._.schema,\n\t\t\t\t\tthis._.tableNamesMap,\n\t\t\t\t\tschema!.fullSchema[tableName] as SQLiteTable,\n\t\t\t\t\tcolumns,\n\t\t\t\t\tdialect,\n\t\t\t\t\tsession as SQLiteSession<any, any, any, any> as any,\n\t\t\t\t) as typeof query[keyof TSchema];\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Creates a subquery that defines a temporary named result set as a CTE.\n\t *\n\t * It is useful for breaking down complex queries into simpler parts and for reusing the result set in subsequent parts of the query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n\t *\n\t * @param alias The alias for the subquery.\n\t *\n\t * Failure to provide an alias will result in a DrizzleTypeError, preventing the subquery from being referenced in other queries.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Create a subquery with alias 'sq' and use it in the select query\n\t * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n\t *\n\t * const result = await db.with(sq).select().from(sq);\n\t * ```\n\t *\n\t * To select arbitrary SQL values as fields in a CTE and reference them in other CTEs or in the main query, you need to add aliases to them:\n\t *\n\t * ```ts\n\t * // Select an arbitrary SQL value as a field in a CTE and reference it in the main query\n\t * const sq = db.$with('sq').as(db.select({\n\t *   name: sql<string>`upper(${users.name})`.as('name'),\n\t * })\n\t * .from(users));\n\t *\n\t * const result = await db.with(sq).select({ name: sq.name }).from(sq);\n\t * ```\n\t */\n\t$with: WithBuilder = (alias: string, selection?: ColumnsSelection) => {\n\t\tconst self = this;\n\t\tconst as = (\n\t\t\tqb:\n\t\t\t\t| TypedQueryBuilder<ColumnsSelection | undefined>\n\t\t\t\t| SQL\n\t\t\t\t| ((qb: QueryBuilder) => TypedQueryBuilder<ColumnsSelection | undefined> | SQL),\n\t\t) => {\n\t\t\tif (typeof qb === 'function') {\n\t\t\t\tqb = qb(new QueryBuilder(self.dialect));\n\t\t\t}\n\n\t\t\treturn new Proxy(\n\t\t\t\tnew WithSubquery(\n\t\t\t\t\tqb.getSQL(),\n\t\t\t\t\tselection ?? ('getSelectedFields' in qb ? qb.getSelectedFields() ?? {} : {}) as SelectedFields,\n\t\t\t\t\talias,\n\t\t\t\t\ttrue,\n\t\t\t\t),\n\t\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t\t);\n\t\t};\n\t\treturn { as };\n\t};\n\n\t$count(\n\t\tsource: SQLiteTable | SQLiteViewBase | SQL | SQLWrapper,\n\t\tfilters?: SQL<unknown>,\n\t) {\n\t\treturn new SQLiteCountBuilder({ source, filters, session: this.session });\n\t}\n\n\t/**\n\t * Incorporates a previously defined CTE (using `$with`) into the main query.\n\t *\n\t * This method allows the main query to reference a temporary named result set.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n\t *\n\t * @param queries The CTEs to incorporate into the main query.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Define a subquery 'sq' as a CTE using $with\n\t * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n\t *\n\t * // Incorporate the CTE 'sq' into the main query and select from it\n\t * const result = await db.with(sq).select().from(sq);\n\t * ```\n\t */\n\twith(...queries: WithSubquery[]) {\n\t\tconst self = this;\n\n\t\t/**\n\t\t * Creates a select query.\n\t\t *\n\t\t * Calling this method with no arguments will select all columns from the table. Pass a selection object to specify the columns you want to select.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select}\n\t\t *\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Select all columns and all rows from the 'cars' table\n\t\t * const allCars: Car[] = await db.select().from(cars);\n\t\t *\n\t\t * // Select specific columns and all rows from the 'cars' table\n\t\t * const carsIdsAndBrands: { id: number; brand: string }[] = await db.select({\n\t\t *   id: cars.id,\n\t\t *   brand: cars.brand\n\t\t * })\n\t\t *   .from(cars);\n\t\t * ```\n\t\t *\n\t\t * Like in SQL, you can use arbitrary expressions as selection fields, not just table columns:\n\t\t *\n\t\t * ```ts\n\t\t * // Select specific columns along with expression and all rows from the 'cars' table\n\t\t * const carsIdsAndLowerNames: { id: number; lowerBrand: string }[] = await db.select({\n\t\t *   id: cars.id,\n\t\t *   lowerBrand: sql<string>`lower(${cars.brand})`,\n\t\t * })\n\t\t *   .from(cars);\n\t\t * ```\n\t\t */\n\t\tfunction select(): SQLiteSelectBuilder<undefined, TResultKind, TRunResult>;\n\t\tfunction select<TSelection extends SelectedFields>(\n\t\t\tfields: TSelection,\n\t\t): SQLiteSelectBuilder<TSelection, TResultKind, TRunResult>;\n\t\tfunction select(\n\t\t\tfields?: SelectedFields,\n\t\t): SQLiteSelectBuilder<SelectedFields | undefined, TResultKind, TRunResult> {\n\t\t\treturn new SQLiteSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Adds `distinct` expression to the select query.\n\t\t *\n\t\t * Calling this method will return only unique values. When multiple columns are selected, it returns rows with unique combinations of values in these columns.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t\t *\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Select all unique rows from the 'cars' table\n\t\t * await db.selectDistinct()\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.id, cars.brand, cars.color);\n\t\t *\n\t\t * // Select all unique brands from the 'cars' table\n\t\t * await db.selectDistinct({ brand: cars.brand })\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.brand);\n\t\t * ```\n\t\t */\n\t\tfunction selectDistinct(): SQLiteSelectBuilder<undefined, TResultKind, TRunResult>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(\n\t\t\tfields: TSelection,\n\t\t): SQLiteSelectBuilder<TSelection, TResultKind, TRunResult>;\n\t\tfunction selectDistinct(\n\t\t\tfields?: SelectedFields,\n\t\t): SQLiteSelectBuilder<SelectedFields | undefined, TResultKind, TRunResult> {\n\t\t\treturn new SQLiteSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t\tdistinct: true,\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Creates an update query.\n\t\t *\n\t\t * Calling this method without `.where()` clause will update all rows in a table. The `.where()` clause specifies which rows should be updated.\n\t\t *\n\t\t * Use `.set()` method to specify which values to update.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t\t *\n\t\t * @param table The table to update.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Update all rows in the 'cars' table\n\t\t * await db.update(cars).set({ color: 'red' });\n\t\t *\n\t\t * // Update rows with filters and conditions\n\t\t * await db.update(cars).set({ color: 'red' }).where(eq(cars.brand, 'BMW'));\n\t\t *\n\t\t * // Update with returning clause\n\t\t * const updatedCar: Car[] = await db.update(cars)\n\t\t *   .set({ color: 'red' })\n\t\t *   .where(eq(cars.id, 1))\n\t\t *   .returning();\n\t\t * ```\n\t\t */\n\t\tfunction update<TTable extends SQLiteTable>(table: TTable): SQLiteUpdateBuilder<TTable, TResultKind, TRunResult> {\n\t\t\treturn new SQLiteUpdateBuilder(table, self.session, self.dialect, queries);\n\t\t}\n\n\t\t/**\n\t\t * Creates an insert query.\n\t\t *\n\t\t * Calling this method will create new rows in a table. Use `.values()` method to specify which values to insert.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/insert}\n\t\t *\n\t\t * @param table The table to insert into.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Insert one row\n\t\t * await db.insert(cars).values({ brand: 'BMW' });\n\t\t *\n\t\t * // Insert multiple rows\n\t\t * await db.insert(cars).values([{ brand: 'BMW' }, { brand: 'Porsche' }]);\n\t\t *\n\t\t * // Insert with returning clause\n\t\t * const insertedCar: Car[] = await db.insert(cars)\n\t\t *   .values({ brand: 'BMW' })\n\t\t *   .returning();\n\t\t * ```\n\t\t */\n\t\tfunction insert<TTable extends SQLiteTable>(into: TTable): SQLiteInsertBuilder<TTable, TResultKind, TRunResult> {\n\t\t\treturn new SQLiteInsertBuilder(into, self.session, self.dialect, queries);\n\t\t}\n\n\t\t/**\n\t\t * Creates a delete query.\n\t\t *\n\t\t * Calling this method without `.where()` clause will delete all rows in a table. The `.where()` clause specifies which rows should be deleted.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t\t *\n\t\t * @param table The table to delete from.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Delete all rows in the 'cars' table\n\t\t * await db.delete(cars);\n\t\t *\n\t\t * // Delete rows with filters and conditions\n\t\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t\t *\n\t\t * // Delete with returning clause\n\t\t * const deletedCar: Car[] = await db.delete(cars)\n\t\t *   .where(eq(cars.id, 1))\n\t\t *   .returning();\n\t\t * ```\n\t\t */\n\t\tfunction delete_<TTable extends SQLiteTable>(from: TTable): SQLiteDeleteBase<TTable, TResultKind, TRunResult> {\n\t\t\treturn new SQLiteDeleteBase(from, self.session, self.dialect, queries);\n\t\t}\n\n\t\treturn { select, selectDistinct, update, insert, delete: delete_ };\n\t}\n\n\t/**\n\t * Creates a select query.\n\t *\n\t * Calling this method with no arguments will select all columns from the table. Pass a selection object to specify the columns you want to select.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select}\n\t *\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all columns and all rows from the 'cars' table\n\t * const allCars: Car[] = await db.select().from(cars);\n\t *\n\t * // Select specific columns and all rows from the 'cars' table\n\t * const carsIdsAndBrands: { id: number; brand: string }[] = await db.select({\n\t *   id: cars.id,\n\t *   brand: cars.brand\n\t * })\n\t *   .from(cars);\n\t * ```\n\t *\n\t * Like in SQL, you can use arbitrary expressions as selection fields, not just table columns:\n\t *\n\t * ```ts\n\t * // Select specific columns along with expression and all rows from the 'cars' table\n\t * const carsIdsAndLowerNames: { id: number; lowerBrand: string }[] = await db.select({\n\t *   id: cars.id,\n\t *   lowerBrand: sql<string>`lower(${cars.brand})`,\n\t * })\n\t *   .from(cars);\n\t * ```\n\t */\n\tselect(): SQLiteSelectBuilder<undefined, TResultKind, TRunResult>;\n\tselect<TSelection extends SelectedFields>(\n\t\tfields: TSelection,\n\t): SQLiteSelectBuilder<TSelection, TResultKind, TRunResult>;\n\tselect(fields?: SelectedFields): SQLiteSelectBuilder<SelectedFields | undefined, TResultKind, TRunResult> {\n\t\treturn new SQLiteSelectBuilder({ fields: fields ?? undefined, session: this.session, dialect: this.dialect });\n\t}\n\n\t/**\n\t * Adds `distinct` expression to the select query.\n\t *\n\t * Calling this method will return only unique values. When multiple columns are selected, it returns rows with unique combinations of values in these columns.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t *\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all unique rows from the 'cars' table\n\t * await db.selectDistinct()\n\t *   .from(cars)\n\t *   .orderBy(cars.id, cars.brand, cars.color);\n\t *\n\t * // Select all unique brands from the 'cars' table\n\t * await db.selectDistinct({ brand: cars.brand })\n\t *   .from(cars)\n\t *   .orderBy(cars.brand);\n\t * ```\n\t */\n\tselectDistinct(): SQLiteSelectBuilder<undefined, TResultKind, TRunResult>;\n\tselectDistinct<TSelection extends SelectedFields>(\n\t\tfields: TSelection,\n\t): SQLiteSelectBuilder<TSelection, TResultKind, TRunResult>;\n\tselectDistinct(\n\t\tfields?: SelectedFields,\n\t): SQLiteSelectBuilder<SelectedFields | undefined, TResultKind, TRunResult> {\n\t\treturn new SQLiteSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t\tdistinct: true,\n\t\t});\n\t}\n\n\t/**\n\t * Creates an update query.\n\t *\n\t * Calling this method without `.where()` clause will update all rows in a table. The `.where()` clause specifies which rows should be updated.\n\t *\n\t * Use `.set()` method to specify which values to update.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t *\n\t * @param table The table to update.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Update all rows in the 'cars' table\n\t * await db.update(cars).set({ color: 'red' });\n\t *\n\t * // Update rows with filters and conditions\n\t * await db.update(cars).set({ color: 'red' }).where(eq(cars.brand, 'BMW'));\n\t *\n\t * // Update with returning clause\n\t * const updatedCar: Car[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.id, 1))\n\t *   .returning();\n\t * ```\n\t */\n\tupdate<TTable extends SQLiteTable>(table: TTable): SQLiteUpdateBuilder<TTable, TResultKind, TRunResult> {\n\t\treturn new SQLiteUpdateBuilder(table, this.session, this.dialect);\n\t}\n\n\t/**\n\t * Creates an insert query.\n\t *\n\t * Calling this method will create new rows in a table. Use `.values()` method to specify which values to insert.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert}\n\t *\n\t * @param table The table to insert into.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Insert one row\n\t * await db.insert(cars).values({ brand: 'BMW' });\n\t *\n\t * // Insert multiple rows\n\t * await db.insert(cars).values([{ brand: 'BMW' }, { brand: 'Porsche' }]);\n\t *\n\t * // Insert with returning clause\n\t * const insertedCar: Car[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning();\n\t * ```\n\t */\n\tinsert<TTable extends SQLiteTable>(into: TTable): SQLiteInsertBuilder<TTable, TResultKind, TRunResult> {\n\t\treturn new SQLiteInsertBuilder(into, this.session, this.dialect);\n\t}\n\n\t/**\n\t * Creates a delete query.\n\t *\n\t * Calling this method without `.where()` clause will delete all rows in a table. The `.where()` clause specifies which rows should be deleted.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t *\n\t * @param table The table to delete from.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Delete all rows in the 'cars' table\n\t * await db.delete(cars);\n\t *\n\t * // Delete rows with filters and conditions\n\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t *\n\t * // Delete with returning clause\n\t * const deletedCar: Car[] = await db.delete(cars)\n\t *   .where(eq(cars.id, 1))\n\t *   .returning();\n\t * ```\n\t */\n\tdelete<TTable extends SQLiteTable>(from: TTable): SQLiteDeleteBase<TTable, TResultKind, TRunResult> {\n\t\treturn new SQLiteDeleteBase(from, this.session, this.dialect);\n\t}\n\n\trun(query: SQLWrapper | string): DBResult<TResultKind, TRunResult> {\n\t\tconst sequel = typeof query === 'string' ? sql.raw(query) : query.getSQL();\n\t\tif (this.resultKind === 'async') {\n\t\t\treturn new SQLiteRaw(\n\t\t\t\tasync () => this.session.run(sequel),\n\t\t\t\t() => sequel,\n\t\t\t\t'run',\n\t\t\t\tthis.dialect as SQLiteAsyncDialect,\n\t\t\t\tthis.session.extractRawRunValueFromBatchResult.bind(this.session),\n\t\t\t) as DBResult<TResultKind, TRunResult>;\n\t\t}\n\t\treturn this.session.run(sequel) as DBResult<TResultKind, TRunResult>;\n\t}\n\n\tall<T = unknown>(query: SQLWrapper | string): DBResult<TResultKind, T[]> {\n\t\tconst sequel = typeof query === 'string' ? sql.raw(query) : query.getSQL();\n\t\tif (this.resultKind === 'async') {\n\t\t\treturn new SQLiteRaw(\n\t\t\t\tasync () => this.session.all(sequel),\n\t\t\t\t() => sequel,\n\t\t\t\t'all',\n\t\t\t\tthis.dialect as SQLiteAsyncDialect,\n\t\t\t\tthis.session.extractRawAllValueFromBatchResult.bind(this.session),\n\t\t\t) as any;\n\t\t}\n\t\treturn this.session.all(sequel) as DBResult<TResultKind, T[]>;\n\t}\n\n\tget<T = unknown>(query: SQLWrapper | string): DBResult<TResultKind, T> {\n\t\tconst sequel = typeof query === 'string' ? sql.raw(query) : query.getSQL();\n\t\tif (this.resultKind === 'async') {\n\t\t\treturn new SQLiteRaw(\n\t\t\t\tasync () => this.session.get(sequel),\n\t\t\t\t() => sequel,\n\t\t\t\t'get',\n\t\t\t\tthis.dialect as SQLiteAsyncDialect,\n\t\t\t\tthis.session.extractRawGetValueFromBatchResult.bind(this.session),\n\t\t\t) as DBResult<TResultKind, T>;\n\t\t}\n\t\treturn this.session.get(sequel) as DBResult<TResultKind, T>;\n\t}\n\n\tvalues<T extends unknown[] = unknown[]>(query: SQLWrapper | string): DBResult<TResultKind, T[]> {\n\t\tconst sequel = typeof query === 'string' ? sql.raw(query) : query.getSQL();\n\t\tif (this.resultKind === 'async') {\n\t\t\treturn new SQLiteRaw(\n\t\t\t\tasync () => this.session.values(sequel),\n\t\t\t\t() => sequel,\n\t\t\t\t'values',\n\t\t\t\tthis.dialect as SQLiteAsyncDialect,\n\t\t\t\tthis.session.extractRawValuesValueFromBatchResult.bind(this.session),\n\t\t\t) as any;\n\t\t}\n\t\treturn this.session.values(sequel) as DBResult<TResultKind, T[]>;\n\t}\n\n\ttransaction<T>(\n\t\ttransaction: (tx: SQLiteTransaction<TResultKind, TRunResult, TFullSchema, TSchema>) => Result<TResultKind, T>,\n\t\tconfig?: SQLiteTransactionConfig,\n\t): Result<TResultKind, T> {\n\t\treturn this.session.transaction(transaction, config);\n\t}\n}\n\nexport type SQLiteWithReplicas<Q> = Q & { $primary: Q };\n\nexport const withReplicas = <\n\tTResultKind extends 'sync' | 'async',\n\tTRunResult,\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n\tQ extends BaseSQLiteDatabase<\n\t\tTResultKind,\n\t\tTRunResult,\n\t\tTFullSchema,\n\t\tTSchema extends Record<string, unknown> ? ExtractTablesWithRelations<TFullSchema> : TSchema\n\t>,\n>(\n\tprimary: Q,\n\treplicas: [Q, ...Q[]],\n\tgetReplica: (replicas: Q[]) => Q = () => replicas[Math.floor(Math.random() * replicas.length)]!,\n): SQLiteWithReplicas<Q> => {\n\tconst select: Q['select'] = (...args: []) => getReplica(replicas).select(...args);\n\tconst selectDistinct: Q['selectDistinct'] = (...args: []) => getReplica(replicas).selectDistinct(...args);\n\tconst $count: Q['$count'] = (...args: [any]) => getReplica(replicas).$count(...args);\n\tconst $with: Q['with'] = (...args: []) => getReplica(replicas).with(...args);\n\n\tconst update: Q['update'] = (...args: [any]) => primary.update(...args);\n\tconst insert: Q['insert'] = (...args: [any]) => primary.insert(...args);\n\tconst $delete: Q['delete'] = (...args: [any]) => primary.delete(...args);\n\tconst run: Q['run'] = (...args: [any]) => primary.run(...args);\n\tconst all: Q['all'] = (...args: [any]) => primary.all(...args);\n\tconst get: Q['get'] = (...args: [any]) => primary.get(...args);\n\tconst values: Q['values'] = (...args: [any]) => primary.values(...args);\n\tconst transaction: Q['transaction'] = (...args: [any]) => primary.transaction(...args);\n\n\treturn {\n\t\t...primary,\n\t\tupdate,\n\t\tinsert,\n\t\tdelete: $delete,\n\t\trun,\n\t\tall,\n\t\tget,\n\t\tvalues,\n\t\ttransaction,\n\t\t$primary: primary,\n\t\tselect,\n\t\tselectDistinct,\n\t\t$count,\n\t\twith: $with,\n\t\tget query() {\n\t\t\treturn getReplica(replicas).query;\n\t\t},\n\t};\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAG3B,6BAAsC;AACtC,iBAAsE;AAEtE,4BAMO;AASP,sBAA6B;AAE7B,mBAAmC;AACnC,mBAAuC;AACvC,iBAA0B;AAKnB,MAAM,mBAKX;AAAA,EAeD,YACS,YAEC,SAEA,SACT,QACC;AANO;AAEC;AAEA;AAGT,SAAK,IAAI,SACN;AAAA,MACD,QAAQ,OAAO;AAAA,MACf,YAAY,OAAO;AAAA,MACnB,eAAe,OAAO;AAAA,IACvB,IACE;AAAA,MACD,QAAQ;AAAA,MACR,YAAY,CAAC;AAAA,MACb,eAAe,CAAC;AAAA,IACjB;AACD,SAAK,QAAQ,CAAC;AACd,UAAM,QAAQ,KAAK;AAGnB,QAAI,KAAK,EAAE,QAAQ;AAClB,iBAAW,CAAC,WAAW,OAAO,KAAK,OAAO,QAAQ,KAAK,EAAE,MAAM,GAAG;AACjE,cAAM,SAA0B,IAAI,IAAI;AAAA,UACvC;AAAA,UACA,OAAQ;AAAA,UACR,KAAK,EAAE;AAAA,UACP,KAAK,EAAE;AAAA,UACP,OAAQ,WAAW,SAAS;AAAA,UAC5B;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EAnDA,QAAiB,wBAAU,IAAY;AAAA,EAQvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6EA,QAAqB,CAAC,OAAe,cAAiC;AACrE,UAAM,OAAO;AACb,UAAM,KAAK,CACV,OAII;AACJ,UAAI,OAAO,OAAO,YAAY;AAC7B,aAAK,GAAG,IAAI,mCAAa,KAAK,OAAO,CAAC;AAAA,MACvC;AAEA,aAAO,IAAI;AAAA,QACV,IAAI;AAAA,UACH,GAAG,OAAO;AAAA,UACV,cAAc,uBAAuB,KAAK,GAAG,kBAAkB,KAAK,CAAC,IAAI,CAAC;AAAA,UAC1E;AAAA,UACA;AAAA,QACD;AAAA,QACA,IAAI,6CAAsB,EAAE,OAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;AAAA,MACvF;AAAA,IACD;AACA,WAAO,EAAE,GAAG;AAAA,EACb;AAAA,EAEA,OACC,QACA,SACC;AACD,WAAO,IAAI,gCAAmB,EAAE,QAAQ,SAAS,SAAS,KAAK,QAAQ,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,QAAQ,SAAyB;AAChC,UAAM,OAAO;AA0Cb,aAAS,OACR,QAC2E;AAC3E,aAAO,IAAI,0CAAoB;AAAA,QAC9B,QAAQ,UAAU;AAAA,QAClB,SAAS,KAAK;AAAA,QACd,SAAS,KAAK;AAAA,QACd,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AA+BA,aAAS,eACR,QAC2E;AAC3E,aAAO,IAAI,0CAAoB;AAAA,QAC9B,QAAQ,UAAU;AAAA,QAClB,SAAS,KAAK;AAAA,QACd,SAAS,KAAK;AAAA,QACd,UAAU;AAAA,QACV,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AA6BA,aAAS,OAAmC,OAAqE;AAChH,aAAO,IAAI,0CAAoB,OAAO,KAAK,SAAS,KAAK,SAAS,OAAO;AAAA,IAC1E;AA0BA,aAAS,OAAmC,MAAoE;AAC/G,aAAO,IAAI,0CAAoB,MAAM,KAAK,SAAS,KAAK,SAAS,OAAO;AAAA,IACzE;AA0BA,aAAS,QAAoC,MAAiE;AAC7G,aAAO,IAAI,uCAAiB,MAAM,KAAK,SAAS,KAAK,SAAS,OAAO;AAAA,IACtE;AAEA,WAAO,EAAE,QAAQ,gBAAgB,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,EAClE;AAAA,EA0CA,OAAO,QAAmG;AACzG,WAAO,IAAI,0CAAoB,EAAE,QAAQ,UAAU,QAAW,SAAS,KAAK,SAAS,SAAS,KAAK,QAAQ,CAAC;AAAA,EAC7G;AAAA,EA+BA,eACC,QAC2E;AAC3E,WAAO,IAAI,0CAAoB;AAAA,MAC9B,QAAQ,UAAU;AAAA,MAClB,SAAS,KAAK;AAAA,MACd,SAAS,KAAK;AAAA,MACd,UAAU;AAAA,IACX,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BA,OAAmC,OAAqE;AACvG,WAAO,IAAI,0CAAoB,OAAO,KAAK,SAAS,KAAK,OAAO;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BA,OAAmC,MAAoE;AACtG,WAAO,IAAI,0CAAoB,MAAM,KAAK,SAAS,KAAK,OAAO;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BA,OAAmC,MAAiE;AACnG,WAAO,IAAI,uCAAiB,MAAM,KAAK,SAAS,KAAK,OAAO;AAAA,EAC7D;AAAA,EAEA,IAAI,OAA+D;AAClE,UAAM,SAAS,OAAO,UAAU,WAAW,eAAI,IAAI,KAAK,IAAI,MAAM,OAAO;AACzE,QAAI,KAAK,eAAe,SAAS;AAChC,aAAO,IAAI;AAAA,QACV,YAAY,KAAK,QAAQ,IAAI,MAAM;AAAA,QACnC,MAAM;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK,QAAQ,kCAAkC,KAAK,KAAK,OAAO;AAAA,MACjE;AAAA,IACD;AACA,WAAO,KAAK,QAAQ,IAAI,MAAM;AAAA,EAC/B;AAAA,EAEA,IAAiB,OAAwD;AACxE,UAAM,SAAS,OAAO,UAAU,WAAW,eAAI,IAAI,KAAK,IAAI,MAAM,OAAO;AACzE,QAAI,KAAK,eAAe,SAAS;AAChC,aAAO,IAAI;AAAA,QACV,YAAY,KAAK,QAAQ,IAAI,MAAM;AAAA,QACnC,MAAM;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK,QAAQ,kCAAkC,KAAK,KAAK,OAAO;AAAA,MACjE;AAAA,IACD;AACA,WAAO,KAAK,QAAQ,IAAI,MAAM;AAAA,EAC/B;AAAA,EAEA,IAAiB,OAAsD;AACtE,UAAM,SAAS,OAAO,UAAU,WAAW,eAAI,IAAI,KAAK,IAAI,MAAM,OAAO;AACzE,QAAI,KAAK,eAAe,SAAS;AAChC,aAAO,IAAI;AAAA,QACV,YAAY,KAAK,QAAQ,IAAI,MAAM;AAAA,QACnC,MAAM;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK,QAAQ,kCAAkC,KAAK,KAAK,OAAO;AAAA,MACjE;AAAA,IACD;AACA,WAAO,KAAK,QAAQ,IAAI,MAAM;AAAA,EAC/B;AAAA,EAEA,OAAwC,OAAwD;AAC/F,UAAM,SAAS,OAAO,UAAU,WAAW,eAAI,IAAI,KAAK,IAAI,MAAM,OAAO;AACzE,QAAI,KAAK,eAAe,SAAS;AAChC,aAAO,IAAI;AAAA,QACV,YAAY,KAAK,QAAQ,OAAO,MAAM;AAAA,QACtC,MAAM;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK,QAAQ,qCAAqC,KAAK,KAAK,OAAO;AAAA,MACpE;AAAA,IACD;AACA,WAAO,KAAK,QAAQ,OAAO,MAAM;AAAA,EAClC;AAAA,EAEA,YACC,aACA,QACyB;AACzB,WAAO,KAAK,QAAQ,YAAY,aAAa,MAAM;AAAA,EACpD;AACD;AAIO,MAAM,eAAe,CAY3B,SACA,UACA,aAAmC,MAAM,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,SAAS,MAAM,CAAC,MAClE;AAC3B,QAAM,SAAsB,IAAI,SAAa,WAAW,QAAQ,EAAE,OAAO,GAAG,IAAI;AAChF,QAAM,iBAAsC,IAAI,SAAa,WAAW,QAAQ,EAAE,eAAe,GAAG,IAAI;AACxG,QAAM,SAAsB,IAAI,SAAgB,WAAW,QAAQ,EAAE,OAAO,GAAG,IAAI;AACnF,QAAM,QAAmB,IAAI,SAAa,WAAW,QAAQ,EAAE,KAAK,GAAG,IAAI;AAE3E,QAAM,SAAsB,IAAI,SAAgB,QAAQ,OAAO,GAAG,IAAI;AACtE,QAAM,SAAsB,IAAI,SAAgB,QAAQ,OAAO,GAAG,IAAI;AACtE,QAAM,UAAuB,IAAI,SAAgB,QAAQ,OAAO,GAAG,IAAI;AACvE,QAAM,MAAgB,IAAI,SAAgB,QAAQ,IAAI,GAAG,IAAI;AAC7D,QAAM,MAAgB,IAAI,SAAgB,QAAQ,IAAI,GAAG,IAAI;AAC7D,QAAM,MAAgB,IAAI,SAAgB,QAAQ,IAAI,GAAG,IAAI;AAC7D,QAAM,SAAsB,IAAI,SAAgB,QAAQ,OAAO,GAAG,IAAI;AACtE,QAAM,cAAgC,IAAI,SAAgB,QAAQ,YAAY,GAAG,IAAI;AAErF,SAAO;AAAA,IACN,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,IAAI,QAAQ;AACX,aAAO,WAAW,QAAQ,EAAE;AAAA,IAC7B;AAAA,EACD;AACD;", "names": []}