/**
 * Create Admin User Script (New ORM Version)
 * 
 * This script creates a new admin user using the new database-agnostic ORM structure.
 * It supports SQLite, MySQL, and PostgreSQL databases.
 * 
 * Following DRY principle: Uses repository pattern
 * Following YAGNI principle: Essential user creation only
 */

const path = require('path');
const fs = require('fs');

/**
 * Simple user creation using direct database operations
 * This avoids TypeScript compilation issues in Node.js scripts
 */
async function createAdminUser(email = '<EMAIL>', password = '123', name = 'Admin User') {
  try {
    console.log('🚀 Starting admin user creation...');
    console.log('');

    // Validate input
    if (!email || !password || !name) {
      throw new Error('Email, password, and name are required');
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format');
    }

    // Password validation
    if (password.length < 3) {
      throw new Error('Password must be at least 3 characters long');
    }

    // Name validation
    if (name.trim().length === 0) {
      throw new Error('Name cannot be empty');
    }

    // Import required modules
    const bcrypt = require('bcryptjs');
    const Database = require('better-sqlite3');

    // Ensure data directory exists
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
      console.log('📁 Created data directory');
    }

    // Database path
    const dbPath = path.join(dataDir, 'accounting.db');

    // Connect to database
    console.log('🔌 Connecting to database...');
    const db = new Database(dbPath);

    // Configure database
    db.pragma('journal_mode = WAL');
    db.pragma('foreign_keys = ON');
    db.pragma('synchronous = NORMAL');

    console.log('✅ Database connected successfully');

    // Create users table if it doesn't exist
    console.log('📋 Ensuring users table exists...');
    db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        password_hash TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Check if user already exists
    console.log(`🔍 Checking if user ${email} already exists...`);
    const existingUser = db.prepare('SELECT id, email FROM users WHERE email = ?').get(email);

    if (existingUser) {
      console.log(`⚠️  User ${email} already exists with ID: ${existingUser.id}`);
      console.log('   No action taken.');
      db.close();
      return;
    }

    // Hash password
    console.log('🔐 Hashing password...');
    const passwordHash = await bcrypt.hash(password, 10);

    // Generate user ID
    const userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Insert user
    console.log(`👤 Creating user ${email}...`);
    const insertUser = db.prepare(`
      INSERT INTO users (id, email, name, password_hash)
      VALUES (?, ?, ?, ?)
    `);

    const result = insertUser.run(userId, email.toLowerCase().trim(), name.trim(), passwordHash);

    if (result.changes > 0) {
      console.log('✅ Admin user created successfully!');
      console.log('');
      console.log('📧 Login Credentials:');
      console.log(`   Email: ${email}`);
      console.log(`   Password: ${password}`);
      console.log(`   Name: ${name}`);
      console.log(`   User ID: ${userId}`);
      console.log('');
      console.log('🚀 You can now login with these credentials!');
    } else {
      throw new Error('Failed to create user - no rows affected');
    }

    // Close database
    db.close();
    console.log('🔌 Database connection closed');

  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
    throw error;
  }
}

/**
 * Main function to handle command line arguments
 */
async function main() {
  try {
    // Get command line arguments
    const args = process.argv.slice(2);

    // Check for help flag
    if (args.includes('--help') || args.includes('-h')) {
      showHelp();
      return;
    }

    // Default values
    let email = '<EMAIL>';
    let password = '123';
    let name = 'Admin User';

    // Parse command line arguments
    if (args.length >= 1) email = args[0];
    if (args.length >= 2) password = args[1];
    if (args.length >= 3) name = args[2];

    // Create the admin user
    await createAdminUser(email, password, name);

  } catch (error) {
    console.error('❌ Script failed:', error.message);
    console.log('');
    console.log('📖 Usage:');
    console.log('   node scripts/create-admin-new.js [email] [password] [name]');
    console.log('');
    console.log('📝 Examples:');
    console.log('   node scripts/create-admin-new.js');
    console.log('   node scripts/create-admin-new.js <EMAIL> 123');
    console.log('   node scripts/create-admin-new.js <EMAIL> 123 "Admin User"');
    console.log('   node scripts/create-admin-new.js <EMAIL> mypassword "John Doe"');

    process.exit(1);
  }
}

/**
 * Display help information
 */
function showHelp() {
  console.log('📖 Create Admin User Script (New ORM Version)');
  console.log('');
  console.log('This script creates a new admin user in the accounting application.');
  console.log('It uses the new database-agnostic ORM structure.');
  console.log('');
  console.log('📝 Usage:');
  console.log('   node scripts/create-admin-new.js [email] [password] [name]');
  console.log('');
  console.log('📋 Parameters:');
  console.log('   email    - User email address (default: <EMAIL>)');
  console.log('   password - User password (default: 123)');
  console.log('   name     - User display name (default: Admin User)');
  console.log('');
  console.log('📝 Examples:');
  console.log('   node scripts/create-admin-new.js');
  console.log('   # Creates: <EMAIL> / 123 / Admin User');
  console.log('');
  console.log('   node scripts/create-admin-new.js <EMAIL> 123');
  console.log('   # Creates: <EMAIL> / 123 / Admin User');
  console.log('');
  console.log('   node scripts/create-admin-new.js <EMAIL> 123 "Super Admin"');
  console.log('   # Creates: <EMAIL> / 123 / Super Admin');
  console.log('');
  console.log('   node scripts/create-admin-new.js <EMAIL> mypassword "John Doe"');
  console.log('   # Creates: <EMAIL> / mypassword / John Doe');
  console.log('');
  console.log('🌐 Database Support:');
  console.log('   - SQLite (default)');
  console.log('   - MySQL (set DATABASE_TYPE=mysql)');
  console.log('   - PostgreSQL (set DATABASE_TYPE=postgresql)');
  console.log('');
  console.log('🔧 Environment Variables:');
  console.log('   DATABASE_TYPE     - Database type (sqlite|mysql|postgresql)');
  console.log('   DATABASE_HOST     - Database host (for MySQL/PostgreSQL)');
  console.log('   DATABASE_PORT     - Database port (for MySQL/PostgreSQL)');
  console.log('   DATABASE_NAME     - Database name');
  console.log('   DATABASE_USER     - Database username (for MySQL/PostgreSQL)');
  console.log('   DATABASE_PASSWORD - Database password (for MySQL/PostgreSQL)');
}

// Run the script if executed directly
if (require.main === module) {
  main();
}

module.exports = { createAdminUser };
