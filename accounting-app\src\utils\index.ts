/**
 * Utility Functions
 * 
 * This file contains reusable utility functions used throughout the application.
 * These functions provide common functionality like formatting, validation, and data manipulation.
 * Centralizing utilities here promotes code reuse and consistency.
 */

import { VALIDATION, REGEX, DATE_FORMATS } from '@/constants';

// ============================================================================
// STRING UTILITIES
// ============================================================================

/**
 * Capitalizes the first letter of a string
 * @param str - The string to capitalize
 * @returns The string with the first letter capitalized
 * 
 * @example
 * capitalize('hello world') // Returns: 'Hello world'
 */
export const capitalize = (str: string): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

/**
 * Converts a string to title case (capitalizes each word)
 * @param str - The string to convert
 * @returns The string in title case
 * 
 * @example
 * toTitleCase('hello world') // Returns: 'Hello World'
 */
export const toTitleCase = (str: string): string => {
  if (!str) return '';
  return str
    .toLowerCase()
    .split(' ')
    .map(word => capitalize(word))
    .join(' ');
};

/**
 * Truncates a string to a specified length and adds ellipsis
 * @param str - The string to truncate
 * @param maxLength - Maximum length before truncation
 * @returns The truncated string with ellipsis if needed
 * 
 * @example
 * truncate('This is a long string', 10) // Returns: 'This is a...'
 */
export const truncate = (str: string, maxLength: number): string => {
  if (!str || str.length <= maxLength) return str;
  return str.slice(0, maxLength).trim() + '...';
};

/**
 * Generates a random string of specified length
 * @param length - Length of the random string
 * @param includeNumbers - Whether to include numbers (default: true)
 * @returns A random string
 * 
 * @example
 * generateRandomString(8) // Returns: 'aB3dE7gH'
 */
export const generateRandomString = (length: number, includeNumbers: boolean = true): string => {
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';
  const chars = includeNumbers ? letters + numbers : letters;
  
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// ============================================================================
// NUMBER AND CURRENCY UTILITIES
// ============================================================================

/**
 * Formats a number as currency with proper decimal places
 * @param amount - The number to format
 * @param currency - Currency symbol (default: '$')
 * @param locale - Locale for formatting (default: 'en-US')
 * @returns Formatted currency string
 * 
 * @example
 * formatCurrency(1234.56) // Returns: '$1,234.56'
 * formatCurrency(1234.56, '₹', 'en-IN') // Returns: '₹1,234.56'
 */
export const formatCurrency = (
  amount: number, 
  currency: string = '$', 
  locale: string = 'en-US'
): string => {
  if (isNaN(amount)) return `${currency}0.00`;
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency === '$' ? 'USD' : 'INR',
    minimumFractionDigits: VALIDATION.CURRENCY_DECIMAL_PLACES,
    maximumFractionDigits: VALIDATION.CURRENCY_DECIMAL_PLACES,
  }).format(amount);
};

/**
 * Formats a number with thousand separators
 * @param num - The number to format
 * @param locale - Locale for formatting (default: 'en-US')
 * @returns Formatted number string
 * 
 * @example
 * formatNumber(1234567) // Returns: '1,234,567'
 */
export const formatNumber = (num: number, locale: string = 'en-US'): string => {
  if (isNaN(num)) return '0';
  return new Intl.NumberFormat(locale).format(num);
};

/**
 * Rounds a number to specified decimal places
 * @param num - The number to round
 * @param decimals - Number of decimal places (default: 2)
 * @returns Rounded number
 * 
 * @example
 * roundToDecimals(3.14159, 2) // Returns: 3.14
 */
export const roundToDecimals = (num: number, decimals: number = 2): number => {
  const factor = Math.pow(10, decimals);
  return Math.round(num * factor) / factor;
};

/**
 * Converts a string to a number, handling currency symbols and commas
 * @param str - The string to convert
 * @returns The parsed number or 0 if invalid
 * 
 * @example
 * parseAmount('$1,234.56') // Returns: 1234.56
 * parseAmount('1,234.56') // Returns: 1234.56
 */
export const parseAmount = (str: string): number => {
  if (!str) return 0;
  // Remove currency symbols, commas, and spaces
  const cleaned = str.replace(/[$,\s]/g, '');
  const parsed = parseFloat(cleaned);
  return isNaN(parsed) ? 0 : parsed;
};

// ============================================================================
// DATE UTILITIES
// ============================================================================

/**
 * Formats a date string or Date object to a readable format
 * @param date - The date to format (string or Date object)
 * @param format - The format to use (from DATE_FORMATS constant)
 * @returns Formatted date string
 * 
 * @example
 * formatDate('2024-01-15') // Returns: 'Jan 15, 2024'
 * formatDate(new Date(), 'SHORT') // Returns: '01/15/2024'
 */
export const formatDate = (date: string | Date, format: keyof typeof DATE_FORMATS = 'DISPLAY'): string => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  if (isNaN(dateObj.getTime())) return '';
  
  // Simple formatting based on format type
  switch (format) {
    case 'DISPLAY':
      return dateObj.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      });
    case 'SHORT':
      return dateObj.toLocaleDateString('en-US');
    case 'ISO':
      return dateObj.toISOString().split('T')[0];
    case 'DATETIME':
      return dateObj.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    case 'TIME':
      return dateObj.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit', 
        hour12: true 
      });
    default:
      return dateObj.toLocaleDateString('en-US');
  }
};

/**
 * Gets the relative time string (e.g., "2 hours ago", "3 days ago")
 * @param date - The date to compare (string or Date object)
 * @returns Relative time string
 * 
 * @example
 * getRelativeTime('2024-01-15T10:00:00Z') // Returns: '2 hours ago'
 */
export const getRelativeTime = (date: string | Date): string => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  if (isNaN(dateObj.getTime())) return '';
  
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
  
  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;
  return `${Math.floor(diffInSeconds / 31536000)} years ago`;
};

/**
 * Checks if a date is within a financial year
 * @param date - The date to check
 * @param startDate - Financial year start date
 * @param endDate - Financial year end date
 * @returns True if date is within the financial year
 * 
 * @example
 * isDateInFinancialYear('2024-06-15', '2024-04-01', '2025-03-31') // Returns: true
 */
export const isDateInFinancialYear = (
  date: string | Date, 
  startDate: string | Date, 
  endDate: string | Date
): boolean => {
  const checkDate = new Date(date);
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  return checkDate >= start && checkDate <= end;
};

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validates an email address
 * @param email - The email to validate
 * @returns True if email is valid
 * 
 * @example
 * isValidEmail('<EMAIL>') // Returns: true
 * isValidEmail('invalid-email') // Returns: false
 */
export const isValidEmail = (email: string): boolean => {
  if (!email) return false;
  return REGEX.EMAIL.test(email);
};

/**
 * Validates a password based on application rules
 * @param password - The password to validate
 * @returns Object with validation result and error message
 * 
 * @example
 * validatePassword('123456') // Returns: { isValid: true, error: null }
 * validatePassword('123') // Returns: { isValid: false, error: 'Password too short' }
 */
export const validatePassword = (password: string): { isValid: boolean; error: string | null } => {
  if (!password) {
    return { isValid: false, error: 'Password is required' };
  }
  
  if (password.length < VALIDATION.PASSWORD_MIN_LENGTH) {
    return { 
      isValid: false, 
      error: `Password must be at least ${VALIDATION.PASSWORD_MIN_LENGTH} characters long` 
    };
  }
  
  if (password.length > VALIDATION.PASSWORD_MAX_LENGTH) {
    return { 
      isValid: false, 
      error: `Password must be no more than ${VALIDATION.PASSWORD_MAX_LENGTH} characters long` 
    };
  }
  
  return { isValid: true, error: null };
};

/**
 * Validates an account code format
 * @param code - The account code to validate
 * @returns True if account code is valid
 * 
 * @example
 * isValidAccountCode('1001') // Returns: true
 * isValidAccountCode('ACC-001') // Returns: true
 * isValidAccountCode('invalid code!') // Returns: false
 */
export const isValidAccountCode = (code: string): boolean => {
  if (!code) return false;
  if (code.length > VALIDATION.ACCOUNT_CODE_MAX_LENGTH) return false;
  return REGEX.ACCOUNT_CODE.test(code);
};

// ============================================================================
// ARRAY AND OBJECT UTILITIES
// ============================================================================

/**
 * Removes duplicate items from an array based on a key
 * @param array - The array to deduplicate
 * @param key - The key to use for comparison
 * @returns Array with duplicates removed
 * 
 * @example
 * const users = [{ id: 1, name: 'John' }, { id: 1, name: 'John' }, { id: 2, name: 'Jane' }];
 * uniqueBy(users, 'id') // Returns: [{ id: 1, name: 'John' }, { id: 2, name: 'Jane' }]
 */
export const uniqueBy = <T>(array: T[], key: keyof T): T[] => {
  const seen = new Set();
  return array.filter(item => {
    const value = item[key];
    if (seen.has(value)) {
      return false;
    }
    seen.add(value);
    return true;
  });
};

/**
 * Groups an array of objects by a specified key
 * @param array - The array to group
 * @param key - The key to group by
 * @returns Object with grouped items
 * 
 * @example
 * const items = [{ type: 'A', value: 1 }, { type: 'B', value: 2 }, { type: 'A', value: 3 }];
 * groupBy(items, 'type') // Returns: { A: [{ type: 'A', value: 1 }, { type: 'A', value: 3 }], B: [{ type: 'B', value: 2 }] }
 */
export const groupBy = <T>(array: T[], key: keyof T): Record<string, T[]> => {
  return array.reduce((groups, item) => {
    const groupKey = String(item[key]);
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(item);
    return groups;
  }, {} as Record<string, T[]>);
};

/**
 * Sorts an array of objects by a specified key
 * @param array - The array to sort
 * @param key - The key to sort by
 * @param direction - Sort direction ('asc' or 'desc')
 * @returns Sorted array
 * 
 * @example
 * const items = [{ name: 'Charlie' }, { name: 'Alice' }, { name: 'Bob' }];
 * sortBy(items, 'name', 'asc') // Returns: [{ name: 'Alice' }, { name: 'Bob' }, { name: 'Charlie' }]
 */
export const sortBy = <T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] => {
  return [...array].sort((a, b) => {
    const aValue = a[key];
    const bValue = b[key];
    
    if (aValue < bValue) return direction === 'asc' ? -1 : 1;
    if (aValue > bValue) return direction === 'asc' ? 1 : -1;
    return 0;
  });
};

// ============================================================================
// DEBOUNCE AND THROTTLE UTILITIES
// ============================================================================

/**
 * Creates a debounced function that delays execution until after delay milliseconds
 * @param func - The function to debounce
 * @param delay - The delay in milliseconds
 * @returns Debounced function
 * 
 * @example
 * const debouncedSearch = debounce((query) => search(query), 300);
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Creates a throttled function that only executes at most once per delay milliseconds
 * @param func - The function to throttle
 * @param delay - The delay in milliseconds
 * @returns Throttled function
 * 
 * @example
 * const throttledScroll = throttle(() => handleScroll(), 100);
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
};
