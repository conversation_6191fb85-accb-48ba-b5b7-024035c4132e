{"version": 3, "sources": ["../../src/sqlite-core/expressions.ts"], "sourcesContent": ["import { bindIfParam } from '~/sql/expressions/index.ts';\nimport type { SQL, SQLChunk, SQLWrapper } from '~/sql/sql.ts';\nimport { sql } from '~/sql/sql.ts';\nimport type { SQLiteColumn } from '~/sqlite-core/columns/index.ts';\n\nexport * from '~/sql/expressions/index.ts';\n\nexport function concat(column: SQLiteColumn | SQL.Aliased, value: string | SQLWrapper): SQL {\n\treturn sql`${column} || ${bindIfParam(value, column)}`;\n}\n\nexport function substring(\n\tcolumn: SQLiteColumn | SQL.Aliased,\n\t{ from, for: _for }: { from?: number | SQLWrapper; for?: number | SQLWrapper },\n): SQL {\n\tconst chunks: SQLChunk[] = [sql`substring(`, column];\n\tif (from !== undefined) {\n\t\tchunks.push(sql` from `, bindIfParam(from, column));\n\t}\n\tif (_for !== undefined) {\n\t\tchunks.push(sql` for `, bindIfParam(_for, column));\n\t}\n\tchunks.push(sql`)`);\n\treturn sql.join(chunks);\n}\n\nexport function rowId(): SQL<number> {\n\treturn sql<number>`rowid`;\n}\n"], "mappings": "AAAA,SAAS,mBAAmB;AAE5B,SAAS,WAAW;AAGpB,cAAc;AAEP,SAAS,OAAO,QAAoC,OAAiC;AAC3F,SAAO,MAAM,MAAM,OAAO,YAAY,OAAO,MAAM,CAAC;AACrD;AAEO,SAAS,UACf,QACA,EAAE,MAAM,KAAK,KAAK,GACZ;AACN,QAAM,SAAqB,CAAC,iBAAiB,MAAM;AACnD,MAAI,SAAS,QAAW;AACvB,WAAO,KAAK,aAAa,YAAY,MAAM,MAAM,CAAC;AAAA,EACnD;AACA,MAAI,SAAS,QAAW;AACvB,WAAO,KAAK,YAAY,YAAY,MAAM,MAAM,CAAC;AAAA,EAClD;AACA,SAAO,KAAK,MAAM;AAClB,SAAO,IAAI,KAAK,MAAM;AACvB;AAEO,SAAS,QAAqB;AACpC,SAAO;AACR;", "names": []}