/**
 * useDebounce Hook
 * 
 * A custom React hook that debounces a value, delaying updates until after
 * a specified delay period has passed without the value changing.
 * This is useful for search inputs, API calls, and other scenarios where
 * you want to limit the frequency of updates.
 */

import { useState, useEffect } from 'react';

/**
 * Custom hook for debouncing a value
 * 
 * @template T - The type of the value being debounced
 * @param value - The value to debounce
 * @param delay - The delay in milliseconds before updating the debounced value
 * @returns The debounced value
 * 
 * @example
 * const [searchTerm, setSearchTerm] = useState('');
 * const debouncedSearchTerm = useDebounce(searchTerm, 300);
 * 
 * // Use debouncedSearchTerm for API calls
 * useEffect(() => {
 *   if (debouncedSearchTerm) {
 *     searchAPI(debouncedSearchTerm);
 *   }
 * }, [debouncedSearchTerm]);
 */
export function useDebounce<T>(value: T, delay: number): T {
  // State to store the debounced value
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    // Set up a timer to update the debounced value after the delay
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // Cleanup function to clear the timeout if value changes before delay
    // This ensures that the debounced value only updates after the delay
    // period has passed without the value changing
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]); // Re-run effect when value or delay changes

  return debouncedValue;
}

/**
 * useDebounceCallback Hook
 * 
 * A custom React hook that debounces a callback function, ensuring it's only
 * called after a specified delay period has passed without new calls.
 */

import { useCallback, useRef } from 'react';

/**
 * Custom hook for debouncing a callback function
 * 
 * @param callback - The function to debounce
 * @param delay - The delay in milliseconds before calling the function
 * @param deps - Dependencies array for the callback (similar to useCallback)
 * @returns The debounced callback function
 * 
 * @example
 * const debouncedSearch = useDebounceCallback(
 *   (query: string) => {
 *     // Perform search
 *     searchAPI(query);
 *   },
 *   300,
 *   []
 * );
 * 
 * // Use in an input handler
 * const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
 *   debouncedSearch(e.target.value);
 * };
 */
export function useDebounceCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList
): T {
  // Ref to store the timeout ID
  const timeoutRef = useRef<NodeJS.Timeout>();

  // Create the debounced callback using useCallback for optimization
  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set a new timeout to call the callback after the delay
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay, ...deps] // Include deps in the dependency array
  ) as T;

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
}
