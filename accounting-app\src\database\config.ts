/**
 * Database Configuration
 * 
 * This file provides database-agnostic configuration that supports
 * SQLite, MySQL, and PostgreSQL. Easy switching between databases.
 * 
 * Following DRY principle: Single configuration for all database types
 * Following YAGNI principle: Only essential configuration options
 */

import { config } from 'dotenv';

// Load environment variables
config();

/**
 * Supported database types
 */
export type DatabaseType = 'sqlite' | 'mysql' | 'postgresql';

/**
 * Database connection configuration interface
 */
export interface DatabaseConfig {
  type: DatabaseType;
  host?: string;
  port?: number;
  database: string;
  username?: string;
  password?: string;
  ssl?: boolean;
  poolMin?: number;
  poolMax?: number;
  connectionTimeout?: number;
  // SQLite specific
  filename?: string;
  // MySQL specific
  charset?: string;
  timezone?: string;
  // PostgreSQL specific
  schema?: string;
}

/**
 * Environment-based database configuration
 * Automatically detects database type from environment variables
 */
function getDatabaseConfig(): DatabaseConfig {
  const dbType = (process.env.DATABASE_TYPE || 'sqlite') as DatabaseType;
  
  const baseConfig = {
    type: dbType,
    poolMin: parseInt(process.env.DB_POOL_MIN || '2'),
    poolMax: parseInt(process.env.DB_POOL_MAX || '10'),
    connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '60000'),
  };

  switch (dbType) {
    case 'sqlite':
      return {
        ...baseConfig,
        database: process.env.DATABASE_NAME || 'accounting',
        filename: process.env.DATABASE_URL || './data/accounting.db',
      };

    case 'mysql':
      return {
        ...baseConfig,
        host: process.env.DATABASE_HOST || 'localhost',
        port: parseInt(process.env.DATABASE_PORT || '3306'),
        database: process.env.DATABASE_NAME || 'accounting',
        username: process.env.DATABASE_USER || 'root',
        password: process.env.DATABASE_PASSWORD || '',
        charset: process.env.DATABASE_CHARSET || 'utf8mb4',
        timezone: process.env.DATABASE_TIMEZONE || 'Z',
        ssl: process.env.DATABASE_SSL === 'true',
      };

    case 'postgresql':
      return {
        ...baseConfig,
        host: process.env.DATABASE_HOST || 'localhost',
        port: parseInt(process.env.DATABASE_PORT || '5432'),
        database: process.env.DATABASE_NAME || 'accounting',
        username: process.env.DATABASE_USER || 'postgres',
        password: process.env.DATABASE_PASSWORD || '',
        schema: process.env.DATABASE_SCHEMA || 'public',
        ssl: process.env.DATABASE_SSL === 'true',
      };

    default:
      throw new Error(`Unsupported database type: ${dbType}`);
  }
}

/**
 * Get database configuration
 */
export const databaseConfig = getDatabaseConfig();

/**
 * Database connection string builders
 */
export const connectionStrings = {
  mysql: (config: DatabaseConfig): string => {
    const { host, port, database, username, password, charset, timezone, ssl } = config;
    const params = new URLSearchParams();
    
    if (charset) params.set('charset', charset);
    if (timezone) params.set('timezone', timezone);
    if (ssl) params.set('ssl', 'true');
    
    const paramString = params.toString();
    const query = paramString ? `?${paramString}` : '';
    
    return `mysql://${username}:${password}@${host}:${port}/${database}${query}`;
  },

  postgresql: (config: DatabaseConfig): string => {
    const { host, port, database, username, password, schema, ssl } = config;
    const params = new URLSearchParams();
    
    if (schema) params.set('schema', schema);
    if (ssl) params.set('sslmode', 'require');
    
    const paramString = params.toString();
    const query = paramString ? `?${paramString}` : '';
    
    return `postgresql://${username}:${password}@${host}:${port}/${database}${query}`;
  },

  sqlite: (config: DatabaseConfig): string => {
    return config.filename || './data/accounting.db';
  },
};

/**
 * Get connection string for current database type
 */
export function getConnectionString(): string {
  const { type } = databaseConfig;
  
  switch (type) {
    case 'mysql':
      return connectionStrings.mysql(databaseConfig);
    case 'postgresql':
      return connectionStrings.postgresql(databaseConfig);
    case 'sqlite':
      return connectionStrings.sqlite(databaseConfig);
    default:
      throw new Error(`Unsupported database type: ${type}`);
  }
}

/**
 * Database feature flags
 * Different databases support different features
 */
export const databaseFeatures = {
  sqlite: {
    supportsReturning: false,
    supportsUpsert: true,
    supportsJsonColumns: true,
    supportsFullTextSearch: true,
    supportsArrays: false,
    maxConnections: 1,
    caseSensitive: false,
  },
  mysql: {
    supportsReturning: false, // MySQL 8.0+ supports it but not widely used
    supportsUpsert: true,
    supportsJsonColumns: true,
    supportsFullTextSearch: true,
    supportsArrays: false,
    maxConnections: 151, // Default max_connections
    caseSensitive: false,
  },
  postgresql: {
    supportsReturning: true,
    supportsUpsert: true,
    supportsJsonColumns: true,
    supportsFullTextSearch: true,
    supportsArrays: true,
    maxConnections: 100, // Default max_connections
    caseSensitive: true,
  },
};

/**
 * Get features for current database type
 */
export function getDatabaseFeatures() {
  return databaseFeatures[databaseConfig.type];
}

/**
 * Database-specific SQL dialects
 */
export const sqlDialects = {
  sqlite: {
    autoIncrement: 'AUTOINCREMENT',
    textType: 'TEXT',
    integerType: 'INTEGER',
    realType: 'REAL',
    booleanType: 'INTEGER',
    dateTimeType: 'DATETIME',
    jsonType: 'TEXT',
    limitOffset: (limit: number, offset: number) => `LIMIT ${limit} OFFSET ${offset}`,
    currentTimestamp: 'CURRENT_TIMESTAMP',
  },
  mysql: {
    autoIncrement: 'AUTO_INCREMENT',
    textType: 'TEXT',
    integerType: 'INT',
    realType: 'DECIMAL(15,2)',
    booleanType: 'BOOLEAN',
    dateTimeType: 'DATETIME',
    jsonType: 'JSON',
    limitOffset: (limit: number, offset: number) => `LIMIT ${offset}, ${limit}`,
    currentTimestamp: 'CURRENT_TIMESTAMP',
  },
  postgresql: {
    autoIncrement: 'SERIAL',
    textType: 'TEXT',
    integerType: 'INTEGER',
    realType: 'DECIMAL(15,2)',
    booleanType: 'BOOLEAN',
    dateTimeType: 'TIMESTAMP',
    jsonType: 'JSONB',
    limitOffset: (limit: number, offset: number) => `LIMIT ${limit} OFFSET ${offset}`,
    currentTimestamp: 'CURRENT_TIMESTAMP',
  },
};

/**
 * Get SQL dialect for current database type
 */
export function getSqlDialect() {
  return sqlDialects[databaseConfig.type];
}

/**
 * Pagination configuration
 */
export const paginationConfig = {
  defaultPageSize: 25,
  maxPageSize: 100,
  pageSizeOptions: [10, 25, 50, 100],
};

/**
 * Filter configuration
 */
export const filterConfig = {
  maxFilters: 10,
  supportedOperators: [
    'equals',
    'not_equals',
    'contains',
    'not_contains',
    'starts_with',
    'ends_with',
    'greater_than',
    'greater_than_or_equal',
    'less_than',
    'less_than_or_equal',
    'in',
    'not_in',
    'is_null',
    'is_not_null',
    'between',
  ] as const,
  supportedSortOrders: ['asc', 'desc'] as const,
};

export type FilterOperator = typeof filterConfig.supportedOperators[number];
export type SortOrder = typeof filterConfig.supportedSortOrders[number];
