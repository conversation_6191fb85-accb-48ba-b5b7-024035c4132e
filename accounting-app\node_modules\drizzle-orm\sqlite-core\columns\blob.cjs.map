{"version": 3, "sources": ["../../../src/sqlite-core/columns/blob.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySQLiteTable } from '~/sqlite-core/table.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { SQLiteColumn, SQLiteColumnBuilder } from './common.ts';\n\ntype BlobMode = 'buffer' | 'json' | 'bigint';\n\nexport type SQLiteBigIntBuilderInitial<TName extends string> = SQLiteBigIntBuilder<{\n\tname: TName;\n\tdataType: 'bigint';\n\tcolumnType: 'SQLiteBigInt';\n\tdata: bigint;\n\tdriverParam: Buffer;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteBigIntBuilder<T extends ColumnBuilderBaseConfig<'bigint', 'SQLiteBigInt'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteBigIntBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'bigint', 'SQLiteBigInt');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteBigInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteBigInt<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any>);\n\t}\n}\n\nexport class SQLiteBigInt<T extends ColumnBaseConfig<'bigint', 'SQLiteBigInt'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteBigInt';\n\n\tgetSQLType(): string {\n\t\treturn 'blob';\n\t}\n\n\toverride mapFromDriverValue(value: Buffer | Uint8Array | ArrayBuffer): bigint {\n\t\tif (Buffer.isBuffer(value)) {\n\t\t\treturn BigInt(value.toString());\n\t\t}\n\n\t\t// for sqlite durable objects\n\t\t// eslint-disable-next-line no-instanceof/no-instanceof\n\t\tif (value instanceof ArrayBuffer) {\n\t\t\tconst decoder = new TextDecoder();\n\t\t\treturn BigInt(decoder.decode(value));\n\t\t}\n\n\t\treturn BigInt(String.fromCodePoint(...value));\n\t}\n\n\toverride mapToDriverValue(value: bigint): Buffer {\n\t\treturn Buffer.from(value.toString());\n\t}\n}\n\nexport type SQLiteBlobJsonBuilderInitial<TName extends string> = SQLiteBlobJsonBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'SQLiteBlobJson';\n\tdata: unknown;\n\tdriverParam: Buffer;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteBlobJsonBuilder<T extends ColumnBuilderBaseConfig<'json', 'SQLiteBlobJson'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteBlobJsonBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'SQLiteBlobJson');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteBlobJson<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteBlobJson<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteBlobJson<T extends ColumnBaseConfig<'json', 'SQLiteBlobJson'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteBlobJson';\n\n\tgetSQLType(): string {\n\t\treturn 'blob';\n\t}\n\n\toverride mapFromDriverValue(value: Buffer | Uint8Array | ArrayBuffer): T['data'] {\n\t\tif (Buffer.isBuffer(value)) {\n\t\t\treturn JSON.parse(value.toString());\n\t\t}\n\n\t\t// for sqlite durable objects\n\t\t// eslint-disable-next-line no-instanceof/no-instanceof\n\t\tif (value instanceof ArrayBuffer) {\n\t\t\tconst decoder = new TextDecoder();\n\t\t\treturn JSON.parse(decoder.decode(value));\n\t\t}\n\n\t\treturn JSON.parse(String.fromCodePoint(...value));\n\t}\n\n\toverride mapToDriverValue(value: T['data']): Buffer {\n\t\treturn Buffer.from(JSON.stringify(value));\n\t}\n}\n\nexport type SQLiteBlobBufferBuilderInitial<TName extends string> = SQLiteBlobBufferBuilder<{\n\tname: TName;\n\tdataType: 'buffer';\n\tcolumnType: 'SQLiteBlobBuffer';\n\tdata: Buffer;\n\tdriverParam: Buffer;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteBlobBufferBuilder<T extends ColumnBuilderBaseConfig<'buffer', 'SQLiteBlobBuffer'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteBlobBufferBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'buffer', 'SQLiteBlobBuffer');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteBlobBuffer<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteBlobBuffer<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any>);\n\t}\n}\n\nexport class SQLiteBlobBuffer<T extends ColumnBaseConfig<'buffer', 'SQLiteBlobBuffer'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteBlobBuffer';\n\n\toverride mapFromDriverValue(value: Buffer | Uint8Array | ArrayBuffer): T['data'] {\n\t\tif (Buffer.isBuffer(value)) {\n\t\t\treturn value;\n\t\t}\n\n\t\treturn Buffer.from(value as Uint8Array);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'blob';\n\t}\n}\n\nexport interface BlobConfig<TMode extends BlobMode = BlobMode> {\n\tmode: TMode;\n}\n\n/**\n *  It's recommended to use `text('...', { mode: 'json' })` instead of `blob` in JSON mode, because it supports JSON functions:\n * >All JSON functions currently throw an error if any of their arguments are BLOBs because BLOBs are reserved for a future enhancement in which BLOBs will store the binary encoding for JSON.\n *\n * https://www.sqlite.org/json1.html\n */\nexport function blob(): SQLiteBlobJsonBuilderInitial<''>;\nexport function blob<TMode extends BlobMode = BlobMode>(\n\tconfig?: BlobConfig<TMode>,\n): Equal<TMode, 'bigint'> extends true ? SQLiteBigIntBuilderInitial<''>\n\t: Equal<TMode, 'buffer'> extends true ? SQLiteBlobBufferBuilderInitial<''>\n\t: SQLiteBlobJsonBuilderInitial<''>;\nexport function blob<TName extends string, TMode extends BlobMode = BlobMode>(\n\tname: TName,\n\tconfig?: BlobConfig<TMode>,\n): Equal<TMode, 'bigint'> extends true ? SQLiteBigIntBuilderInitial<TName>\n\t: Equal<TMode, 'buffer'> extends true ? SQLiteBlobBufferBuilderInitial<TName>\n\t: SQLiteBlobJsonBuilderInitial<TName>;\nexport function blob(a?: string | BlobConfig, b?: BlobConfig) {\n\tconst { name, config } = getColumnNameAndConfig<BlobConfig | undefined>(a, b);\n\tif (config?.mode === 'json') {\n\t\treturn new SQLiteBlobJsonBuilder(name);\n\t}\n\tif (config?.mode === 'bigint') {\n\t\treturn new SQLiteBigIntBuilder(name);\n\t}\n\treturn new SQLiteBlobBufferBuilder(name);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAmD;AACnD,oBAAkD;AAa3C,MAAM,4BACJ,kCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,cAAc;AAAA,EACrC;AAAA;AAAA,EAGS,MACR,OACgD;AAChD,WAAO,IAAI,aAA8C,OAAO,KAAK,MAAyC;AAAA,EAC/G;AACD;AAEO,MAAM,qBAA2E,2BAAgB;AAAA,EACvG,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAkD;AAC7E,QAAI,OAAO,SAAS,KAAK,GAAG;AAC3B,aAAO,OAAO,MAAM,SAAS,CAAC;AAAA,IAC/B;AAIA,QAAI,iBAAiB,aAAa;AACjC,YAAM,UAAU,IAAI,YAAY;AAChC,aAAO,OAAO,QAAQ,OAAO,KAAK,CAAC;AAAA,IACpC;AAEA,WAAO,OAAO,OAAO,cAAc,GAAG,KAAK,CAAC;AAAA,EAC7C;AAAA,EAES,iBAAiB,OAAuB;AAChD,WAAO,OAAO,KAAK,MAAM,SAAS,CAAC;AAAA,EACpC;AACD;AAWO,MAAM,8BACJ,kCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,QAAQ,gBAAgB;AAAA,EACrC;AAAA;AAAA,EAGS,MACR,OACkD;AAClD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,uBAA6E,2BAAgB;AAAA,EACzG,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAqD;AAChF,QAAI,OAAO,SAAS,KAAK,GAAG;AAC3B,aAAO,KAAK,MAAM,MAAM,SAAS,CAAC;AAAA,IACnC;AAIA,QAAI,iBAAiB,aAAa;AACjC,YAAM,UAAU,IAAI,YAAY;AAChC,aAAO,KAAK,MAAM,QAAQ,OAAO,KAAK,CAAC;AAAA,IACxC;AAEA,WAAO,KAAK,MAAM,OAAO,cAAc,GAAG,KAAK,CAAC;AAAA,EACjD;AAAA,EAES,iBAAiB,OAA0B;AACnD,WAAO,OAAO,KAAK,KAAK,UAAU,KAAK,CAAC;AAAA,EACzC;AACD;AAWO,MAAM,gCACJ,kCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,kBAAkB;AAAA,EACzC;AAAA;AAAA,EAGS,MACR,OACoD;AACpD,WAAO,IAAI,iBAAkD,OAAO,KAAK,MAAyC;AAAA,EACnH;AACD;AAEO,MAAM,yBAAmF,2BAAgB;AAAA,EAC/G,QAA0B,wBAAU,IAAY;AAAA,EAEvC,mBAAmB,OAAqD;AAChF,QAAI,OAAO,SAAS,KAAK,GAAG;AAC3B,aAAO;AAAA,IACR;AAEA,WAAO,OAAO,KAAK,KAAmB;AAAA,EACvC;AAAA,EAEA,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAwBO,SAAS,KAAK,GAAyB,GAAgB;AAC7D,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA+C,GAAG,CAAC;AAC5E,MAAI,QAAQ,SAAS,QAAQ;AAC5B,WAAO,IAAI,sBAAsB,IAAI;AAAA,EACtC;AACA,MAAI,QAAQ,SAAS,UAAU;AAC9B,WAAO,IAAI,oBAAoB,IAAI;AAAA,EACpC;AACA,SAAO,IAAI,wBAAwB,IAAI;AACxC;", "names": []}