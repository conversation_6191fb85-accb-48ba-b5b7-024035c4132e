{"version": 3, "sources": ["../../../src/singlestore-core/query-builders/insert.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { SingleStoreDialect } from '~/singlestore-core/dialect.ts';\nimport type {\n\tAnySingleStoreQueryResultHKT,\n\tPreparedQueryHKTBase,\n\tPreparedQueryKind,\n\tSingleStorePreparedQueryConfig,\n\tSingleStoreQueryResultHKT,\n\tSingleStoreQueryResultKind,\n\tSingleStoreSession,\n} from '~/singlestore-core/session.ts';\nimport type { SingleStoreTable } from '~/singlestore-core/table.ts';\nimport type { Placeholder, Query, SQLWrapper } from '~/sql/sql.ts';\nimport { Param, SQL, sql } from '~/sql/sql.ts';\nimport type { InferModelFromColumns } from '~/table.ts';\nimport { Table } from '~/table.ts';\nimport { mapUpdateSet, orderSelectedFields } from '~/utils.ts';\nimport type { AnySingleStoreColumn, SingleStoreColumn } from '../columns/common.ts';\nimport type { SelectedFieldsOrdered } from './select.types.ts';\nimport type { SingleStoreUpdateSetSource } from './update.ts';\n\nexport interface SingleStoreInsertConfig<TTable extends SingleStoreTable = SingleStoreTable> {\n\ttable: TTable;\n\tvalues: Record<string, Param | SQL>[];\n\tignore: boolean;\n\tonConflict?: SQL;\n\treturning?: SelectedFieldsOrdered;\n}\n\nexport type AnySingleStoreInsertConfig = SingleStoreInsertConfig<SingleStoreTable>;\n\nexport type SingleStoreInsertValue<TTable extends SingleStoreTable> =\n\t& {\n\t\t[Key in keyof TTable['$inferInsert']]: TTable['$inferInsert'][Key] | SQL | Placeholder;\n\t}\n\t& {};\n\nexport class SingleStoreInsertBuilder<\n\tTTable extends SingleStoreTable,\n\tTQueryResult extends SingleStoreQueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n> {\n\tstatic readonly [entityKind]: string = 'SingleStoreInsertBuilder';\n\n\tprivate shouldIgnore = false;\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: SingleStoreSession,\n\t\tprivate dialect: SingleStoreDialect,\n\t) {}\n\n\tignore(): this {\n\t\tthis.shouldIgnore = true;\n\t\treturn this;\n\t}\n\n\tvalues(value: SingleStoreInsertValue<TTable>): SingleStoreInsertBase<TTable, TQueryResult, TPreparedQueryHKT>;\n\tvalues(values: SingleStoreInsertValue<TTable>[]): SingleStoreInsertBase<TTable, TQueryResult, TPreparedQueryHKT>;\n\tvalues(\n\t\tvalues: SingleStoreInsertValue<TTable> | SingleStoreInsertValue<TTable>[],\n\t): SingleStoreInsertBase<TTable, TQueryResult, TPreparedQueryHKT> {\n\t\tvalues = Array.isArray(values) ? values : [values];\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('values() must be called with at least one value');\n\t\t}\n\t\tconst mappedValues = values.map((entry) => {\n\t\t\tconst result: Record<string, Param | SQL> = {};\n\t\t\tconst cols = this.table[Table.Symbol.Columns];\n\t\t\tfor (const colKey of Object.keys(entry)) {\n\t\t\t\tconst colValue = entry[colKey as keyof typeof entry];\n\t\t\t\tresult[colKey] = is(colValue, SQL) ? colValue : new Param(colValue, cols[colKey]);\n\t\t\t}\n\t\t\treturn result;\n\t\t});\n\n\t\treturn new SingleStoreInsertBase(this.table, mappedValues, this.shouldIgnore, this.session, this.dialect);\n\t}\n}\n\nexport type SingleStoreInsertWithout<\n\tT extends AnySingleStoreInsert,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T\n\t: Omit<\n\t\tSingleStoreInsertBase<\n\t\t\tT['_']['table'],\n\t\t\tT['_']['queryResult'],\n\t\t\tT['_']['preparedQueryHKT'],\n\t\t\tT['_']['returning'],\n\t\t\tTDynamic,\n\t\t\tT['_']['excludedMethods'] | '$returning'\n\t\t>,\n\t\tT['_']['excludedMethods'] | K\n\t>;\n\nexport type SingleStoreInsertDynamic<T extends AnySingleStoreInsert> = SingleStoreInsert<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['preparedQueryHKT'],\n\tT['_']['returning']\n>;\n\nexport type SingleStoreInsertPrepare<\n\tT extends AnySingleStoreInsert,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n> = PreparedQueryKind<\n\tT['_']['preparedQueryHKT'],\n\tSingleStorePreparedQueryConfig & {\n\t\texecute: TReturning extends undefined ? SingleStoreQueryResultKind<T['_']['queryResult'], never> : TReturning[];\n\t\titerator: never;\n\t},\n\ttrue\n>;\n\nexport type SingleStoreInsertOnDuplicateKeyUpdateConfig<T extends AnySingleStoreInsert> = {\n\tset: SingleStoreUpdateSetSource<T['_']['table']>;\n};\n\nexport type SingleStoreInsert<\n\tTTable extends SingleStoreTable = SingleStoreTable,\n\tTQueryResult extends SingleStoreQueryResultHKT = AnySingleStoreQueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase = PreparedQueryHKTBase,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n> = SingleStoreInsertBase<TTable, TQueryResult, TPreparedQueryHKT, TReturning, true, never>;\n\nexport type SingleStoreInsertReturning<\n\tT extends AnySingleStoreInsert,\n\tTDynamic extends boolean,\n> = SingleStoreInsertBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['preparedQueryHKT'],\n\tInferModelFromColumns<GetPrimarySerialOrDefaultKeys<T['_']['table']['_']['columns']>>,\n\tTDynamic,\n\tT['_']['excludedMethods'] | '$returning'\n>;\n\nexport type AnySingleStoreInsert = SingleStoreInsertBase<any, any, any, any, any, any>;\n\nexport interface SingleStoreInsertBase<\n\tTTable extends SingleStoreTable,\n\tTQueryResult extends SingleStoreQueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tQueryPromise<TReturning extends undefined ? SingleStoreQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<\n\t\tTReturning extends undefined ? SingleStoreQueryResultKind<TQueryResult, never> : TReturning[],\n\t\t'singlestore'\n\t>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'singlestore';\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly preparedQueryHKT: TPreparedQueryHKT;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly returning: TReturning;\n\t\treadonly result: TReturning extends undefined ? SingleStoreQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport type PrimaryKeyKeys<T extends Record<string, AnySingleStoreColumn>> = {\n\t[K in keyof T]: T[K]['_']['isPrimaryKey'] extends true ? T[K]['_']['isAutoincrement'] extends true ? K\n\t\t: T[K]['_']['hasRuntimeDefault'] extends true ? T[K]['_']['isPrimaryKey'] extends true ? K : never\n\t\t: never\n\t\t: T[K]['_']['hasRuntimeDefault'] extends true ? T[K]['_']['isPrimaryKey'] extends true ? K : never\n\t\t: never;\n}[keyof T];\n\nexport type GetPrimarySerialOrDefaultKeys<T extends Record<string, AnySingleStoreColumn>> = {\n\t[K in PrimaryKeyKeys<T>]: T[K];\n};\n\nexport class SingleStoreInsertBase<\n\tTTable extends SingleStoreTable,\n\tTQueryResult extends SingleStoreQueryResultHKT,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? SingleStoreQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tRunnableQuery<\n\t\t\tTReturning extends undefined ? SingleStoreQueryResultKind<TQueryResult, never> : TReturning[],\n\t\t\t'singlestore'\n\t\t>,\n\t\tSQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreInsert';\n\n\tdeclare protected $table: TTable;\n\n\tprivate config: SingleStoreInsertConfig<TTable>;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tvalues: SingleStoreInsertConfig['values'],\n\t\tignore: boolean,\n\t\tprivate session: SingleStoreSession,\n\t\tprivate dialect: SingleStoreDialect,\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, values, ignore };\n\t}\n\n\t/**\n\t * Adds an `on duplicate key update` clause to the query.\n\t *\n\t * Calling this method will update update the row if any unique index conflicts. MySQL will automatically determine the conflict target based on the primary key and unique indexes.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#on-duplicate-key-update}\n\t *\n\t * @param config The `set` clause\n\t *\n\t * @example\n\t * ```ts\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW'})\n\t *   .onDuplicateKeyUpdate({ set: { brand: 'Porsche' }});\n\t * ```\n\t *\n\t * While MySQL does not directly support doing nothing on conflict, you can perform a no-op by setting any column's value to itself and achieve the same effect:\n\t *\n\t * ```ts\n\t * import { sql } from 'drizzle-orm';\n\t *\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onDuplicateKeyUpdate({ set: { id: sql`id` } });\n\t * ```\n\t */\n\tonDuplicateKeyUpdate(\n\t\tconfig: SingleStoreInsertOnDuplicateKeyUpdateConfig<this>,\n\t): SingleStoreInsertWithout<this, TDynamic, 'onDuplicateKeyUpdate'> {\n\t\tconst setSql = this.dialect.buildUpdateSet(this.config.table, mapUpdateSet(this.config.table, config.set));\n\t\tthis.config.onConflict = sql`update ${setSql}`;\n\t\treturn this as any;\n\t}\n\n\t$returningId(): SingleStoreInsertWithout<\n\t\tSingleStoreInsertReturning<this, TDynamic>,\n\t\tTDynamic,\n\t\t'$returningId'\n\t> {\n\t\tconst returning: SelectedFieldsOrdered = [];\n\t\tfor (const [key, value] of Object.entries(this.config.table[Table.Symbol.Columns])) {\n\t\t\tif (value.primary) {\n\t\t\t\treturning.push({ field: value, path: [key] });\n\t\t\t}\n\t\t}\n\t\tthis.config.returning = orderSelectedFields<SingleStoreColumn>(this.config.table[Table.Symbol.Columns]);\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildInsertQuery(this.config).sql;\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\tprepare(): SingleStoreInsertPrepare<this, TReturning> {\n\t\tconst { sql, generatedIds } = this.dialect.buildInsertQuery(this.config);\n\t\treturn this.session.prepareQuery(\n\t\t\tthis.dialect.sqlToQuery(sql),\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tgeneratedIds,\n\t\t\tthis.config.returning,\n\t\t) as SingleStoreInsertPrepare<this, TReturning>;\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn this.prepare().execute(placeholderValues);\n\t};\n\n\tprivate createIterator = (): ReturnType<this['prepare']>['iterator'] => {\n\t\tconst self = this;\n\t\treturn async function*(placeholderValues) {\n\t\t\tyield* self.prepare().iterator(placeholderValues);\n\t\t};\n\t};\n\n\titerator = this.createIterator();\n\n\t$dynamic(): SingleStoreInsertDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "mappings": "AAAA,SAAS,YAAY,UAAU;AAC/B,SAAS,oBAAoB;AAc7B,SAAS,OAAO,KAAK,WAAW;AAEhC,SAAS,aAAa;AACtB,SAAS,cAAc,2BAA2B;AAqB3C,MAAM,yBAIX;AAAA,EAKD,YACS,OACA,SACA,SACP;AAHO;AACA;AACA;AAAA,EACN;AAAA,EARH,QAAiB,UAAU,IAAY;AAAA,EAE/B,eAAe;AAAA,EAQvB,SAAe;AACd,SAAK,eAAe;AACpB,WAAO;AAAA,EACR;AAAA,EAIA,OACC,QACiE;AACjE,aAAS,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AACjD,QAAI,OAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,iDAAiD;AAAA,IAClE;AACA,UAAM,eAAe,OAAO,IAAI,CAAC,UAAU;AAC1C,YAAM,SAAsC,CAAC;AAC7C,YAAM,OAAO,KAAK,MAAM,MAAM,OAAO,OAAO;AAC5C,iBAAW,UAAU,OAAO,KAAK,KAAK,GAAG;AACxC,cAAM,WAAW,MAAM,MAA4B;AACnD,eAAO,MAAM,IAAI,GAAG,UAAU,GAAG,IAAI,WAAW,IAAI,MAAM,UAAU,KAAK,MAAM,CAAC;AAAA,MACjF;AACA,aAAO;AAAA,IACR,CAAC;AAED,WAAO,IAAI,sBAAsB,KAAK,OAAO,cAAc,KAAK,cAAc,KAAK,SAAS,KAAK,OAAO;AAAA,EACzG;AACD;AAsGO,MAAM,8BAWH,aAOV;AAAA,EAOC,YACC,OACA,QACA,QACQ,SACA,SACP;AACD,UAAM;AAHE;AACA;AAGR,SAAK,SAAS,EAAE,OAAO,QAAQ,OAAO;AAAA,EACvC;AAAA,EAfA,QAA0B,UAAU,IAAY;AAAA,EAIxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuCR,qBACC,QACmE;AACnE,UAAM,SAAS,KAAK,QAAQ,eAAe,KAAK,OAAO,OAAO,aAAa,KAAK,OAAO,OAAO,OAAO,GAAG,CAAC;AACzG,SAAK,OAAO,aAAa,aAAa,MAAM;AAC5C,WAAO;AAAA,EACR;AAAA,EAEA,eAIE;AACD,UAAM,YAAmC,CAAC;AAC1C,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,KAAK,OAAO,MAAM,MAAM,OAAO,OAAO,CAAC,GAAG;AACnF,UAAI,MAAM,SAAS;AAClB,kBAAU,KAAK,EAAE,OAAO,OAAO,MAAM,CAAC,GAAG,EAAE,CAAC;AAAA,MAC7C;AAAA,IACD;AACA,SAAK,OAAO,YAAY,oBAAuC,KAAK,OAAO,MAAM,MAAM,OAAO,OAAO,CAAC;AACtG,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM,EAAE;AAAA,EACnD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA,EAEA,UAAsD;AACrD,UAAM,EAAE,KAAAA,MAAK,aAAa,IAAI,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AACvE,WAAO,KAAK,QAAQ;AAAA,MACnB,KAAK,QAAQ,WAAWA,IAAG;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK,OAAO;AAAA,IACb;AAAA,EACD;AAAA,EAES,UAAkD,CAAC,sBAAsB;AACjF,WAAO,KAAK,QAAQ,EAAE,QAAQ,iBAAiB;AAAA,EAChD;AAAA,EAEQ,iBAAiB,MAA+C;AACvE,UAAM,OAAO;AACb,WAAO,iBAAgB,mBAAmB;AACzC,aAAO,KAAK,QAAQ,EAAE,SAAS,iBAAiB;AAAA,IACjD;AAAA,EACD;AAAA,EAEA,WAAW,KAAK,eAAe;AAAA,EAE/B,WAA2C;AAC1C,WAAO;AAAA,EACR;AACD;", "names": ["sql"]}