{"version": 3, "sources": [], "sections": [{"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      users: {\n        Row: {\n          id: string;\n          email: string;\n          name: string;\n          created_at: string;\n        };\n        Insert: {\n          id: string;\n          email: string;\n          name: string;\n          created_at?: string;\n        };\n        Update: {\n          id?: string;\n          email?: string;\n          name?: string;\n          created_at?: string;\n        };\n      };\n      companies: {\n        Row: {\n          id: string;\n          name: string;\n          description: string | null;\n          user_id: string;\n          created_at: string;\n        };\n        Insert: {\n          id?: string;\n          name: string;\n          description?: string | null;\n          user_id: string;\n          created_at?: string;\n        };\n        Update: {\n          id?: string;\n          name?: string;\n          description?: string | null;\n          user_id?: string;\n          created_at?: string;\n        };\n      };\n      financial_years: {\n        Row: {\n          id: string;\n          name: string;\n          start_date: string;\n          end_date: string;\n          is_active: boolean;\n          company_id: string;\n          created_at: string;\n        };\n        Insert: {\n          id?: string;\n          name: string;\n          start_date: string;\n          end_date: string;\n          is_active?: boolean;\n          company_id: string;\n          created_at?: string;\n        };\n        Update: {\n          id?: string;\n          name?: string;\n          start_date?: string;\n          end_date?: string;\n          is_active?: boolean;\n          company_id?: string;\n          created_at?: string;\n        };\n      };\n    };\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,wEAAwC;AAC5D,MAAM,kBAAkB,qDAA6C;AAE9D,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { supabase } from '@/lib/supabase';\n\n/**\n * User interface representing the authenticated user data structure\n * This interface defines the shape of user data throughout the application\n */\ninterface User {\n  /** Unique identifier for the user */\n  id: string;\n  /** User's email address (used for authentication) */\n  email: string;\n  /** User's display name */\n  name: string;\n  /** Timestamp when the user account was created */\n  created_at: string;\n}\n\n/**\n * Authentication context interface defining all available auth operations\n * This provides type safety for all authentication-related functions\n */\ninterface AuthContextType {\n  /** Current authenticated user or null if not logged in */\n  user: User | null;\n  /** Loading state for authentication operations */\n  loading: boolean;\n  /**\n   * Sign in function\n   * @param email - User's email address\n   * @param password - User's password\n   * @returns Promise with optional error message\n   */\n  signIn: (email: string, password: string) => Promise<{ error?: string }>;\n  /**\n   * Sign up function for new user registration\n   * @param email - User's email address\n   * @param password - User's password\n   * @param name - User's display name\n   * @returns Promise with optional error message\n   */\n  signUp: (email: string, password: string, name: string) => Promise<{ error?: string }>;\n  /**\n   * Sign out function to end user session\n   * @returns Promise that resolves when sign out is complete\n   */\n  signOut: () => Promise<void>;\n}\n\n/**\n * Create the authentication context with undefined as default\n * This will be populated by the AuthProvider component\n */\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n/**\n * AuthProvider component that wraps the application and provides authentication state\n * This component manages user authentication state and provides auth methods to children\n *\n * @param children - React components that will have access to auth context\n */\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  // State to store the current authenticated user\n  const [user, setUser] = useState<User | null>(null);\n\n  // Loading state to show when auth operations are in progress\n  const [loading, setLoading] = useState(true);\n\n  /**\n   * Effect hook to initialize authentication state and listen for auth changes\n   * This runs once when the component mounts and sets up auth listeners\n   */\n  useEffect(() => {\n    /**\n     * Check if user is currently logged in by getting the session\n     * This function runs on app initialization to restore user state\n     */\n    const checkUser = async () => {\n      try {\n        // Get current session from Supabase\n        const { data: { session } } = await supabase.auth.getSession();\n\n        if (session?.user) {\n          // If session exists, fetch the user profile from our users table\n          const { data: profile } = await supabase\n            .from('users')\n            .select('*')\n            .eq('id', session.user.id)\n            .single();\n\n          // Set user state if profile was found\n          if (profile) {\n            setUser(profile);\n          }\n        }\n      } catch (error) {\n        // Log any errors during user check\n        console.error('Error checking user:', error);\n      } finally {\n        // Always set loading to false when check is complete\n        setLoading(false);\n      }\n    };\n\n    // Execute the user check\n    checkUser();\n\n    /**\n     * Set up listener for authentication state changes\n     * This will trigger whenever user signs in, signs out, or token refreshes\n     */\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (event === 'SIGNED_IN' && session?.user) {\n          // User signed in - fetch their profile\n          const { data: profile } = await supabase\n            .from('users')\n            .select('*')\n            .eq('id', session.user.id)\n            .single();\n\n          if (profile) {\n            setUser(profile);\n          }\n        } else if (event === 'SIGNED_OUT') {\n          // User signed out - clear user state\n          setUser(null);\n        }\n        // Update loading state\n        setLoading(false);\n      }\n    );\n\n    // Cleanup function to unsubscribe from auth changes when component unmounts\n    return () => subscription.unsubscribe();\n  }, []); // Empty dependency array means this effect runs once on mount\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      if (error) {\n        return { error: error.message };\n      }\n\n      return {};\n    } catch (error) {\n      return { error: 'An unexpected error occurred' };\n    }\n  };\n\n  const signUp = async (email: string, password: string, name: string) => {\n    try {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n      });\n\n      if (error) {\n        return { error: error.message };\n      }\n\n      if (data.user) {\n        // Create user profile\n        const { error: profileError } = await supabase\n          .from('users')\n          .insert([\n            {\n              id: data.user.id,\n              email: data.user.email,\n              name,\n            },\n          ]);\n\n        if (profileError) {\n          return { error: profileError.message };\n        }\n      }\n\n      return {};\n    } catch (error) {\n      return { error: 'An unexpected error occurred' };\n    }\n  };\n\n  const signOut = async () => {\n    await supabase.auth.signOut();\n    setUser(null);\n  };\n\n  const value = {\n    user,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAmDA;;;CAGC,GACD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAQxD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,gDAAgD;IAChD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE9C,6DAA6D;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC;;;GAGC,GACD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;;;KAGC,GACD,MAAM,YAAY;YAChB,IAAI;gBACF,oCAAoC;gBACpC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;gBAE5D,IAAI,SAAS,MAAM;oBACjB,iEAAiE;oBACjE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACrC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QAAQ,IAAI,CAAC,EAAE,EACxB,MAAM;oBAET,sCAAsC;oBACtC,IAAI,SAAS;wBACX,QAAQ;oBACV;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,mCAAmC;gBACnC,QAAQ,KAAK,CAAC,wBAAwB;YACxC,SAAU;gBACR,qDAAqD;gBACrD,WAAW;YACb;QACF;QAEA,yBAAyB;QACzB;QAEA;;;KAGC,GACD,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,IAAI,UAAU,eAAe,SAAS,MAAM;gBAC1C,uCAAuC;gBACvC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACrC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QAAQ,IAAI,CAAC,EAAE,EACxB,MAAM;gBAET,IAAI,SAAS;oBACX,QAAQ;gBACV;YACF,OAAO,IAAI,UAAU,cAAc;gBACjC,qCAAqC;gBACrC,QAAQ;YACV;YACA,uBAAuB;YACvB,WAAW;QACb;QAGF,4EAA4E;QAC5E,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE,GAAG,8DAA8D;IAEtE,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBACvD;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,OAAO;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAChC;YAEA,OAAO,CAAC;QACV,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAA+B;QACjD;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,OAAO;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAChC;YAEA,IAAI,KAAK,IAAI,EAAE;gBACb,sBAAsB;gBACtB,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC3C,IAAI,CAAC,SACL,MAAM,CAAC;oBACN;wBACE,IAAI,KAAK,IAAI,CAAC,EAAE;wBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;wBACtB;oBACF;iBACD;gBAEH,IAAI,cAAc;oBAChB,OAAO;wBAAE,OAAO,aAAa,OAAO;oBAAC;gBACvC;YACF;YAEA,OAAO,CAAC;QACV,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAA+B;QACjD;IACF;IAEA,MAAM,UAAU;QACd,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAC3B,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/src/contexts/AppContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { useAuth } from './AuthContext';\nimport { supabase } from '@/lib/supabase';\n\ninterface Company {\n  id: string;\n  name: string;\n  description?: string;\n  created_at: string;\n  user_id: string;\n}\n\ninterface FinancialYear {\n  id: string;\n  name: string;\n  start_date: string;\n  end_date: string;\n  is_active: boolean;\n  company_id: string;\n  created_at: string;\n}\n\ninterface AppContextType {\n  companies: Company[];\n  currentCompany: Company | null;\n  financialYears: FinancialYear[];\n  currentFinancialYear: FinancialYear | null;\n  loading: boolean;\n  setCurrentCompany: (company: Company | null) => void;\n  setCurrentFinancialYear: (year: FinancialYear | null) => void;\n  refreshCompanies: () => Promise<void>;\n  refreshFinancialYears: () => Promise<void>;\n  createCompany: (name: string, description?: string) => Promise<{ error?: string }>;\n  createFinancialYear: (name: string, startDate: string, endDate: string) => Promise<{ error?: string }>;\n}\n\nconst AppContext = createContext<AppContextType | undefined>(undefined);\n\nexport function AppProvider({ children }: { children: React.ReactNode }) {\n  const { user } = useAuth();\n  const [companies, setCompanies] = useState<Company[]>([]);\n  const [currentCompany, setCurrentCompany] = useState<Company | null>(null);\n  const [financialYears, setFinancialYears] = useState<FinancialYear[]>([]);\n  const [currentFinancialYear, setCurrentFinancialYear] = useState<FinancialYear | null>(null);\n  const [loading, setLoading] = useState(false);\n\n  const refreshCompanies = async () => {\n    if (!user) return;\n\n    setLoading(true);\n    try {\n      if (DEMO_MODE) {\n        // Use demo data\n        const userCompanies = DEMO_COMPANIES.filter(c => c.user_id === user.id);\n        setCompanies(userCompanies);\n\n        // Set first company as current if none selected\n        if (!currentCompany && userCompanies.length > 0) {\n          setCurrentCompany(userCompanies[0]);\n        }\n        setLoading(false);\n        return;\n      }\n\n      const { data, error } = await supabase\n        .from('companies')\n        .select('*')\n        .eq('user_id', user.id)\n        .order('created_at', { ascending: false });\n\n      if (error) {\n        console.error('Error fetching companies:', error);\n      } else {\n        setCompanies(data || []);\n\n        // Set first company as current if none selected\n        if (!currentCompany && data && data.length > 0) {\n          setCurrentCompany(data[0]);\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching companies:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const refreshFinancialYears = async () => {\n    if (!currentCompany) return;\n\n    setLoading(true);\n    try {\n      if (DEMO_MODE) {\n        // Use demo data\n        const companyYears = DEMO_FINANCIAL_YEARS.filter(y => y.company_id === currentCompany.id);\n        setFinancialYears(companyYears);\n\n        // Set active year as current or first year if none selected\n        if (companyYears.length > 0) {\n          const activeYear = companyYears.find(year => year.is_active);\n          if (activeYear && !currentFinancialYear) {\n            setCurrentFinancialYear(activeYear);\n          } else if (!currentFinancialYear) {\n            setCurrentFinancialYear(companyYears[0]);\n          }\n        }\n        setLoading(false);\n        return;\n      }\n\n      const { data, error } = await supabase\n        .from('financial_years')\n        .select('*')\n        .eq('company_id', currentCompany.id)\n        .order('start_date', { ascending: false });\n\n      if (error) {\n        console.error('Error fetching financial years:', error);\n      } else {\n        setFinancialYears(data || []);\n\n        // Set active year as current or first year if none selected\n        if (data && data.length > 0) {\n          const activeYear = data.find(year => year.is_active);\n          if (activeYear && !currentFinancialYear) {\n            setCurrentFinancialYear(activeYear);\n          } else if (!currentFinancialYear) {\n            setCurrentFinancialYear(data[0]);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching financial years:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const createCompany = async (name: string, description?: string) => {\n    if (!user) return { error: 'User not authenticated' };\n\n    try {\n      const { data, error } = await supabase\n        .from('companies')\n        .insert([\n          {\n            name,\n            description,\n            user_id: user.id,\n          },\n        ])\n        .select()\n        .single();\n\n      if (error) {\n        return { error: error.message };\n      }\n\n      await refreshCompanies();\n      return {};\n    } catch (error) {\n      return { error: 'An unexpected error occurred' };\n    }\n  };\n\n  const createFinancialYear = async (name: string, startDate: string, endDate: string) => {\n    if (!currentCompany) return { error: 'No company selected' };\n\n    try {\n      const { data, error } = await supabase\n        .from('financial_years')\n        .insert([\n          {\n            name,\n            start_date: startDate,\n            end_date: endDate,\n            company_id: currentCompany.id,\n            is_active: false,\n          },\n        ])\n        .select()\n        .single();\n\n      if (error) {\n        return { error: error.message };\n      }\n\n      await refreshFinancialYears();\n      return {};\n    } catch (error) {\n      return { error: 'An unexpected error occurred' };\n    }\n  };\n\n  useEffect(() => {\n    if (user) {\n      refreshCompanies();\n    } else {\n      setCompanies([]);\n      setCurrentCompany(null);\n      setFinancialYears([]);\n      setCurrentFinancialYear(null);\n    }\n  }, [user]);\n\n  useEffect(() => {\n    if (currentCompany) {\n      refreshFinancialYears();\n    } else {\n      setFinancialYears([]);\n      setCurrentFinancialYear(null);\n    }\n  }, [currentCompany]);\n\n  const value = {\n    companies,\n    currentCompany,\n    financialYears,\n    currentFinancialYear,\n    loading,\n    setCurrentCompany,\n    setCurrentFinancialYear,\n    refreshCompanies,\n    refreshFinancialYears,\n    createCompany,\n    createFinancialYear,\n  };\n\n  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;\n}\n\nexport function useApp() {\n  const context = useContext(AppContext);\n  if (context === undefined) {\n    throw new Error('useApp must be used within an AppProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAsCA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA8B;AAEtD,SAAS,YAAY,EAAE,QAAQ,EAAiC;IACrE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACxD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACvF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,mBAAmB;QACvB,IAAI,CAAC,MAAM;QAEX,WAAW;QACX,IAAI;YACF,IAAI,WAAW;gBACb,gBAAgB;gBAChB,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,KAAK,EAAE;gBACtE,aAAa;gBAEb,gDAAgD;gBAChD,IAAI,CAAC,kBAAkB,cAAc,MAAM,GAAG,GAAG;oBAC/C,kBAAkB,aAAa,CAAC,EAAE;gBACpC;gBACA,WAAW;gBACX;YACF;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,6BAA6B;YAC7C,OAAO;gBACL,aAAa,QAAQ,EAAE;gBAEvB,gDAAgD;gBAChD,IAAI,CAAC,kBAAkB,QAAQ,KAAK,MAAM,GAAG,GAAG;oBAC9C,kBAAkB,IAAI,CAAC,EAAE;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,gBAAgB;QAErB,WAAW;QACX,IAAI;YACF,IAAI,WAAW;gBACb,gBAAgB;gBAChB,MAAM,eAAe,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,eAAe,EAAE;gBACxF,kBAAkB;gBAElB,4DAA4D;gBAC5D,IAAI,aAAa,MAAM,GAAG,GAAG;oBAC3B,MAAM,aAAa,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS;oBAC3D,IAAI,cAAc,CAAC,sBAAsB;wBACvC,wBAAwB;oBAC1B,OAAO,IAAI,CAAC,sBAAsB;wBAChC,wBAAwB,YAAY,CAAC,EAAE;oBACzC;gBACF;gBACA,WAAW;gBACX;YACF;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,mBACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,eAAe,EAAE,EAClC,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,mCAAmC;YACnD,OAAO;gBACL,kBAAkB,QAAQ,EAAE;gBAE5B,4DAA4D;gBAC5D,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;oBAC3B,MAAM,aAAa,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS;oBACnD,IAAI,cAAc,CAAC,sBAAsB;wBACvC,wBAAwB;oBAC1B,OAAO,IAAI,CAAC,sBAAsB;wBAChC,wBAAwB,IAAI,CAAC,EAAE;oBACjC;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO,MAAc;QACzC,IAAI,CAAC,MAAM,OAAO;YAAE,OAAO;QAAyB;QAEpD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC;gBACN;oBACE;oBACA;oBACA,SAAS,KAAK,EAAE;gBAClB;aACD,EACA,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,OAAO;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAChC;YAEA,MAAM;YACN,OAAO,CAAC;QACV,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAA+B;QACjD;IACF;IAEA,MAAM,sBAAsB,OAAO,MAAc,WAAmB;QAClE,IAAI,CAAC,gBAAgB,OAAO;YAAE,OAAO;QAAsB;QAE3D,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,mBACL,MAAM,CAAC;gBACN;oBACE;oBACA,YAAY;oBACZ,UAAU;oBACV,YAAY,eAAe,EAAE;oBAC7B,WAAW;gBACb;aACD,EACA,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,OAAO;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAChC;YAEA,MAAM;YACN,OAAO,CAAC;QACV,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAA+B;QACjD;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF,OAAO;YACL,aAAa,EAAE;YACf,kBAAkB;YAClB,kBAAkB,EAAE;YACpB,wBAAwB;QAC1B;IACF,GAAG;QAAC;KAAK;IAET,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB;QACF,OAAO;YACL,kBAAkB,EAAE;YACpB,wBAAwB;QAC1B;IACF,GAAG;QAAC;KAAe;IAEnB,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,WAAW,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC7C;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 436, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/src/app/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { AppProvider } from '@/contexts/AppContext';\nimport { AuthProvider } from '@/contexts/AuthContext';\nimport \"./globals.css\";\n\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#1976d2',\n    },\n    secondary: {\n      main: '#dc004e',\n    },\n    background: {\n      default: '#f5f5f5',\n    },\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n  },\n  components: {\n    MuiDrawer: {\n      styleOverrides: {\n        paper: {\n          backgroundColor: '#2c3e50',\n          color: '#ecf0f1',\n        },\n      },\n    },\n  },\n});\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <html lang=\"en\">\n      <body>\n        <ThemeProvider theme={theme}>\n          <CssBaseline />\n          <AuthProvider>\n            <AppProvider>\n              {children}\n            </AppProvider>\n          </AuthProvider>\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AALA;;;;;;;AAQA,MAAM,QAAQ,CAAA,GAAA,2MAAA,CAAA,cAAW,AAAD,EAAE;IACxB,SAAS;QACP,MAAM;QACN,SAAS;YACP,MAAM;QACR;QACA,WAAW;YACT,MAAM;QACR;QACA,YAAY;YACV,SAAS;QACX;IACF;IACA,YAAY;QACV,YAAY;IACd;IACA,YAAY;QACV,WAAW;YACT,gBAAgB;gBACd,OAAO;oBACL,iBAAiB;oBACjB,OAAO;gBACT;YACF;QACF;IACF;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;sBACC,cAAA,8OAAC,+MAAA,CAAA,gBAAa;gBAAC,OAAO;;kCACpB,8OAAC,sKAAA,CAAA,UAAW;;;;;kCACZ,8OAAC,+HAAA,CAAA,eAAY;kCACX,cAAA,8OAAC,8HAAA,CAAA,cAAW;sCACT;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}]}