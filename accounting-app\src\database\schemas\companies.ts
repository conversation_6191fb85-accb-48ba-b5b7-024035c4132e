/**
 * Companies Schema Definition
 * 
 * This file defines the companies table schema using Drizzle ORM.
 * It handles company information and relationships with users.
 * 
 * Following DRY principle: Single schema per entity
 * Following YAGNI principle: Only essential company fields
 */

import { sqliteTable, text, index } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';
import { users } from './users';

/**
 * Companies table - Stores company information
 * Each user can have multiple companies
 * 
 * Following proper ORM patterns: Foreign key relationships
 */
export const companies = sqliteTable('companies', {
  /** Unique identifier for the company */
  id: text('id').primaryKey(),
  
  /** Company name */
  name: text('name').notNull(),
  
  /** Optional company description */
  description: text('description'),
  
  /** Reference to the user who owns this company */
  userId: text('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  
  /** Timestamp when the company was created */
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  
  /** Timestamp when the company was last updated */
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  /** Index on user_id for fast lookups of user's companies */
  userIdIdx: index('companies_user_id_idx').on(table.userId),
  
  /** Index on name for searching companies by name */
  nameIdx: index('companies_name_idx').on(table.name),
  
  /** Index on created_at for sorting and pagination */
  createdAtIdx: index('companies_created_at_idx').on(table.createdAt),
  
  /** Composite index for user-specific company name searches */
  userNameIdx: index('companies_user_name_idx').on(table.userId, table.name),
}));

/**
 * Type definitions for companies table operations
 * These provide type safety for insert, select, and update operations
 */

/** Type for inserting a new company */
export type InsertCompany = typeof companies.$inferInsert;

/** Type for selecting company data */
export type SelectCompany = typeof companies.$inferSelect;

/** Type for updating company data (excludes id and timestamps) */
export type UpdateCompany = Partial<Pick<SelectCompany, 'name' | 'description'>>;

/**
 * Company validation constraints
 * Following DRY principle: Centralized validation rules
 */
export const CompanyConstraints = {
  /** Minimum length for company names */
  NAME_MIN_LENGTH: 1,
  /** Maximum length for company names */
  NAME_MAX_LENGTH: 200,
  /** Maximum length for company descriptions */
  DESCRIPTION_MAX_LENGTH: 1000,
} as const;

/**
 * Company-related utility types
 * Following proper ORM patterns: Type-safe relationships
 */

/** Company with user information */
export type CompanyWithUser = SelectCompany & {
  user: {
    id: string;
    name: string;
    email: string;
  };
};

/** Company statistics type */
export type CompanyStats = {
  totalCompanies: number;
  activeCompanies: number;
  companiesCreatedThisMonth: number;
};
