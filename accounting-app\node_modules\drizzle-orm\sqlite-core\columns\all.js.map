{"version": 3, "sources": ["../../../src/sqlite-core/columns/all.ts"], "sourcesContent": ["import { blob } from './blob.ts';\nimport { customType } from './custom.ts';\nimport { integer } from './integer.ts';\nimport { numeric } from './numeric.ts';\nimport { real } from './real.ts';\nimport { text } from './text.ts';\n\nexport function getSQLiteColumnBuilders() {\n\treturn {\n\t\tblob,\n\t\tcustomType,\n\t\tinteger,\n\t\tnumeric,\n\t\treal,\n\t\ttext,\n\t};\n}\n\nexport type SQLiteColumnBuilders = ReturnType<typeof getSQLiteColumnBuilders>;\n"], "mappings": "AAAA,SAAS,YAAY;AACrB,SAAS,kBAAkB;AAC3B,SAAS,eAAe;AACxB,SAAS,eAAe;AACxB,SAAS,YAAY;AACrB,SAAS,YAAY;AAEd,SAAS,0BAA0B;AACzC,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;", "names": []}