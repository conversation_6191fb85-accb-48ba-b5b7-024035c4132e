{"version": 3, "sources": ["../../../src/pg-core/columns/smallint.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn } from './common.ts';\nimport { PgIntColumnBaseBuilder } from './int.common.ts';\n\nexport type PgSmallIntBuilderInitial<TName extends string> = PgSmallIntBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'PgSmallInt';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class PgSmallIntBuilder<T extends ColumnBuilderBaseConfig<'number', 'PgSmallInt'>>\n\textends PgIntColumnBaseBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgSmallIntBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'PgSmallInt');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgSmallInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgSmallInt<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgSmallInt<T extends ColumnBaseConfig<'number', 'PgSmallInt'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgSmallInt';\n\n\tgetSQLType(): string {\n\t\treturn 'smallint';\n\t}\n\n\toverride mapFromDriverValue = (value: number | string): number => {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number(value);\n\t\t}\n\t\treturn value;\n\t};\n}\n\nexport function smallint(): PgSmallIntBuilderInitial<''>;\nexport function smallint<TName extends string>(name: TName): PgSmallIntBuilderInitial<TName>;\nexport function smallint(name?: string) {\n\treturn new PgSmallIntBuilder(name ?? '');\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,gBAAgB;AACzB,SAAS,8BAA8B;AAWhC,MAAM,0BACJ,uBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,YAAY;AAAA,EACnC;AAAA;AAAA,EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI,WAA4C,OAAO,KAAK,MAA8C;AAAA,EAClH;AACD;AAEO,MAAM,mBAAuE,SAAY;AAAA,EAC/F,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,qBAAqB,CAAC,UAAmC;AACjE,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,KAAK;AAAA,IACpB;AACA,WAAO;AAAA,EACR;AACD;AAIO,SAAS,SAAS,MAAe;AACvC,SAAO,IAAI,kBAAkB,QAAQ,EAAE;AACxC;", "names": []}