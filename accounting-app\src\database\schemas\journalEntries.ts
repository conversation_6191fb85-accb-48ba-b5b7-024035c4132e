/**
 * Journal Entries Schema Definition
 * 
 * This file defines the journal entries and journal entry lines table schemas using Drizzle ORM.
 * It handles accounting transactions and their individual debit/credit lines.
 * 
 * Following DRY principle: Single schema per entity
 * Following YAGNI principle: Only essential journal entry fields
 */

import { sqliteTable, text, real, integer, index } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';
import { companies } from './companies';
import { financialYears } from './financialYears';
import { accounts } from './accounts';
import { users } from './users';

/**
 * Journal entry status enum for type safety
 * Following proper ORM patterns: Type-safe enums
 */
export const JournalEntryStatus = {
  DRAFT: 'DRAFT',
  POSTED: 'POSTED',
  REVERSED: 'REVERSED',
} as const;

export type JournalEntryStatusType = typeof JournalEntryStatus[keyof typeof JournalEntryStatus];

/**
 * Journal Entries table - Main transaction records
 * Each journal entry represents a complete accounting transaction
 * 
 * Following proper ORM patterns: Decimal fields for monetary amounts
 */
export const journalEntries = sqliteTable('journal_entries', {
  /** Unique identifier for the journal entry */
  id: text('id').primaryKey(),
  
  /** Sequential entry number for easy reference (e.g., "JE001") */
  entryNumber: text('entry_number').notNull(),
  
  /** Date of the transaction (ISO date string) */
  date: text('date').notNull(),
  
  /** Description of the transaction */
  description: text('description').notNull(),
  
  /** Optional reference number (e.g., invoice number, check number) */
  reference: text('reference'),
  
  /** Total debit amount (must equal total_credit) */
  totalDebit: real('total_debit').default(0).notNull(),
  
  /** Total credit amount (must equal total_debit) */
  totalCredit: real('total_credit').default(0).notNull(),
  
  /** Current status of the journal entry */
  status: text('status').default(JournalEntryStatus.DRAFT).notNull().$type<JournalEntryStatusType>(),
  
  /** Reference to the company this entry belongs to */
  companyId: text('company_id').notNull().references(() => companies.id, { onDelete: 'cascade' }),
  
  /** Reference to the financial year this entry belongs to */
  financialYearId: text('financial_year_id').notNull().references(() => financialYears.id, { onDelete: 'cascade' }),
  
  /** Reference to the user who created this entry */
  createdBy: text('created_by').notNull().references(() => users.id),
  
  /** Timestamp when the entry was created */
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  
  /** Timestamp when the entry was last updated */
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  /** Index on company_id for fast lookups of company's journal entries */
  companyIdIdx: index('journal_entries_company_id_idx').on(table.companyId),
  
  /** Index on financial_year_id for fast lookups of year's journal entries */
  financialYearIdIdx: index('journal_entries_financial_year_id_idx').on(table.financialYearId),
  
  /** Index on status for filtering by entry status */
  statusIdx: index('journal_entries_status_idx').on(table.status),
  
  /** Index on date for date-based queries and sorting */
  dateIdx: index('journal_entries_date_idx').on(table.date),
  
  /** Index on entry_number for fast lookups by entry number */
  entryNumberIdx: index('journal_entries_entry_number_idx').on(table.entryNumber),
  
  /** Index on created_by for user-specific queries */
  createdByIdx: index('journal_entries_created_by_idx').on(table.createdBy),
  
  /** Composite index for unique entry numbers within company/year */
  uniqueEntryNumberIdx: index('journal_entries_unique_entry_number_idx').on(table.companyId, table.financialYearId, table.entryNumber),
  
  /** Composite index for company and status queries */
  companyStatusIdx: index('journal_entries_company_status_idx').on(table.companyId, table.status),
  
  /** Composite index for date range queries within company */
  companyDateIdx: index('journal_entries_company_date_idx').on(table.companyId, table.date),
}));

/**
 * Journal Entry Lines table - Individual debit/credit lines
 * Each journal entry consists of multiple lines that must balance
 * 
 * Following proper ORM patterns: Proper line ordering and constraints
 */
export const journalEntryLines = sqliteTable('journal_entry_lines', {
  /** Unique identifier for the journal entry line */
  id: text('id').primaryKey(),
  
  /** Reference to the journal entry this line belongs to */
  journalEntryId: text('journal_entry_id').notNull().references(() => journalEntries.id, { onDelete: 'cascade' }),
  
  /** Reference to the account being debited or credited */
  accountId: text('account_id').notNull().references(() => accounts.id),
  
  /** Optional description for this specific line */
  description: text('description'),
  
  /** Debit amount (0 if this is a credit line) */
  debitAmount: real('debit_amount').default(0).notNull(),
  
  /** Credit amount (0 if this is a debit line) */
  creditAmount: real('credit_amount').default(0).notNull(),
  
  /** Line number for ordering within the journal entry */
  lineNumber: integer('line_number').notNull(),
  
  /** Timestamp when the line was created */
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  /** Index on journal_entry_id for fast lookups of entry's lines */
  journalEntryIdIdx: index('journal_entry_lines_journal_entry_id_idx').on(table.journalEntryId),
  
  /** Index on account_id for account-specific queries */
  accountIdIdx: index('journal_entry_lines_account_id_idx').on(table.accountId),
  
  /** Index on line_number for ordering lines within entries */
  lineNumberIdx: index('journal_entry_lines_line_number_idx').on(table.lineNumber),
  
  /** Composite index for unique line numbers within journal entry */
  uniqueLineNumberIdx: index('journal_entry_lines_unique_line_number_idx').on(table.journalEntryId, table.lineNumber),
  
  /** Composite index for journal entry and account queries */
  entryAccountIdx: index('journal_entry_lines_entry_account_idx').on(table.journalEntryId, table.accountId),
}));

/**
 * Type definitions for journal entries and lines table operations
 * These provide type safety for insert, select, and update operations
 */

/** Type for inserting a new journal entry */
export type InsertJournalEntry = typeof journalEntries.$inferInsert;

/** Type for selecting journal entry data */
export type SelectJournalEntry = typeof journalEntries.$inferSelect;

/** Type for updating journal entry data */
export type UpdateJournalEntry = Partial<Pick<SelectJournalEntry, 'entryNumber' | 'date' | 'description' | 'reference' | 'status'>>;

/** Type for inserting a new journal entry line */
export type InsertJournalEntryLine = typeof journalEntryLines.$inferInsert;

/** Type for selecting journal entry line data */
export type SelectJournalEntryLine = typeof journalEntryLines.$inferSelect;

/** Type for updating journal entry line data */
export type UpdateJournalEntryLine = Partial<Pick<SelectJournalEntryLine, 'accountId' | 'description' | 'debitAmount' | 'creditAmount' | 'lineNumber'>>;

/**
 * Journal entry validation constraints
 * Following DRY principle: Centralized validation rules
 */
export const JournalEntryConstraints = {
  /** Minimum length for entry numbers */
  ENTRY_NUMBER_MIN_LENGTH: 1,
  /** Maximum length for entry numbers */
  ENTRY_NUMBER_MAX_LENGTH: 50,
  /** Minimum length for descriptions */
  DESCRIPTION_MIN_LENGTH: 1,
  /** Maximum length for descriptions */
  DESCRIPTION_MAX_LENGTH: 500,
  /** Maximum length for references */
  REFERENCE_MAX_LENGTH: 100,
  /** Maximum length for line descriptions */
  LINE_DESCRIPTION_MAX_LENGTH: 200,
  /** Minimum number of lines required for a journal entry */
  MIN_LINES: 2,
  /** Maximum number of lines allowed for a journal entry */
  MAX_LINES: 100,
  /** Maximum monetary amount */
  MAX_AMOUNT: *********.99,
  /** Decimal places for monetary amounts */
  DECIMAL_PLACES: 2,
} as const;

/**
 * Journal entry utility types
 * Following proper ORM patterns: Type-safe relationships and utilities
 */

/** Journal entry with lines */
export type JournalEntryWithLines = SelectJournalEntry & {
  lines: (SelectJournalEntryLine & {
    account: {
      id: string;
      code: string;
      name: string;
    };
  })[];
};

/** Journal entry with complete relationships */
export type JournalEntryWithRelations = SelectJournalEntry & {
  lines: (SelectJournalEntryLine & {
    account: {
      id: string;
      code: string;
      name: string;
      accountType: {
        name: string;
        category: string;
      };
    };
  })[];
  company: {
    id: string;
    name: string;
  };
  financialYear: {
    id: string;
    name: string;
  };
  createdByUser: {
    id: string;
    name: string;
    email: string;
  };
};

/** Journal entry statistics */
export type JournalEntryStats = {
  totalEntries: number;
  entriesByStatus: Record<JournalEntryStatusType, number>;
  totalAmount: number;
  averageAmount: number;
  entriesThisMonth: number;
};

/**
 * Journal entry helper functions types
 * Following DRY principle: Reusable utility function types
 */

/** Type for journal entry creation input */
export type CreateJournalEntryInput = {
  entryNumber: string;
  date: string;
  description: string;
  reference?: string;
  companyId: string;
  financialYearId: string;
  createdBy: string;
  lines: CreateJournalEntryLineInput[];
};

/** Type for journal entry line creation input */
export type CreateJournalEntryLineInput = {
  accountId: string;
  description?: string;
  debitAmount?: number;
  creditAmount?: number;
  lineNumber: number;
};

/** Type for journal entry update input */
export type UpdateJournalEntryInput = {
  entryNumber?: string;
  date?: string;
  description?: string;
  reference?: string;
  status?: JournalEntryStatusType;
  lines?: UpdateJournalEntryLineInput[];
};

/** Type for journal entry line update input */
export type UpdateJournalEntryLineInput = {
  id?: string;
  accountId?: string;
  description?: string;
  debitAmount?: number;
  creditAmount?: number;
  lineNumber?: number;
};

/** Type for journal entry filter options */
export type JournalEntryFilters = {
  companyId?: string;
  financialYearId?: string;
  status?: JournalEntryStatusType;
  dateFrom?: string;
  dateTo?: string;
  accountId?: string;
  createdBy?: string;
  search?: string;
  entryNumbers?: string[];
};

/** Type for journal entry balance validation */
export type JournalEntryBalance = {
  totalDebit: number;
  totalCredit: number;
  isBalanced: boolean;
  difference: number;
};
