{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      users: {\n        Row: {\n          id: string;\n          email: string;\n          name: string;\n          created_at: string;\n        };\n        Insert: {\n          id: string;\n          email: string;\n          name: string;\n          created_at?: string;\n        };\n        Update: {\n          id?: string;\n          email?: string;\n          name?: string;\n          created_at?: string;\n        };\n      };\n      companies: {\n        Row: {\n          id: string;\n          name: string;\n          description: string | null;\n          user_id: string;\n          created_at: string;\n        };\n        Insert: {\n          id?: string;\n          name: string;\n          description?: string | null;\n          user_id: string;\n          created_at?: string;\n        };\n        Update: {\n          id?: string;\n          name?: string;\n          description?: string | null;\n          user_id?: string;\n          created_at?: string;\n        };\n      };\n      financial_years: {\n        Row: {\n          id: string;\n          name: string;\n          start_date: string;\n          end_date: string;\n          is_active: boolean;\n          company_id: string;\n          created_at: string;\n        };\n        Insert: {\n          id?: string;\n          name: string;\n          start_date: string;\n          end_date: string;\n          is_active?: boolean;\n          company_id: string;\n          created_at?: string;\n        };\n        Update: {\n          id?: string;\n          name?: string;\n          start_date?: string;\n          end_date?: string;\n          is_active?: boolean;\n          company_id?: string;\n          created_at?: string;\n        };\n      };\n    };\n  };\n}\n"], "names": [], "mappings": ";;;AAEoB;AAFpB;;AAEA,MAAM,cAAc,wEAAwC;AAC5D,MAAM,kBAAkB,qDAA6C;AAE9D,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { supabase } from '@/lib/supabase';\n\n// Demo mode for testing without Supabase setup\nconst DEMO_MODE = true;\nconst DEMO_USERS = [\n  {\n    id: 'demo-user-1',\n    email: '<EMAIL>',\n    name: '<PERSON> Admin',\n    created_at: new Date().toISOString(),\n  }\n];\n\ninterface User {\n  id: string;\n  email: string;\n  name: string;\n  created_at: string;\n}\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  signIn: (email: string, password: string) => Promise<{ error?: string }>;\n  signUp: (email: string, password: string, name: string) => Promise<{ error?: string }>;\n  signOut: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Check if user is logged in\n    const checkUser = async () => {\n      try {\n        if (DEMO_MODE) {\n          // Check localStorage for demo user\n          const demoUser = localStorage.getItem('demo-user');\n          if (demoUser) {\n            setUser(JSON.parse(demoUser));\n          }\n          setLoading(false);\n          return;\n        }\n\n        const { data: { session } } = await supabase.auth.getSession();\n        if (session?.user) {\n          // Fetch user profile\n          const { data: profile } = await supabase\n            .from('users')\n            .select('*')\n            .eq('id', session.user.id)\n            .single();\n\n          if (profile) {\n            setUser(profile);\n          }\n        }\n      } catch (error) {\n        console.error('Error checking user:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkUser();\n\n    if (!DEMO_MODE) {\n      // Listen for auth changes\n      const { data: { subscription } } = supabase.auth.onAuthStateChange(\n        async (event, session) => {\n          if (event === 'SIGNED_IN' && session?.user) {\n            const { data: profile } = await supabase\n              .from('users')\n              .select('*')\n              .eq('id', session.user.id)\n              .single();\n\n            if (profile) {\n              setUser(profile);\n            }\n          } else if (event === 'SIGNED_OUT') {\n            setUser(null);\n          }\n          setLoading(false);\n        }\n      );\n\n      return () => subscription.unsubscribe();\n    }\n  }, []);\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      if (DEMO_MODE) {\n        // Demo authentication\n        const demoUser = DEMO_USERS.find(u => u.email === email);\n        if (demoUser && password === '123456') {\n          localStorage.setItem('demo-user', JSON.stringify(demoUser));\n          setUser(demoUser);\n          return {};\n        } else {\n          return { error: 'Invalid email or password' };\n        }\n      }\n\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      if (error) {\n        return { error: error.message };\n      }\n\n      return {};\n    } catch (error) {\n      return { error: 'An unexpected error occurred' };\n    }\n  };\n\n  const signUp = async (email: string, password: string, name: string) => {\n    try {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n      });\n\n      if (error) {\n        return { error: error.message };\n      }\n\n      if (data.user) {\n        // Create user profile\n        const { error: profileError } = await supabase\n          .from('users')\n          .insert([\n            {\n              id: data.user.id,\n              email: data.user.email,\n              name,\n            },\n          ]);\n\n        if (profileError) {\n          return { error: profileError.message };\n        }\n      }\n\n      return {};\n    } catch (error) {\n      return { error: 'An unexpected error occurred' };\n    }\n  };\n\n  const signOut = async () => {\n    if (DEMO_MODE) {\n      localStorage.removeItem('demo-user');\n      setUser(null);\n      return;\n    }\n\n    await supabase.auth.signOut();\n    setUser(null);\n  };\n\n  const value = {\n    user,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAKA,+CAA+C;AAC/C,MAAM,YAAY;AAClB,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,YAAY,IAAI,OAAO,WAAW;IACpC;CACD;AAiBD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,6BAA6B;YAC7B,MAAM;oDAAY;oBAChB,IAAI;wBACF,wCAAe;4BACb,mCAAmC;4BACnC,MAAM,WAAW,aAAa,OAAO,CAAC;4BACtC,IAAI,UAAU;gCACZ,QAAQ,KAAK,KAAK,CAAC;4BACrB;4BACA,WAAW;4BACX;wBACF;;wBAEA,MAAgB;oBAalB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,wBAAwB;oBACxC,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;YAEA,uCAAgB;;YAsBhB;QACF;iCAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,wCAAe;gBACb,sBAAsB;gBACtB,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;gBAClD,IAAI,YAAY,aAAa,UAAU;oBACrC,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;oBACjD,QAAQ;oBACR,OAAO,CAAC;gBACV,OAAO;oBACL,OAAO;wBAAE,OAAO;oBAA4B;gBAC9C;YACF;;YAEA,MAAQ;QAUV,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAA+B;QACjD;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,OAAO;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAChC;YAEA,IAAI,KAAK,IAAI,EAAE;gBACb,sBAAsB;gBACtB,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC3C,IAAI,CAAC,SACL,MAAM,CAAC;oBACN;wBACE,IAAI,KAAK,IAAI,CAAC,EAAE;wBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;wBACtB;oBACF;iBACD;gBAEH,IAAI,cAAc;oBAChB,OAAO;wBAAE,OAAO,aAAa,OAAO;oBAAC;gBACvC;YACF;YAEA,OAAO,CAAC;QACV,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAA+B;QACjD;IACF;IAEA,MAAM,UAAU;QACd,wCAAe;YACb,aAAa,UAAU,CAAC;YACxB,QAAQ;YACR;QACF;;IAIF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GApJgB;KAAA;AAsJT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/src/contexts/AppContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { useAuth } from './AuthContext';\nimport { supabase } from '@/lib/supabase';\n\n// Demo mode for testing without Supabase setup\nconst DEMO_MODE = true;\nconst DEMO_COMPANIES = [\n  {\n    id: 'demo-company-1',\n    name: 'ABC Corporation',\n    description: 'A sample technology company',\n    user_id: 'demo-user-1',\n    created_at: new Date().toISOString(),\n  },\n  {\n    id: 'demo-company-2',\n    name: 'XYZ Trading Ltd',\n    description: 'Import/Export business',\n    user_id: 'demo-user-1',\n    created_at: new Date().toISOString(),\n  }\n];\n\nconst DEMO_FINANCIAL_YEARS = [\n  {\n    id: 'demo-year-1',\n    name: 'FY 2024-25',\n    start_date: '2024-04-01',\n    end_date: '2025-03-31',\n    is_active: true,\n    company_id: 'demo-company-1',\n    created_at: new Date().toISOString(),\n  },\n  {\n    id: 'demo-year-2',\n    name: 'FY 2023-24',\n    start_date: '2023-04-01',\n    end_date: '2024-03-31',\n    is_active: false,\n    company_id: 'demo-company-1',\n    created_at: new Date().toISOString(),\n  },\n  {\n    id: 'demo-year-3',\n    name: 'FY 2024-25',\n    start_date: '2024-04-01',\n    end_date: '2025-03-31',\n    is_active: true,\n    company_id: 'demo-company-2',\n    created_at: new Date().toISOString(),\n  }\n];\n\ninterface Company {\n  id: string;\n  name: string;\n  description?: string;\n  created_at: string;\n  user_id: string;\n}\n\ninterface FinancialYear {\n  id: string;\n  name: string;\n  start_date: string;\n  end_date: string;\n  is_active: boolean;\n  company_id: string;\n  created_at: string;\n}\n\ninterface AppContextType {\n  companies: Company[];\n  currentCompany: Company | null;\n  financialYears: FinancialYear[];\n  currentFinancialYear: FinancialYear | null;\n  loading: boolean;\n  setCurrentCompany: (company: Company | null) => void;\n  setCurrentFinancialYear: (year: FinancialYear | null) => void;\n  refreshCompanies: () => Promise<void>;\n  refreshFinancialYears: () => Promise<void>;\n  createCompany: (name: string, description?: string) => Promise<{ error?: string }>;\n  createFinancialYear: (name: string, startDate: string, endDate: string) => Promise<{ error?: string }>;\n}\n\nconst AppContext = createContext<AppContextType | undefined>(undefined);\n\nexport function AppProvider({ children }: { children: React.ReactNode }) {\n  const { user } = useAuth();\n  const [companies, setCompanies] = useState<Company[]>([]);\n  const [currentCompany, setCurrentCompany] = useState<Company | null>(null);\n  const [financialYears, setFinancialYears] = useState<FinancialYear[]>([]);\n  const [currentFinancialYear, setCurrentFinancialYear] = useState<FinancialYear | null>(null);\n  const [loading, setLoading] = useState(false);\n\n  const refreshCompanies = async () => {\n    if (!user) return;\n\n    setLoading(true);\n    try {\n      if (DEMO_MODE) {\n        // Use demo data\n        const userCompanies = DEMO_COMPANIES.filter(c => c.user_id === user.id);\n        setCompanies(userCompanies);\n\n        // Set first company as current if none selected\n        if (!currentCompany && userCompanies.length > 0) {\n          setCurrentCompany(userCompanies[0]);\n        }\n        setLoading(false);\n        return;\n      }\n\n      const { data, error } = await supabase\n        .from('companies')\n        .select('*')\n        .eq('user_id', user.id)\n        .order('created_at', { ascending: false });\n\n      if (error) {\n        console.error('Error fetching companies:', error);\n      } else {\n        setCompanies(data || []);\n\n        // Set first company as current if none selected\n        if (!currentCompany && data && data.length > 0) {\n          setCurrentCompany(data[0]);\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching companies:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const refreshFinancialYears = async () => {\n    if (!currentCompany) return;\n\n    setLoading(true);\n    try {\n      if (DEMO_MODE) {\n        // Use demo data\n        const companyYears = DEMO_FINANCIAL_YEARS.filter(y => y.company_id === currentCompany.id);\n        setFinancialYears(companyYears);\n\n        // Set active year as current or first year if none selected\n        if (companyYears.length > 0) {\n          const activeYear = companyYears.find(year => year.is_active);\n          if (activeYear && !currentFinancialYear) {\n            setCurrentFinancialYear(activeYear);\n          } else if (!currentFinancialYear) {\n            setCurrentFinancialYear(companyYears[0]);\n          }\n        }\n        setLoading(false);\n        return;\n      }\n\n      const { data, error } = await supabase\n        .from('financial_years')\n        .select('*')\n        .eq('company_id', currentCompany.id)\n        .order('start_date', { ascending: false });\n\n      if (error) {\n        console.error('Error fetching financial years:', error);\n      } else {\n        setFinancialYears(data || []);\n\n        // Set active year as current or first year if none selected\n        if (data && data.length > 0) {\n          const activeYear = data.find(year => year.is_active);\n          if (activeYear && !currentFinancialYear) {\n            setCurrentFinancialYear(activeYear);\n          } else if (!currentFinancialYear) {\n            setCurrentFinancialYear(data[0]);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching financial years:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const createCompany = async (name: string, description?: string) => {\n    if (!user) return { error: 'User not authenticated' };\n\n    try {\n      const { data, error } = await supabase\n        .from('companies')\n        .insert([\n          {\n            name,\n            description,\n            user_id: user.id,\n          },\n        ])\n        .select()\n        .single();\n\n      if (error) {\n        return { error: error.message };\n      }\n\n      await refreshCompanies();\n      return {};\n    } catch (error) {\n      return { error: 'An unexpected error occurred' };\n    }\n  };\n\n  const createFinancialYear = async (name: string, startDate: string, endDate: string) => {\n    if (!currentCompany) return { error: 'No company selected' };\n\n    try {\n      const { data, error } = await supabase\n        .from('financial_years')\n        .insert([\n          {\n            name,\n            start_date: startDate,\n            end_date: endDate,\n            company_id: currentCompany.id,\n            is_active: false,\n          },\n        ])\n        .select()\n        .single();\n\n      if (error) {\n        return { error: error.message };\n      }\n\n      await refreshFinancialYears();\n      return {};\n    } catch (error) {\n      return { error: 'An unexpected error occurred' };\n    }\n  };\n\n  useEffect(() => {\n    if (user) {\n      refreshCompanies();\n    } else {\n      setCompanies([]);\n      setCurrentCompany(null);\n      setFinancialYears([]);\n      setCurrentFinancialYear(null);\n    }\n  }, [user]);\n\n  useEffect(() => {\n    if (currentCompany) {\n      refreshFinancialYears();\n    } else {\n      setFinancialYears([]);\n      setCurrentFinancialYear(null);\n    }\n  }, [currentCompany]);\n\n  const value = {\n    companies,\n    currentCompany,\n    financialYears,\n    currentFinancialYear,\n    loading,\n    setCurrentCompany,\n    setCurrentFinancialYear,\n    refreshCompanies,\n    refreshFinancialYears,\n    createCompany,\n    createFinancialYear,\n  };\n\n  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;\n}\n\nexport function useApp() {\n  const context = useContext(AppContext);\n  if (context === undefined) {\n    throw new Error('useApp must be used within an AppProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,+CAA+C;AAC/C,MAAM,YAAY;AAClB,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,SAAS;QACT,YAAY,IAAI,OAAO,WAAW;IACpC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,SAAS;QACT,YAAY,IAAI,OAAO,WAAW;IACpC;CACD;AAED,MAAM,uBAAuB;IAC3B;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;QACV,WAAW;QACX,YAAY;QACZ,YAAY,IAAI,OAAO,WAAW;IACpC;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;QACV,WAAW;QACX,YAAY;QACZ,YAAY,IAAI,OAAO,WAAW;IACpC;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;QACV,WAAW;QACX,YAAY;QACZ,YAAY,IAAI,OAAO,WAAW;IACpC;CACD;AAkCD,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA8B;AAEtD,SAAS,YAAY,EAAE,QAAQ,EAAiC;;IACrE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACxD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACvF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,mBAAmB;QACvB,IAAI,CAAC,MAAM;QAEX,WAAW;QACX,IAAI;YACF,wCAAe;gBACb,gBAAgB;gBAChB,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,KAAK,EAAE;gBACtE,aAAa;gBAEb,gDAAgD;gBAChD,IAAI,CAAC,kBAAkB,cAAc,MAAM,GAAG,GAAG;oBAC/C,kBAAkB,aAAa,CAAC,EAAE;gBACpC;gBACA,WAAW;gBACX;YACF;;YAEA,MAAQ,kBAAM;QAgBhB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,gBAAgB;QAErB,WAAW;QACX,IAAI;YACF,wCAAe;gBACb,gBAAgB;gBAChB,MAAM,eAAe,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,eAAe,EAAE;gBACxF,kBAAkB;gBAElB,4DAA4D;gBAC5D,IAAI,aAAa,MAAM,GAAG,GAAG;oBAC3B,MAAM,aAAa,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,SAAS;oBAC3D,IAAI,cAAc,CAAC,sBAAsB;wBACvC,wBAAwB;oBAC1B,OAAO,IAAI,CAAC,sBAAsB;wBAChC,wBAAwB,YAAY,CAAC,EAAE;oBACzC;gBACF;gBACA,WAAW;gBACX;YACF;;YAEA,MAAQ,kBAAM;QAqBhB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO,MAAc;QACzC,IAAI,CAAC,MAAM,OAAO;YAAE,OAAO;QAAyB;QAEpD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC;gBACN;oBACE;oBACA;oBACA,SAAS,KAAK,EAAE;gBAClB;aACD,EACA,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,OAAO;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAChC;YAEA,MAAM;YACN,OAAO,CAAC;QACV,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAA+B;QACjD;IACF;IAEA,MAAM,sBAAsB,OAAO,MAAc,WAAmB;QAClE,IAAI,CAAC,gBAAgB,OAAO;YAAE,OAAO;QAAsB;QAE3D,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,mBACL,MAAM,CAAC;gBACN;oBACE;oBACA,YAAY;oBACZ,UAAU;oBACV,YAAY,eAAe,EAAE;oBAC7B,WAAW;gBACb;aACD,EACA,MAAM,GACN,MAAM;YAET,IAAI,OAAO;gBACT,OAAO;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAChC;YAEA,MAAM;YACN,OAAO,CAAC;QACV,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;YAA+B;QACjD;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,MAAM;gBACR;YACF,OAAO;gBACL,aAAa,EAAE;gBACf,kBAAkB;gBAClB,kBAAkB,EAAE;gBACpB,wBAAwB;YAC1B;QACF;gCAAG;QAAC;KAAK;IAET,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,gBAAgB;gBAClB;YACF,OAAO;gBACL,kBAAkB,EAAE;gBACpB,wBAAwB;YAC1B;QACF;gCAAG;QAAC;KAAe;IAEnB,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,WAAW,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC7C;GA/LgB;;QACG,kIAAA,CAAA,UAAO;;;KADV;AAiMT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/src/app/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { AppProvider } from '@/contexts/AppContext';\nimport { AuthProvider } from '@/contexts/AuthContext';\nimport \"./globals.css\";\n\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#1976d2',\n    },\n    secondary: {\n      main: '#dc004e',\n    },\n    background: {\n      default: '#f5f5f5',\n    },\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n  },\n  components: {\n    MuiDrawer: {\n      styleOverrides: {\n        paper: {\n          backgroundColor: '#2c3e50',\n          color: '#ecf0f1',\n        },\n      },\n    },\n  },\n});\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <html lang=\"en\">\n      <body>\n        <ThemeProvider theme={theme}>\n          <CssBaseline />\n          <AuthProvider>\n            <AppProvider>\n              {children}\n            </AppProvider>\n          </AuthProvider>\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AALA;;;;;;;AAQA,MAAM,QAAQ,CAAA,GAAA,8MAAA,CAAA,cAAW,AAAD,EAAE;IACxB,SAAS;QACP,MAAM;QACN,SAAS;YACP,MAAM;QACR;QACA,WAAW;YACT,MAAM;QACR;QACA,YAAY;YACV,SAAS;QACX;IACF;IACA,YAAY;QACV,YAAY;IACd;IACA,YAAY;QACV,WAAW;YACT,gBAAgB;gBACd,OAAO;oBACL,iBAAiB;oBACjB,OAAO;gBACT;YACF;QACF;IACF;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,6LAAC;QAAK,MAAK;kBACT,cAAA,6LAAC;sBACC,cAAA,6LAAC,kNAAA,CAAA,gBAAa;gBAAC,OAAO;;kCACpB,6LAAC,yKAAA,CAAA,UAAW;;;;;kCACZ,6LAAC,kIAAA,CAAA,eAAY;kCACX,cAAA,6LAAC,iIAAA,CAAA,cAAW;sCACT;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;KAnBwB", "debugId": null}}]}