{"version": 3, "sources": ["../../src/singlestore-proxy/driver.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { SingleStoreDatabase } from '~/singlestore-core/db.ts';\nimport { SingleStoreDialect } from '~/singlestore-core/dialect.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport {\n\ttype SingleStoreRemotePreparedQueryHKT,\n\ttype SingleStoreRemoteQueryResultHKT,\n\tSingleStoreRemoteSession,\n} from './session.ts';\n\nexport class SingleStoreRemoteDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends SingleStoreDatabase<SingleStoreRemoteQueryResultHKT, SingleStoreRemotePreparedQueryHKT, TSchema> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreRemoteDatabase';\n}\n\nexport type RemoteCallback = (\n\tsql: string,\n\tparams: any[],\n\tmethod: 'all' | 'execute',\n) => Promise<{ rows: any[]; insertId?: number; affectedRows?: number }>;\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tcallback: RemoteCallback,\n\tconfig: DrizzleConfig<TSchema> = {},\n): SingleStoreRemoteDatabase<TSchema> {\n\tconst dialect = new SingleStoreDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new SingleStoreRemoteSession(callback, dialect, schema, { logger });\n\treturn new SingleStoreRemoteDatabase(dialect, session, schema as any) as SingleStoreRemoteDatabase<\n\t\tTSchema\n\t>;\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B;AAAA,EACC;AAAA,EACA;AAAA,OAGM;AACP,SAAS,2BAA2B;AACpC,SAAS,0BAA0B;AAEnC;AAAA,EAGC;AAAA,OACM;AAEA,MAAM,kCAEH,oBAAiG;AAAA,EAC1G,QAA0B,UAAU,IAAY;AACjD;AAQO,SAAS,QACf,UACA,SAAiC,CAAC,GACG;AACrC,QAAM,UAAU,IAAI,mBAAmB,EAAE,QAAQ,OAAO,OAAO,CAAC;AAChE,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,yBAAyB,UAAU,SAAS,QAAQ,EAAE,OAAO,CAAC;AAClF,SAAO,IAAI,0BAA0B,SAAS,SAAS,MAAa;AAGrE;", "names": []}