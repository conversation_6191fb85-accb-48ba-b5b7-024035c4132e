/**
 * useApi Hook
 * 
 * A comprehensive custom React hook for handling API requests with loading states,
 * error handling, and automatic retries. This hook provides a consistent interface
 * for all API operations throughout the application.
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { ApiResponse, ErrorResponse } from '@/types';
import { API_CONFIG, ERROR_MESSAGES } from '@/constants';

/**
 * Configuration options for API requests
 */
interface ApiOptions {
  /** Whether to show loading state (default: true) */
  showLoading?: boolean;
  /** Number of retry attempts on failure (default: 0) */
  retries?: number;
  /** Delay between retries in milliseconds (default: 1000) */
  retryDelay?: number;
  /** Whether to automatically retry on network errors (default: true) */
  retryOnNetworkError?: boolean;
  /** Custom headers to include in the request */
  headers?: Record<string, string>;
  /** Request timeout in milliseconds (default: from API_CONFIG) */
  timeout?: number;
}

/**
 * State interface for the API hook
 */
interface ApiState<T> {
  /** The response data (null if no successful response yet) */
  data: T | null;
  /** Loading state indicator */
  loading: boolean;
  /** Error message (null if no error) */
  error: string | null;
  /** Whether the request was successful */
  success: boolean;
}

/**
 * Custom hook for handling API requests with comprehensive error handling and loading states
 * 
 * @template T - The expected type of the API response data
 * @param options - Configuration options for the API requests
 * @returns Object containing state and request functions
 * 
 * @example
 * const { data, loading, error, execute, reset } = useApi<User[]>();
 * 
 * // Execute a GET request
 * const fetchUsers = () => execute('/api/users');
 * 
 * // Execute a POST request
 * const createUser = (userData: CreateUserInput) => 
 *   execute('/api/users', { method: 'POST', body: JSON.stringify(userData) });
 */
export function useApi<T = any>(options: ApiOptions = {}) {
  // Destructure options with defaults
  const {
    showLoading = true,
    retries = 0,
    retryDelay = 1000,
    retryOnNetworkError = true,
    headers = {},
    timeout = API_CONFIG.TIMEOUT,
  } = options;

  // State management for API requests
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
    success: false,
  });

  // Ref to track if component is mounted (prevents state updates after unmount)
  const isMountedRef = useRef(true);

  // Ref to store the current AbortController for request cancellation
  const abortControllerRef = useRef<AbortController | null>(null);

  /**
   * Helper function to safely update state only if component is still mounted
   */
  const safeSetState = useCallback((newState: Partial<ApiState<T>>) => {
    if (isMountedRef.current) {
      setState(prevState => ({ ...prevState, ...newState }));
    }
  }, []);

  /**
   * Helper function to perform the actual fetch request with timeout and abort support
   */
  const performFetch = useCallback(
    async (url: string, fetchOptions: RequestInit = {}): Promise<Response> => {
      // Create new AbortController for this request
      const controller = new AbortController();
      abortControllerRef.current = controller;

      // Set up timeout
      const timeoutId = setTimeout(() => {
        controller.abort();
      }, timeout);

      try {
        // Merge default headers with custom headers
        const defaultHeaders = {
          'Content-Type': 'application/json',
          ...headers,
        };

        // Perform the fetch request
        const response = await fetch(url, {
          ...fetchOptions,
          headers: {
            ...defaultHeaders,
            ...fetchOptions.headers,
          },
          signal: controller.signal,
        });

        // Clear timeout since request completed
        clearTimeout(timeoutId);

        return response;
      } catch (error) {
        // Clear timeout
        clearTimeout(timeoutId);
        throw error;
      }
    },
    [headers, timeout]
  );

  /**
   * Helper function to handle retry logic
   */
  const executeWithRetry = useCallback(
    async (url: string, fetchOptions: RequestInit = {}, attemptNumber: number = 0): Promise<T> => {
      try {
        // Perform the fetch request
        const response = await performFetch(url, fetchOptions);

        // Check if response is ok
        if (!response.ok) {
          // Try to parse error response
          let errorMessage = ERROR_MESSAGES.GENERIC;
          try {
            const errorData: ErrorResponse = await response.json();
            errorMessage = errorData.error || errorMessage;
          } catch {
            // If parsing fails, use status text
            errorMessage = response.statusText || errorMessage;
          }

          throw new Error(errorMessage);
        }

        // Parse successful response
        const responseData: ApiResponse<T> = await response.json();
        
        if (responseData.success && responseData.data !== undefined) {
          return responseData.data;
        } else {
          throw new Error(responseData.error || ERROR_MESSAGES.GENERIC);
        }
      } catch (error) {
        // Check if we should retry
        const isNetworkError = error instanceof TypeError || 
                              (error as Error).name === 'AbortError' ||
                              (error as Error).message.includes('fetch');
        
        const shouldRetry = attemptNumber < retries && 
                           (retryOnNetworkError ? isNetworkError : false);

        if (shouldRetry) {
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          
          // Recursive retry
          return executeWithRetry(url, fetchOptions, attemptNumber + 1);
        }

        // No more retries, throw the error
        if (error instanceof Error) {
          if (error.name === 'AbortError') {
            throw new Error('Request was cancelled');
          }
          throw error;
        }
        
        throw new Error(ERROR_MESSAGES.GENERIC);
      }
    },
    [performFetch, retries, retryDelay, retryOnNetworkError]
  );

  /**
   * Main function to execute API requests
   * 
   * @param url - The API endpoint URL
   * @param fetchOptions - Additional fetch options (method, body, headers, etc.)
   * @returns Promise that resolves to the response data
   */
  const execute = useCallback(
    async (url: string, fetchOptions: RequestInit = {}): Promise<T | null> => {
      // Cancel any ongoing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Set loading state
      if (showLoading) {
        safeSetState({ loading: true, error: null, success: false });
      }

      try {
        // Execute the request with retry logic
        const data = await executeWithRetry(url, fetchOptions);
        
        // Update state with successful response
        safeSetState({
          data,
          loading: false,
          error: null,
          success: true,
        });

        return data;
      } catch (error) {
        // Handle error
        const errorMessage = error instanceof Error ? error.message : ERROR_MESSAGES.GENERIC;
        
        safeSetState({
          data: null,
          loading: false,
          error: errorMessage,
          success: false,
        });

        // Re-throw error so caller can handle it if needed
        throw error;
      }
    },
    [showLoading, safeSetState, executeWithRetry]
  );

  /**
   * Function to cancel any ongoing request
   */
  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    
    safeSetState({
      loading: false,
      error: 'Request was cancelled',
      success: false,
    });
  }, [safeSetState]);

  /**
   * Function to reset the state to initial values
   */
  const reset = useCallback(() => {
    // Cancel any ongoing request
    cancel();
    
    // Reset state
    safeSetState({
      data: null,
      loading: false,
      error: null,
      success: false,
    });
  }, [cancel, safeSetState]);

  /**
   * Cleanup effect to handle component unmount
   */
  useEffect(() => {
    // Set mounted flag
    isMountedRef.current = true;

    // Cleanup function
    return () => {
      // Mark component as unmounted
      isMountedRef.current = false;
      
      // Cancel any ongoing requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Return the state and control functions
  return {
    /** The response data */
    data: state.data,
    /** Loading state indicator */
    loading: state.loading,
    /** Error message (null if no error) */
    error: state.error,
    /** Whether the last request was successful */
    success: state.success,
    /** Function to execute API requests */
    execute,
    /** Function to cancel ongoing requests */
    cancel,
    /** Function to reset state */
    reset,
  };
}
