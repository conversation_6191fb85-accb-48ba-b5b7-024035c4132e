'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { supabase } from '@/lib/supabase';

interface Company {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  user_id: string;
}

interface FinancialYear {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
  is_active: boolean;
  company_id: string;
  created_at: string;
}

interface AppContextType {
  companies: Company[];
  currentCompany: Company | null;
  financialYears: FinancialYear[];
  currentFinancialYear: FinancialYear | null;
  loading: boolean;
  setCurrentCompany: (company: Company | null) => void;
  setCurrentFinancialYear: (year: FinancialYear | null) => void;
  refreshCompanies: () => Promise<void>;
  refreshFinancialYears: () => Promise<void>;
  createCompany: (name: string, description?: string) => Promise<{ error?: string }>;
  createFinancialYear: (name: string, startDate: string, endDate: string) => Promise<{ error?: string }>;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [companies, setCompanies] = useState<Company[]>([]);
  const [currentCompany, setCurrentCompany] = useState<Company | null>(null);
  const [financialYears, setFinancialYears] = useState<FinancialYear[]>([]);
  const [currentFinancialYear, setCurrentFinancialYear] = useState<FinancialYear | null>(null);
  const [loading, setLoading] = useState(false);

  const refreshCompanies = async () => {
    if (!user) return;

    setLoading(true);
    try {
      if (DEMO_MODE) {
        // Use demo data
        const userCompanies = DEMO_COMPANIES.filter(c => c.user_id === user.id);
        setCompanies(userCompanies);

        // Set first company as current if none selected
        if (!currentCompany && userCompanies.length > 0) {
          setCurrentCompany(userCompanies[0]);
        }
        setLoading(false);
        return;
      }

      const { data, error } = await supabase
        .from('companies')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching companies:', error);
      } else {
        setCompanies(data || []);

        // Set first company as current if none selected
        if (!currentCompany && data && data.length > 0) {
          setCurrentCompany(data[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching companies:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshFinancialYears = async () => {
    if (!currentCompany) return;

    setLoading(true);
    try {
      if (DEMO_MODE) {
        // Use demo data
        const companyYears = DEMO_FINANCIAL_YEARS.filter(y => y.company_id === currentCompany.id);
        setFinancialYears(companyYears);

        // Set active year as current or first year if none selected
        if (companyYears.length > 0) {
          const activeYear = companyYears.find(year => year.is_active);
          if (activeYear && !currentFinancialYear) {
            setCurrentFinancialYear(activeYear);
          } else if (!currentFinancialYear) {
            setCurrentFinancialYear(companyYears[0]);
          }
        }
        setLoading(false);
        return;
      }

      const { data, error } = await supabase
        .from('financial_years')
        .select('*')
        .eq('company_id', currentCompany.id)
        .order('start_date', { ascending: false });

      if (error) {
        console.error('Error fetching financial years:', error);
      } else {
        setFinancialYears(data || []);

        // Set active year as current or first year if none selected
        if (data && data.length > 0) {
          const activeYear = data.find(year => year.is_active);
          if (activeYear && !currentFinancialYear) {
            setCurrentFinancialYear(activeYear);
          } else if (!currentFinancialYear) {
            setCurrentFinancialYear(data[0]);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching financial years:', error);
    } finally {
      setLoading(false);
    }
  };

  const createCompany = async (name: string, description?: string) => {
    if (!user) return { error: 'User not authenticated' };

    try {
      const { data, error } = await supabase
        .from('companies')
        .insert([
          {
            name,
            description,
            user_id: user.id,
          },
        ])
        .select()
        .single();

      if (error) {
        return { error: error.message };
      }

      await refreshCompanies();
      return {};
    } catch (error) {
      return { error: 'An unexpected error occurred' };
    }
  };

  const createFinancialYear = async (name: string, startDate: string, endDate: string) => {
    if (!currentCompany) return { error: 'No company selected' };

    try {
      const { data, error } = await supabase
        .from('financial_years')
        .insert([
          {
            name,
            start_date: startDate,
            end_date: endDate,
            company_id: currentCompany.id,
            is_active: false,
          },
        ])
        .select()
        .single();

      if (error) {
        return { error: error.message };
      }

      await refreshFinancialYears();
      return {};
    } catch (error) {
      return { error: 'An unexpected error occurred' };
    }
  };

  useEffect(() => {
    if (user) {
      refreshCompanies();
    } else {
      setCompanies([]);
      setCurrentCompany(null);
      setFinancialYears([]);
      setCurrentFinancialYear(null);
    }
  }, [user]);

  useEffect(() => {
    if (currentCompany) {
      refreshFinancialYears();
    } else {
      setFinancialYears([]);
      setCurrentFinancialYear(null);
    }
  }, [currentCompany]);

  const value = {
    companies,
    currentCompany,
    financialYears,
    currentFinancialYear,
    loading,
    setCurrentCompany,
    setCurrentFinancialYear,
    refreshCompanies,
    refreshFinancialYears,
    createCompany,
    createFinancialYear,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
}

export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
