/**
 * Database Migration System
 * 
 * This file provides database-agnostic migration management that supports
 * SQLite, MySQL, and PostgreSQL with automatic schema creation and seeding.
 * 
 * Following DRY principle: Single migration system for all database types
 * Following YAGNI principle: Only essential migration features
 */

import { getDatabase, getRawConnection } from './connection';
import { databaseConfig, getSqlDialect } from './config';
import { defaultAccountTypes } from './schemas';
import * as schema from './schemas';
import bcrypt from 'bcryptjs';

/**
 * Migration interface
 */
interface Migration {
  id: string;
  name: string;
  up: () => Promise<void>;
  down: () => Promise<void>;
}

/**
 * Create migrations table for tracking applied migrations
 */
async function createMigrationsTable(): Promise<void> {
  const rawConnection = getRawConnection();
  const dialect = getSqlDialect();

  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS migrations (
      id ${dialect.textType} PRIMARY KEY,
      name ${dialect.textType} NOT NULL,
      applied_at ${dialect.dateTimeType} DEFAULT ${dialect.currentTimestamp}
    )
  `;

  switch (databaseConfig.type) {
    case 'sqlite':
      if (rawConnection && 'exec' in rawConnection) {
        (rawConnection as any).exec(createTableSQL);
      }
      break;
    case 'mysql':
      if (rawConnection && 'execute' in rawConnection) {
        await (rawConnection as any).execute(createTableSQL);
      }
      break;
    case 'postgresql':
      if (rawConnection && 'unsafe' in rawConnection) {
        await (rawConnection as any).unsafe(createTableSQL);
      }
      break;
  }
}

/**
 * Check if migration has been applied
 */
async function isMigrationApplied(migrationId: string): Promise<boolean> {
  const db = await getDatabase();
  
  try {
    const result = await (db as any).select()
      .from('migrations')
      .where('id', migrationId)
      .limit(1);
    
    return result.length > 0;
  } catch (error) {
    // If migrations table doesn't exist, migration hasn't been applied
    return false;
  }
}

/**
 * Mark migration as applied
 */
async function markMigrationApplied(migration: Migration): Promise<void> {
  const db = await getDatabase();
  
  await (db as any).insert('migrations').values({
    id: migration.id,
    name: migration.name,
    applied_at: new Date().toISOString(),
  });
}

/**
 * Create database schema tables
 */
async function createSchemaTables(): Promise<void> {
  const rawConnection = getRawConnection();
  const dialect = getSqlDialect();

  const tables = [
    // Users table
    `CREATE TABLE IF NOT EXISTS users (
      id ${dialect.textType} PRIMARY KEY,
      email ${dialect.textType} UNIQUE NOT NULL,
      name ${dialect.textType} NOT NULL,
      password_hash ${dialect.textType} NOT NULL,
      created_at ${dialect.dateTimeType} DEFAULT ${dialect.currentTimestamp},
      updated_at ${dialect.dateTimeType} DEFAULT ${dialect.currentTimestamp}
    )`,

    // Companies table
    `CREATE TABLE IF NOT EXISTS companies (
      id ${dialect.textType} PRIMARY KEY,
      name ${dialect.textType} NOT NULL,
      description ${dialect.textType},
      user_id ${dialect.textType} NOT NULL,
      created_at ${dialect.dateTimeType} DEFAULT ${dialect.currentTimestamp},
      updated_at ${dialect.dateTimeType} DEFAULT ${dialect.currentTimestamp},
      FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
    )`,

    // Financial Years table
    `CREATE TABLE IF NOT EXISTS financial_years (
      id ${dialect.textType} PRIMARY KEY,
      name ${dialect.textType} NOT NULL,
      start_date ${dialect.textType} NOT NULL,
      end_date ${dialect.textType} NOT NULL,
      is_active ${dialect.booleanType} DEFAULT FALSE,
      company_id ${dialect.textType} NOT NULL,
      created_at ${dialect.dateTimeType} DEFAULT ${dialect.currentTimestamp},
      updated_at ${dialect.dateTimeType} DEFAULT ${dialect.currentTimestamp},
      FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE
    )`,

    // Account Types table
    `CREATE TABLE IF NOT EXISTS account_types (
      id ${dialect.textType} PRIMARY KEY,
      name ${dialect.textType} NOT NULL,
      category ${dialect.textType} NOT NULL,
      description ${dialect.textType},
      created_at ${dialect.dateTimeType} DEFAULT ${dialect.currentTimestamp}
    )`,

    // Accounts table
    `CREATE TABLE IF NOT EXISTS accounts (
      id ${dialect.textType} PRIMARY KEY,
      code ${dialect.textType} NOT NULL,
      name ${dialect.textType} NOT NULL,
      account_type_id ${dialect.textType} NOT NULL,
      parent_account_id ${dialect.textType},
      is_active ${dialect.booleanType} DEFAULT TRUE,
      company_id ${dialect.textType} NOT NULL,
      financial_year_id ${dialect.textType} NOT NULL,
      created_at ${dialect.dateTimeType} DEFAULT ${dialect.currentTimestamp},
      updated_at ${dialect.dateTimeType} DEFAULT ${dialect.currentTimestamp},
      FOREIGN KEY (account_type_id) REFERENCES account_types (id),
      FOREIGN KEY (parent_account_id) REFERENCES accounts (id),
      FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE,
      FOREIGN KEY (financial_year_id) REFERENCES financial_years (id) ON DELETE CASCADE
    )`,

    // Journal Entries table
    `CREATE TABLE IF NOT EXISTS journal_entries (
      id ${dialect.textType} PRIMARY KEY,
      entry_number ${dialect.textType} NOT NULL,
      date ${dialect.textType} NOT NULL,
      description ${dialect.textType} NOT NULL,
      reference ${dialect.textType},
      total_debit ${dialect.realType} DEFAULT 0,
      total_credit ${dialect.realType} DEFAULT 0,
      status ${dialect.textType} DEFAULT 'DRAFT',
      company_id ${dialect.textType} NOT NULL,
      financial_year_id ${dialect.textType} NOT NULL,
      created_by ${dialect.textType} NOT NULL,
      created_at ${dialect.dateTimeType} DEFAULT ${dialect.currentTimestamp},
      updated_at ${dialect.dateTimeType} DEFAULT ${dialect.currentTimestamp},
      FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE,
      FOREIGN KEY (financial_year_id) REFERENCES financial_years (id) ON DELETE CASCADE,
      FOREIGN KEY (created_by) REFERENCES users (id)
    )`,

    // Journal Entry Lines table
    `CREATE TABLE IF NOT EXISTS journal_entry_lines (
      id ${dialect.textType} PRIMARY KEY,
      journal_entry_id ${dialect.textType} NOT NULL,
      account_id ${dialect.textType} NOT NULL,
      description ${dialect.textType},
      debit_amount ${dialect.realType} DEFAULT 0,
      credit_amount ${dialect.realType} DEFAULT 0,
      line_number ${dialect.integerType} NOT NULL,
      created_at ${dialect.dateTimeType} DEFAULT ${dialect.currentTimestamp},
      FOREIGN KEY (journal_entry_id) REFERENCES journal_entries (id) ON DELETE CASCADE,
      FOREIGN KEY (account_id) REFERENCES accounts (id)
    )`,
  ];

  // Create indexes
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_companies_user_id ON companies (user_id)',
    'CREATE INDEX IF NOT EXISTS idx_financial_years_company_id ON financial_years (company_id)',
    'CREATE INDEX IF NOT EXISTS idx_accounts_company_id ON accounts (company_id)',
    'CREATE INDEX IF NOT EXISTS idx_accounts_financial_year_id ON accounts (financial_year_id)',
    'CREATE INDEX IF NOT EXISTS idx_journal_entries_company_id ON journal_entries (company_id)',
    'CREATE INDEX IF NOT EXISTS idx_journal_entries_financial_year_id ON journal_entries (financial_year_id)',
    'CREATE INDEX IF NOT EXISTS idx_journal_entry_lines_journal_entry_id ON journal_entry_lines (journal_entry_id)',
    'CREATE INDEX IF NOT EXISTS idx_users_email ON users (email)',
    'CREATE INDEX IF NOT EXISTS idx_accounts_code ON accounts (code)',
    'CREATE INDEX IF NOT EXISTS idx_journal_entries_date ON journal_entries (date)',
  ];

  // Execute table creation
  for (const tableSQL of tables) {
    switch (databaseConfig.type) {
      case 'sqlite':
        if (rawConnection && 'exec' in rawConnection) {
          (rawConnection as any).exec(tableSQL);
        }
        break;
      case 'mysql':
        if (rawConnection && 'execute' in rawConnection) {
          await (rawConnection as any).execute(tableSQL);
        }
        break;
      case 'postgresql':
        if (rawConnection && 'unsafe' in rawConnection) {
          await (rawConnection as any).unsafe(tableSQL);
        }
        break;
    }
  }

  // Execute index creation
  for (const indexSQL of indexes) {
    try {
      switch (databaseConfig.type) {
        case 'sqlite':
          if (rawConnection && 'exec' in rawConnection) {
            (rawConnection as any).exec(indexSQL);
          }
          break;
        case 'mysql':
          if (rawConnection && 'execute' in rawConnection) {
            await (rawConnection as any).execute(indexSQL);
          }
          break;
        case 'postgresql':
          if (rawConnection && 'unsafe' in rawConnection) {
            await (rawConnection as any).unsafe(indexSQL);
          }
          break;
      }
    } catch (error) {
      // Ignore index creation errors (might already exist)
      console.warn(`Index creation warning: ${error}`);
    }
  }
}

/**
 * Seed default data
 */
async function seedDefaultData(): Promise<void> {
  const db = await getDatabase();

  try {
    // Insert default account types
    for (const accountType of defaultAccountTypes) {
      await (db as any).insert(schema.accountTypes)
        .values(accountType)
        .onConflictDoNothing();
    }

    // Create default admin user
    const adminExists = await (db as any).select()
      .from(schema.users)
      .where('email', '<EMAIL>')
      .limit(1);

    if (adminExists.length === 0) {
      const passwordHash = await bcrypt.hash('123', 10);
      const userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      await (db as any).insert(schema.users).values({
        id: userId,
        email: '<EMAIL>',
        name: 'Admin User',
        passwordHash,
      });

      console.log('✅ Default admin user created: <EMAIL> / 123');
    }

  } catch (error) {
    console.error('❌ Error seeding default data:', error);
    throw error;
  }
}

/**
 * Migration definitions
 */
const migrations: Migration[] = [
  {
    id: '001',
    name: 'create_initial_schema',
    up: async () => {
      await createSchemaTables();
      console.log('✅ Initial schema created');
    },
    down: async () => {
      // Drop tables in reverse order
      const tables = [
        'journal_entry_lines',
        'journal_entries',
        'accounts',
        'account_types',
        'financial_years',
        'companies',
        'users',
      ];

      const rawConnection = getRawConnection();
      for (const table of tables) {
        const dropSQL = `DROP TABLE IF EXISTS ${table}`;
        
        switch (databaseConfig.type) {
          case 'sqlite':
            if (rawConnection && 'exec' in rawConnection) {
              (rawConnection as any).exec(dropSQL);
            }
            break;
          case 'mysql':
            if (rawConnection && 'execute' in rawConnection) {
              await (rawConnection as any).execute(dropSQL);
            }
            break;
          case 'postgresql':
            if (rawConnection && 'unsafe' in rawConnection) {
              await (rawConnection as any).unsafe(dropSQL);
            }
            break;
        }
      }
      console.log('✅ Initial schema dropped');
    },
  },
  {
    id: '002',
    name: 'seed_default_data',
    up: async () => {
      await seedDefaultData();
      console.log('✅ Default data seeded');
    },
    down: async () => {
      // Clear default data
      const db = await getDatabase();
      await (db as any).delete().from(schema.users).where('email', '<EMAIL>');
      await (db as any).delete().from(schema.accountTypes);
      console.log('✅ Default data cleared');
    },
  },
];

/**
 * Run all pending migrations
 */
export async function runMigrations(): Promise<void> {
  console.log('🚀 Running database migrations...');

  try {
    // Ensure migrations table exists
    await createMigrationsTable();

    // Run each migration if not already applied
    for (const migration of migrations) {
      const isApplied = await isMigrationApplied(migration.id);
      
      if (!isApplied) {
        console.log(`📋 Running migration: ${migration.name}`);
        await migration.up();
        await markMigrationApplied(migration);
        console.log(`✅ Migration completed: ${migration.name}`);
      } else {
        console.log(`⏭️  Migration already applied: ${migration.name}`);
      }
    }

    console.log('🎉 All migrations completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

/**
 * Rollback last migration
 */
export async function rollbackMigration(): Promise<void> {
  console.log('🔄 Rolling back last migration...');

  try {
    const db = await getDatabase();
    
    // Get last applied migration
    const lastMigration = await (db as any).select()
      .from('migrations')
      .orderBy('applied_at', 'desc')
      .limit(1);

    if (lastMigration.length === 0) {
      console.log('ℹ️  No migrations to rollback');
      return;
    }

    const migrationToRollback = migrations.find(m => m.id === lastMigration[0].id);
    
    if (migrationToRollback) {
      console.log(`📋 Rolling back migration: ${migrationToRollback.name}`);
      await migrationToRollback.down();
      
      // Remove from migrations table
      await (db as any).delete()
        .from('migrations')
        .where('id', migrationToRollback.id);
      
      console.log(`✅ Migration rolled back: ${migrationToRollback.name}`);
    } else {
      console.log('❌ Migration definition not found');
    }

  } catch (error) {
    console.error('❌ Rollback failed:', error);
    throw error;
  }
}

/**
 * Get migration status
 */
export async function getMigrationStatus(): Promise<{
  total: number;
  applied: number;
  pending: string[];
}> {
  try {
    await createMigrationsTable();
    
    const appliedMigrations = await (await getDatabase() as any).select()
      .from('migrations')
      .orderBy('applied_at', 'asc');

    const appliedIds = appliedMigrations.map((m: any) => m.id);
    const pendingMigrations = migrations.filter(m => !appliedIds.includes(m.id));

    return {
      total: migrations.length,
      applied: appliedMigrations.length,
      pending: pendingMigrations.map(m => m.name),
    };

  } catch (error) {
    console.error('❌ Error getting migration status:', error);
    return {
      total: migrations.length,
      applied: 0,
      pending: migrations.map(m => m.name),
    };
  }
}
