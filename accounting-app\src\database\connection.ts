/**
 * Database Connection Manager
 * 
 * This file provides database-agnostic connection management that supports
 * SQLite, MySQL, and PostgreSQL with automatic switching based on configuration.
 * 
 * Following DRY principle: Single connection manager for all database types
 * Following YAGNI principle: Only essential connection features
 */

import { drizzle } from 'drizzle-orm/better-sqlite3';
import { drizzle as drizzleMysql } from 'drizzle-orm/mysql2';
import { drizzle as drizzlePostgres } from 'drizzle-orm/postgres-js';
import Database from 'better-sqlite3';
import mysql from 'mysql2/promise';
import postgres from 'postgres';
import * as schema from './schemas';
import { databaseConfig, getConnectionString, getDatabaseFeatures } from './config';
import path from 'path';
import fs from 'fs';

/**
 * Database connection type definitions
 */
type SQLiteConnection = ReturnType<typeof drizzle>;
type MySQLConnection = ReturnType<typeof drizzleMysql>;
type PostgreSQLConnection = ReturnType<typeof drizzlePostgres>;

export type DatabaseConnection = SQLiteConnection | MySQLConnection | PostgreSQLConnection;

/**
 * Connection instances (Singleton pattern)
 */
let connectionInstance: DatabaseConnection | null = null;
let rawConnectionInstance: Database.Database | mysql.Connection | postgres.Sql | null = null;

/**
 * Create SQLite connection
 */
function createSQLiteConnection(): SQLiteConnection {
  const connectionString = getConnectionString();
  
  // Ensure data directory exists
  const dataDir = path.dirname(connectionString);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }

  // Create SQLite database instance
  const sqlite = new Database(connectionString, {
    timeout: databaseConfig.connectionTimeout,
  });

  // Configure SQLite for optimal performance
  sqlite.pragma('journal_mode = WAL');
  sqlite.pragma('foreign_keys = ON');
  sqlite.pragma('synchronous = NORMAL');
  sqlite.pragma('cache_size = 1000000');
  sqlite.pragma('temp_store = memory');

  // Store raw connection for schema operations
  rawConnectionInstance = sqlite;

  // Create Drizzle ORM instance
  return drizzle(sqlite, { schema });
}

/**
 * Create MySQL connection
 */
async function createMySQLConnection(): Promise<MySQLConnection> {
  const connectionString = getConnectionString();

  // Create MySQL connection pool
  const pool = mysql.createPool({
    uri: connectionString,
    connectionLimit: databaseConfig.poolMax,
    acquireTimeout: databaseConfig.connectionTimeout,
    timeout: databaseConfig.connectionTimeout,
    charset: databaseConfig.charset || 'utf8mb4',
    timezone: databaseConfig.timezone || 'Z',
    ssl: databaseConfig.ssl,
  });

  // Store raw connection for schema operations
  rawConnectionInstance = await pool.getConnection();

  // Create Drizzle ORM instance
  return drizzleMysql(pool, { schema, mode: 'default' });
}

/**
 * Create PostgreSQL connection
 */
function createPostgreSQLConnection(): PostgreSQLConnection {
  const connectionString = getConnectionString();

  // Create PostgreSQL connection
  const sql = postgres(connectionString, {
    max: databaseConfig.poolMax,
    idle_timeout: databaseConfig.connectionTimeout,
    connect_timeout: databaseConfig.connectionTimeout,
    ssl: databaseConfig.ssl,
  });

  // Store raw connection for schema operations
  rawConnectionInstance = sql;

  // Create Drizzle ORM instance
  return drizzlePostgres(sql, { schema });
}

/**
 * Get database connection (Singleton pattern)
 * Automatically creates the appropriate connection based on configuration
 */
export async function getDatabase(): Promise<DatabaseConnection> {
  if (connectionInstance) {
    return connectionInstance;
  }

  try {
    console.log(`🔌 Connecting to ${databaseConfig.type.toUpperCase()} database...`);

    switch (databaseConfig.type) {
      case 'sqlite':
        connectionInstance = createSQLiteConnection();
        break;

      case 'mysql':
        connectionInstance = await createMySQLConnection();
        break;

      case 'postgresql':
        connectionInstance = createPostgreSQLConnection();
        break;

      default:
        throw new Error(`Unsupported database type: ${databaseConfig.type}`);
    }

    console.log(`✅ ${databaseConfig.type.toUpperCase()} database connected successfully`);
    return connectionInstance;

  } catch (error) {
    console.error(`❌ Failed to connect to ${databaseConfig.type.toUpperCase()} database:`, error);
    throw new Error(`Database connection failed: ${error}`);
  }
}

/**
 * Get raw database connection for schema operations
 */
export function getRawConnection(): Database.Database | mysql.Connection | postgres.Sql | null {
  return rawConnectionInstance;
}

/**
 * Close database connection
 */
export async function closeDatabase(): Promise<void> {
  if (!connectionInstance || !rawConnectionInstance) {
    return;
  }

  try {
    console.log(`🔌 Closing ${databaseConfig.type.toUpperCase()} database connection...`);

    switch (databaseConfig.type) {
      case 'sqlite':
        if (rawConnectionInstance && 'close' in rawConnectionInstance) {
          (rawConnectionInstance as Database.Database).close();
        }
        break;

      case 'mysql':
        if (rawConnectionInstance && 'end' in rawConnectionInstance) {
          await (rawConnectionInstance as mysql.Connection).end();
        }
        break;

      case 'postgresql':
        if (rawConnectionInstance && 'end' in rawConnectionInstance) {
          await (rawConnectionInstance as postgres.Sql).end();
        }
        break;
    }

    connectionInstance = null;
    rawConnectionInstance = null;
    console.log(`✅ ${databaseConfig.type.toUpperCase()} database connection closed`);

  } catch (error) {
    console.error(`❌ Error closing ${databaseConfig.type.toUpperCase()} database:`, error);
  }
}

/**
 * Test database connection
 */
export async function testConnection(): Promise<boolean> {
  try {
    const db = await getDatabase();
    
    // Simple test query based on database type
    switch (databaseConfig.type) {
      case 'sqlite':
        await (db as SQLiteConnection).run('SELECT 1');
        break;
      case 'mysql':
        await (db as MySQLConnection).execute('SELECT 1');
        break;
      case 'postgresql':
        await (db as PostgreSQLConnection)`SELECT 1`;
        break;
    }

    console.log(`✅ ${databaseConfig.type.toUpperCase()} database connection test successful`);
    return true;

  } catch (error) {
    console.error(`❌ ${databaseConfig.type.toUpperCase()} database connection test failed:`, error);
    return false;
  }
}

/**
 * Get database information
 */
export function getDatabaseInfo() {
  const features = getDatabaseFeatures();
  
  return {
    type: databaseConfig.type,
    connectionString: getConnectionString().replace(/password=[^&]+/g, 'password=***'),
    features,
    config: {
      ...databaseConfig,
      password: databaseConfig.password ? '***' : undefined,
    },
  };
}

/**
 * Database health check
 */
export async function healthCheck(): Promise<{
  status: 'healthy' | 'unhealthy';
  type: string;
  uptime: number;
  features: any;
  error?: string;
}> {
  const startTime = Date.now();
  
  try {
    const isConnected = await testConnection();
    const uptime = Date.now() - startTime;
    
    if (isConnected) {
      return {
        status: 'healthy',
        type: databaseConfig.type,
        uptime,
        features: getDatabaseFeatures(),
      };
    } else {
      return {
        status: 'unhealthy',
        type: databaseConfig.type,
        uptime,
        features: getDatabaseFeatures(),
        error: 'Connection test failed',
      };
    }
  } catch (error) {
    const uptime = Date.now() - startTime;
    return {
      status: 'unhealthy',
      type: databaseConfig.type,
      uptime,
      features: getDatabaseFeatures(),
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Export database instance for direct use
 */
export { schema };
