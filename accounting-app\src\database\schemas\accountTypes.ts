/**
 * Account Types Schema Definition
 * 
 * This file defines the account types table schema using Drizzle ORM.
 * It handles predefined account categories for the chart of accounts.
 * 
 * Following DRY principle: Single schema per entity
 * Following YAGNI principle: Only essential account type fields
 */

import { sqliteTable, text, index } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

/**
 * Account category enum for type safety
 * Following proper ORM patterns: Type-safe enums
 */
export const AccountCategory = {
  ASSET: 'ASSET',
  LIABILITY: 'LIABILITY',
  EQUITY: 'EQUITY',
  REVENUE: 'REVENUE',
  EXPENSE: 'EXPENSE',
} as const;

export type AccountCategoryType = typeof AccountCategory[keyof typeof AccountCategory];

/**
 * Account Types table - Predefined account categories
 * These are the standard accounting categories (Asset, Liability, etc.)
 * 
 * Following proper ORM patterns: Enum-like constraints
 */
export const accountTypes = sqliteTable('account_types', {
  /** Unique identifier for the account type */
  id: text('id').primaryKey(),
  
  /** Display name of the account type (e.g., "Current Assets") */
  name: text('name').notNull(),
  
  /** The main accounting category this type belongs to */
  category: text('category').notNull().$type<AccountCategoryType>(),
  
  /** Optional description explaining this account type */
  description: text('description'),
  
  /** Timestamp when the account type was created */
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  /** Index on category for filtering by account category */
  categoryIdx: index('account_types_category_idx').on(table.category),
  
  /** Index on name for searching account types by name */
  nameIdx: index('account_types_name_idx').on(table.name),
  
  /** Composite index for category and name searches */
  categoryNameIdx: index('account_types_category_name_idx').on(table.category, table.name),
}));

/**
 * Type definitions for account types table operations
 * These provide type safety for insert, select, and update operations
 */

/** Type for inserting a new account type */
export type InsertAccountType = typeof accountTypes.$inferInsert;

/** Type for selecting account type data */
export type SelectAccountType = typeof accountTypes.$inferSelect;

/** Type for updating account type data (excludes id and timestamps) */
export type UpdateAccountType = Partial<Pick<SelectAccountType, 'name' | 'category' | 'description'>>;

/**
 * Account type validation constraints
 * Following DRY principle: Centralized validation rules
 */
export const AccountTypeConstraints = {
  /** Minimum length for account type names */
  NAME_MIN_LENGTH: 1,
  /** Maximum length for account type names */
  NAME_MAX_LENGTH: 100,
  /** Maximum length for account type descriptions */
  DESCRIPTION_MAX_LENGTH: 500,
} as const;

/**
 * Default account types data
 * These will be inserted during database initialization
 * 
 * Following DRY principle: Centralized default data
 */
export const defaultAccountTypes: InsertAccountType[] = [
  {
    id: 'current-assets',
    name: 'Current Assets',
    category: AccountCategory.ASSET,
    description: 'Assets that can be converted to cash within one year',
  },
  {
    id: 'fixed-assets',
    name: 'Fixed Assets',
    category: AccountCategory.ASSET,
    description: 'Long-term assets used in business operations',
  },
  {
    id: 'current-liabilities',
    name: 'Current Liabilities',
    category: AccountCategory.LIABILITY,
    description: 'Debts due within one year',
  },
  {
    id: 'long-term-liabilities',
    name: 'Long-term Liabilities',
    category: AccountCategory.LIABILITY,
    description: 'Debts due after one year',
  },
  {
    id: 'owners-equity',
    name: 'Owner\'s Equity',
    category: AccountCategory.EQUITY,
    description: 'Owner\'s stake in the business',
  },
  {
    id: 'revenue',
    name: 'Revenue',
    category: AccountCategory.REVENUE,
    description: 'Income from business operations',
  },
  {
    id: 'operating-expenses',
    name: 'Operating Expenses',
    category: AccountCategory.EXPENSE,
    description: 'Costs of running the business',
  },
  {
    id: 'other-income',
    name: 'Other Income',
    category: AccountCategory.REVENUE,
    description: 'Non-operating income',
  },
  {
    id: 'other-expenses',
    name: 'Other Expenses',
    category: AccountCategory.EXPENSE,
    description: 'Non-operating expenses',
  },
];

/**
 * Account type utility types
 * Following proper ORM patterns: Type-safe utilities
 */

/** Account type grouped by category */
export type AccountTypesByCategory = {
  [K in AccountCategoryType]: SelectAccountType[];
};

/** Account type statistics */
export type AccountTypeStats = {
  totalTypes: number;
  typesByCategory: Record<AccountCategoryType, number>;
};

/**
 * Account type helper functions types
 * Following DRY principle: Reusable utility function types
 */

/** Type for account type creation input */
export type CreateAccountTypeInput = {
  name: string;
  category: AccountCategoryType;
  description?: string;
};

/** Type for account type filter options */
export type AccountTypeFilters = {
  category?: AccountCategoryType;
  search?: string;
};
