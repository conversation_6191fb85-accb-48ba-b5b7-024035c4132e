{"version": 3, "sources": ["../../src/sqlite-proxy/driver.ts"], "sourcesContent": ["import type { BatchItem, BatchResponse } from '~/batch.ts';\nimport { entityKind } from '~/entity.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { createTableRelationsHelpers, extractTablesRelationalConfig } from '~/relations.ts';\nimport type { ExtractTablesWithRelations, RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { BaseSQLiteDatabase } from '~/sqlite-core/db.ts';\nimport { SQLiteAsyncDialect } from '~/sqlite-core/dialect.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { SQLiteRemoteSession } from './session.ts';\n\nexport interface SqliteRemoteResult<T = unknown> {\n\trows?: T[];\n}\n\nexport class SqliteRemoteDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends BaseSQLiteDatabase<'async', SqliteRemoteResult, TSchema> {\n\tstatic override readonly [entityKind]: string = 'SqliteRemoteDatabase';\n\n\t/** @internal */\n\tdeclare readonly session: SQLiteRemoteSession<TSchema, ExtractTablesWithRelations<TSchema>>;\n\n\tasync batch<U extends BatchItem<'sqlite'>, T extends Readonly<[U, ...U[]]>>(\n\t\tbatch: T,\n\t): Promise<BatchResponse<T>> {\n\t\treturn this.session.batch(batch) as Promise<BatchResponse<T>>;\n\t}\n}\n\nexport type AsyncRemoteCallback = (\n\tsql: string,\n\tparams: any[],\n\tmethod: 'run' | 'all' | 'values' | 'get',\n) => Promise<{ rows: any[] }>;\n\nexport type AsyncBatchRemoteCallback = (batch: {\n\tsql: string;\n\tparams: any[];\n\tmethod: 'run' | 'all' | 'values' | 'get';\n}[]) => Promise<{ rows: any[] }[]>;\n\nexport type RemoteCallback = AsyncRemoteCallback;\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tcallback: RemoteCallback,\n\tconfig?: DrizzleConfig<TSchema>,\n): SqliteRemoteDatabase<TSchema>;\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tcallback: RemoteCallback,\n\tbatchCallback?: AsyncBatchRemoteCallback,\n\tconfig?: DrizzleConfig<TSchema>,\n): SqliteRemoteDatabase<TSchema>;\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tcallback: RemoteCallback,\n\tbatchCallback?: AsyncBatchRemoteCallback | DrizzleConfig<TSchema>,\n\tconfig?: DrizzleConfig<TSchema>,\n): SqliteRemoteDatabase<TSchema> {\n\tconst dialect = new SQLiteAsyncDialect({ casing: config?.casing });\n\tlet logger;\n\tlet _batchCallback: AsyncBatchRemoteCallback | undefined;\n\tlet _config: DrizzleConfig<TSchema> = {};\n\n\tif (batchCallback) {\n\t\tif (typeof batchCallback === 'function') {\n\t\t\t_batchCallback = batchCallback as AsyncBatchRemoteCallback;\n\t\t\t_config = config ?? {};\n\t\t} else {\n\t\t\t_batchCallback = undefined;\n\t\t\t_config = batchCallback as DrizzleConfig<TSchema>;\n\t\t}\n\n\t\tif (_config.logger === true) {\n\t\t\tlogger = new DefaultLogger();\n\t\t} else if (_config.logger !== false) {\n\t\t\tlogger = _config.logger;\n\t\t}\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (_config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\t_config.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: _config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new SQLiteRemoteSession(callback, dialect, schema, _batchCallback, { logger });\n\treturn new SqliteRemoteDatabase('async', dialect, session, schema) as SqliteRemoteDatabase<TSchema>;\n}\n"], "mappings": "AACA,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B,SAAS,6BAA6B,qCAAqC;AAE3E,SAAS,0BAA0B;AACnC,SAAS,0BAA0B;AAEnC,SAAS,2BAA2B;AAM7B,MAAM,6BAEH,mBAAyD;AAAA,EAClE,QAA0B,UAAU,IAAY;AAAA,EAKhD,MAAM,MACL,OAC4B;AAC5B,WAAO,KAAK,QAAQ,MAAM,KAAK;AAAA,EAChC;AACD;AAyBO,SAAS,QACf,UACA,eACA,QACgC;AAChC,QAAM,UAAU,IAAI,mBAAmB,EAAE,QAAQ,QAAQ,OAAO,CAAC;AACjE,MAAI;AACJ,MAAI;AACJ,MAAI,UAAkC,CAAC;AAEvC,MAAI,eAAe;AAClB,QAAI,OAAO,kBAAkB,YAAY;AACxC,uBAAiB;AACjB,gBAAU,UAAU,CAAC;AAAA,IACtB,OAAO;AACN,uBAAiB;AACjB,gBAAU;AAAA,IACX;AAEA,QAAI,QAAQ,WAAW,MAAM;AAC5B,eAAS,IAAI,cAAc;AAAA,IAC5B,WAAW,QAAQ,WAAW,OAAO;AACpC,eAAS,QAAQ;AAAA,IAClB;AAAA,EACD;AAEA,MAAI;AACJ,MAAI,QAAQ,QAAQ;AACnB,UAAM,eAAe;AAAA,MACpB,QAAQ;AAAA,MACR;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,QAAQ;AAAA,MACpB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,oBAAoB,UAAU,SAAS,QAAQ,gBAAgB,EAAE,OAAO,CAAC;AAC7F,SAAO,IAAI,qBAAqB,SAAS,SAAS,SAAS,MAAM;AAClE;", "names": []}