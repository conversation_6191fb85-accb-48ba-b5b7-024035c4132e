/**
 * Financial Years Schema Definition
 * 
 * This file defines the financial years table schema using Drizzle ORM.
 * It handles financial year periods for companies.
 * 
 * Following DRY principle: Single schema per entity
 * Following YAGNI principle: Only essential financial year fields
 */

import { sqliteTable, text, integer, index } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';
import { companies } from './companies';

/**
 * Financial Years table - Stores financial year periods for companies
 * Each company can have multiple financial years
 * 
 * Following proper ORM patterns: Boolean and date fields with constraints
 */
export const financialYears = sqliteTable('financial_years', {
  /** Unique identifier for the financial year */
  id: text('id').primaryKey(),
  
  /** Display name for the financial year (e.g., "FY 2024-25") */
  name: text('name').notNull(),
  
  /** Start date of the financial year (ISO date string) */
  startDate: text('start_date').notNull(),
  
  /** End date of the financial year (ISO date string) */
  endDate: text('end_date').notNull(),
  
  /** Whether this is the currently active financial year for the company */
  isActive: integer('is_active', { mode: 'boolean' }).default(false).notNull(),
  
  /** Reference to the company this financial year belongs to */
  companyId: text('company_id').notNull().references(() => companies.id, { onDelete: 'cascade' }),
  
  /** Timestamp when the financial year was created */
  createdAt: text('created_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
  
  /** Timestamp when the financial year was last updated */
  updatedAt: text('updated_at').default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  /** Index on company_id for fast lookups of company's financial years */
  companyIdIdx: index('financial_years_company_id_idx').on(table.companyId),
  
  /** Index on is_active for finding active financial years */
  activeIdx: index('financial_years_active_idx').on(table.isActive),
  
  /** Index on start_date for date-based queries */
  startDateIdx: index('financial_years_start_date_idx').on(table.startDate),
  
  /** Index on end_date for date-based queries */
  endDateIdx: index('financial_years_end_date_idx').on(table.endDate),
  
  /** Composite index for company-specific active year lookups */
  companyActiveIdx: index('financial_years_company_active_idx').on(table.companyId, table.isActive),
  
  /** Composite index for date range queries */
  dateRangeIdx: index('financial_years_date_range_idx').on(table.startDate, table.endDate),
}));

/**
 * Type definitions for financial years table operations
 * These provide type safety for insert, select, and update operations
 */

/** Type for inserting a new financial year */
export type InsertFinancialYear = typeof financialYears.$inferInsert;

/** Type for selecting financial year data */
export type SelectFinancialYear = typeof financialYears.$inferSelect;

/** Type for updating financial year data (excludes id and timestamps) */
export type UpdateFinancialYear = Partial<Pick<SelectFinancialYear, 'name' | 'startDate' | 'endDate' | 'isActive'>>;

/**
 * Financial year validation constraints
 * Following DRY principle: Centralized validation rules
 */
export const FinancialYearConstraints = {
  /** Minimum length for financial year names */
  NAME_MIN_LENGTH: 1,
  /** Maximum length for financial year names */
  NAME_MAX_LENGTH: 100,
  /** Minimum duration in days for a financial year */
  MIN_DURATION_DAYS: 1,
  /** Maximum duration in days for a financial year (typically 366 for leap years) */
  MAX_DURATION_DAYS: 366,
} as const;

/**
 * Financial year utility types and enums
 * Following proper ORM patterns: Type-safe enums and utilities
 */

/** Common financial year patterns */
export const FinancialYearPatterns = {
  /** April to March (common in India) */
  APRIL_MARCH: { startMonth: 4, startDay: 1, endMonth: 3, endDay: 31 },
  /** January to December (calendar year) */
  JANUARY_DECEMBER: { startMonth: 1, startDay: 1, endMonth: 12, endDay: 31 },
  /** July to June (common in Australia) */
  JULY_JUNE: { startMonth: 7, startDay: 1, endMonth: 6, endDay: 30 },
} as const;

/** Financial year with company information */
export type FinancialYearWithCompany = SelectFinancialYear & {
  company: {
    id: string;
    name: string;
    userId: string;
  };
};

/** Financial year statistics type */
export type FinancialYearStats = {
  totalYears: number;
  activeYears: number;
  yearsCreatedThisMonth: number;
  averageDurationDays: number;
};

/**
 * Financial year helper functions types
 * Following DRY principle: Reusable utility function types
 */

/** Type for financial year creation input */
export type CreateFinancialYearInput = {
  name: string;
  startDate: string;
  endDate: string;
  isActive?: boolean;
  companyId: string;
};

/** Type for financial year update input */
export type UpdateFinancialYearInput = {
  name?: string;
  startDate?: string;
  endDate?: string;
  isActive?: boolean;
};
