/**
 * Current User API Route
 * 
 * Endpoint to get current authenticated user information
 * with proper JWT validation and error handling.
 * 
 * Following DRY principle: Single user info endpoint
 * Following YAGNI principle: Only essential user data
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/jwt';
import { userRepository } from '@/database/repository';

/**
 * User response interface
 */
interface UserResponse {
  success: boolean;
  message: string;
  user?: {
    id: string;
    email: string;
    name: string;
    createdAt: string;
    updatedAt: string;
  };
}

/**
 * GET /api/auth/me
 * 
 * Get current authenticated user information
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Get current user from JWT token
    const currentUser = getCurrentUser(request);

    if (!currentUser) {
      return NextResponse.json(
        {
          success: false,
          message: 'Not authenticated',
        },
        { status: 401 }
      );
    }

    // Fetch fresh user data from database
    const user = await userRepository.findById(currentUser.userId);

    if (!user) {
      // User exists in token but not in database (shouldn't happen)
      return NextResponse.json(
        {
          success: false,
          message: 'User not found',
        },
        { status: 404 }
      );
    }

    // Return user data (excluding password hash)
    const response: UserResponse = {
      success: true,
      message: 'User information retrieved successfully',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
    };

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('Get current user API error:', error);

    return NextResponse.json(
      {
        success: false,
        message: 'An internal server error occurred',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/auth/me
 * 
 * Method not allowed for POST requests
 */
export async function POST(): Promise<NextResponse> {
  return NextResponse.json(
    {
      success: false,
      message: 'Method not allowed. Use GET to retrieve user information.',
    },
    { status: 405 }
  );
}

/**
 * OPTIONS /api/auth/me
 * 
 * Handle CORS preflight requests
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
