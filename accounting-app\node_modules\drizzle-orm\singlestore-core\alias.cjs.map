{"version": 3, "sources": ["../../src/singlestore-core/alias.ts"], "sourcesContent": ["import { TableAliasProxyHandler } from '~/alias.ts';\nimport type { BuildAliasTable } from './query-builders/select.types.ts';\nimport type { SingleStoreTable } from './table.ts';\n\nexport function alias<TTable extends SingleStoreTable, T<PERSON>lias extends string>( // | SingleStoreViewBase\n\ttable: TTable,\n\talias: T<PERSON><PERSON><PERSON>,\n): BuildAliasTable<TTable, TAlias> {\n\treturn new Proxy(table, new TableAliasProxyHandler(alias, false)) as any;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAuC;AAIhC,SAAS,MACf,OACAA,QACkC;AAClC,SAAO,IAAI,MAAM,OAAO,IAAI,oCAAuBA,QAAO,KAAK,CAAC;AACjE;", "names": ["alias"]}