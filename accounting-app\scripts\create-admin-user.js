const { createClient } = require('@supabase/supabase-js');

// For demo purposes, we'll use placeholder values
// In production, you would use your actual Supabase credentials
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createAdminUser() {
  try {
    console.log('Creating admin user...');
    
    // Create user with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: '123456',
      email_confirm: true,
      user_metadata: {
        name: '<PERSON> Ad<PERSON>'
      }
    });

    if (authError) {
      console.error('Error creating auth user:', authError);
      return;
    }

    console.log('✅ Admin user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: 123456');
    console.log('👤 Name: Ali Admin');
    console.log('🆔 User ID:', authData.user.id);

    // The user profile will be automatically created by the database trigger
    console.log('\n🎉 You can now login with these credentials!');
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Check if this is being run directly
if (require.main === module) {
  createAdminUser();
}

module.exports = { createAdminUser };
