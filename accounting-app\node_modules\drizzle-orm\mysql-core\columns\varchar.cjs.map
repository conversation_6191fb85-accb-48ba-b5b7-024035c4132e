{"version": 3, "sources": ["../../../src/mysql-core/columns/varchar.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { getColumnNameAndConfig, type Writable } from '~/utils.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlVarCharBuilderInitial<\n\tTName extends string,\n\tTEnum extends [string, ...string[]],\n\tTLength extends number | undefined,\n> = MySqlVarCharBuilder<\n\t{\n\t\tname: TName;\n\t\tdataType: 'string';\n\t\tcolumnType: 'MySqlVarChar';\n\t\tdata: TEnum[number];\n\t\tdriverParam: number | string;\n\t\tenumValues: TEnum;\n\t\tlength: TLength;\n\t}\n>;\n\nexport class MySqlVarCharBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'MySqlVarChar'> & { length?: number | undefined },\n> extends MySqlColumnBuilder<T, MySqlVarCharConfig<T['enumValues'], T['length']>> {\n\tstatic override readonly [entityKind]: string = 'MySqlVarCharBuilder';\n\n\t/** @internal */\n\tconstructor(name: T['name'], config: MySqlVarCharConfig<T['enumValues'], T['length']>) {\n\t\tsuper(name, 'string', 'MySqlVarChar');\n\t\tthis.config.length = config.length;\n\t\tthis.config.enum = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlVarChar<MakeColumnConfig<T, TTableName> & { length: T['length']; enumValues: T['enumValues'] }> {\n\t\treturn new MySqlVarChar<MakeColumnConfig<T, TTableName> & { length: T['length']; enumValues: T['enumValues'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlVarChar<T extends ColumnBaseConfig<'string', 'MySqlVarChar'> & { length?: number | undefined }>\n\textends MySqlColumn<T, MySqlVarCharConfig<T['enumValues'], T['length']>, { length: T['length'] }>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlVarChar';\n\n\treadonly length: number | undefined = this.config.length;\n\n\toverride readonly enumValues = this.config.enum;\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `varchar` : `varchar(${this.length})`;\n\t}\n}\n\nexport interface MySqlVarCharConfig<\n\tTEnum extends string[] | readonly string[] | undefined = string[] | readonly string[] | undefined,\n\tTLength extends number | undefined = number | undefined,\n> {\n\tenum?: TEnum;\n\tlength: TLength;\n}\n\nexport function varchar<U extends string, T extends Readonly<[U, ...U[]]>, L extends number | undefined>(\n\tconfig: MySqlVarCharConfig<T | Writable<T>, L>,\n): MySqlVarCharBuilderInitial<'', Writable<T>, L>;\nexport function varchar<\n\tTName extends string,\n\tU extends string,\n\tT extends Readonly<[U, ...U[]]>,\n\tL extends number | undefined,\n>(\n\tname: TName,\n\tconfig: MySqlVarCharConfig<T | Writable<T>, L>,\n): MySqlVarCharBuilderInitial<TName, Writable<T>, L>;\nexport function varchar(a?: string | MySqlVarCharConfig, b?: MySqlVarCharConfig): any {\n\tconst { name, config } = getColumnNameAndConfig<MySqlVarCharConfig>(a, b);\n\treturn new MySqlVarCharBuilder(name, config as any);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAsD;AACtD,oBAAgD;AAkBzC,MAAM,4BAEH,iCAAwE;AAAA,EACjF,QAA0B,wBAAU,IAAY;AAAA;AAAA,EAGhD,YAAY,MAAiB,QAA0D;AACtF,UAAM,MAAM,UAAU,cAAc;AACpC,SAAK,OAAO,SAAS,OAAO;AAC5B,SAAK,OAAO,OAAO,OAAO;AAAA,EAC3B;AAAA;AAAA,EAGS,MACR,OACuG;AACvG,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,qBACJ,0BACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,SAA6B,KAAK,OAAO;AAAA,EAEhC,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,YAAY,WAAW,KAAK,MAAM;AAAA,EACtE;AACD;AAsBO,SAAS,QAAQ,GAAiC,GAA6B;AACrF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA2C,GAAG,CAAC;AACxE,SAAO,IAAI,oBAAoB,MAAM,MAAa;AACnD;", "names": []}