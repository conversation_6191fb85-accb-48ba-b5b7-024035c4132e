/**
 * Company Repository
 *
 * This file provides company-specific database operations with Odoo-like features
 * including pagination, filtering, sorting, and search capabilities.
 *
 * Following DRY principle: Extends base repository
 * Following YAGNI principle: Only essential company operations
 */

import { BaseRepository, QueryOptions, FilterCondition } from './base';
import { companies, users, SelectCompany, InsertCompany, CompanyWithUser } from '../schemas';
import { eq, and, sql } from 'drizzle-orm';

/**
 * Company creation input
 */
export interface CreateCompanyInput {
  name: string;
  description?: string;
  userId: string;
}

/**
 * Company update input
 */
export interface UpdateCompanyInput {
  name?: string;
  description?: string;
}

/**
 * Company search and filter options
 */
export interface CompanyQueryOptions extends QueryOptions {
  search?: {
    query?: string;
    fields?: ('name' | 'description')[];
  };
  userId?: string; // Filter by user
}

/**
 * Company repository with business management features
 */
export class CompanyRepository extends BaseRepository<SelectCompany> {
  constructor() {
    super('companies', companies);
  }

  /**
   * Create a new company
   */
  async createCompany(companyData: CreateCompanyInput): Promise<SelectCompany> {
    // Generate company ID
    const companyId = `company_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Create company data
    const companyToCreate: InsertCompany = {
      id: companyId,
      name: companyData.name.trim(),
      description: companyData.description?.trim() || null,
      userId: companyData.userId,
    };

    // Insert company
    return await this.create(companyToCreate);
  }

  /**
   * Update company
   */
  async updateCompany(id: string, companyData: UpdateCompanyInput): Promise<SelectCompany | null> {
    const updateData: Partial<SelectCompany> = {};

    if (companyData.name) {
      updateData.name = companyData.name.trim();
    }

    if (companyData.description !== undefined) {
      updateData.description = companyData.description?.trim() || null;
    }

    // Add updated timestamp
    updateData.updatedAt = new Date().toISOString();

    return await this.update(id, updateData);
  }

  /**
   * Find companies by user ID
   */
  async findByUserId(userId: string, options: QueryOptions = {}) {
    const filters: FilterCondition[] = [
      { field: 'userId', operator: 'equals', value: userId },
      ...(options.filters || []),
    ];

    return await this.findAll({
      ...options,
      filters,
    });
  }

  /**
   * Find company with user information
   */
  async findWithUser(id: string): Promise<CompanyWithUser | null> {
    const db = await this.initDb();

    const result = await db.select({
      id: companies.id,
      name: companies.name,
      description: companies.description,
      userId: companies.userId,
      createdAt: companies.createdAt,
      updatedAt: companies.updatedAt,
      user: {
        id: users.id,
        name: users.name,
        email: users.email,
      },
    })
    .from(companies)
    .leftJoin(users, eq(companies.userId, users.id))
    .where(eq(companies.id, id))
    .limit(1);

    return result[0] || null;
  }

  /**
   * Search companies with enhanced options
   */
  async searchCompanies(options: CompanyQueryOptions = {}) {
    // Set default search fields if not provided
    if (options.search && !options.search.fields) {
      options.search.fields = ['name', 'description'];
    }

    // Add user filter if provided
    const filters = [...(options.filters || [])];
    if (options.userId) {
      filters.push({ field: 'userId', operator: 'equals', value: options.userId });
    }

    return await this.findAll({
      ...options,
      filters,
    });
  }

  /**
   * Get companies with user information
   */
  async getCompaniesWithUsers(options: CompanyQueryOptions = {}) {
    const db = await this.initDb();

    // Build filter conditions
    const filterConditions = options.filters ? this.buildFilterConditions(options.filters) : [];
    const searchConditions = options.search ? this.buildSearchConditions(options.search) : [];

    // Add user filter if provided
    if (options.userId) {
      filterConditions.push(eq(companies.userId, options.userId));
    }

    const allConditions = [...filterConditions, ...searchConditions];
    const whereClause = allConditions.length > 0 ? and(...allConditions) : undefined;

    // Build sort conditions
    const orderBy = options.sort ? this.buildSortConditions(options.sort) : [];

    // Calculate pagination
    const pagination = this.calculatePagination(options.pagination || {});

    // Get total count
    let countQuery = db.select({ count: sql`COUNT(*)` })
      .from(companies)
      .leftJoin(users, eq(companies.userId, users.id));

    if (whereClause) {
      countQuery = countQuery.where(whereClause);
    }

    const [{ count }] = await countQuery;
    const total = Number(count);

    // Get paginated data with user information
    let dataQuery = db.select({
      id: companies.id,
      name: companies.name,
      description: companies.description,
      userId: companies.userId,
      createdAt: companies.createdAt,
      updatedAt: companies.updatedAt,
      user: {
        id: users.id,
        name: users.name,
        email: users.email,
      },
    })
    .from(companies)
    .leftJoin(users, eq(companies.userId, users.id));

    if (whereClause) {
      dataQuery = dataQuery.where(whereClause);
    }

    if (orderBy.length > 0) {
      dataQuery = dataQuery.orderBy(...orderBy);
    }

    dataQuery = dataQuery.limit(pagination.limit).offset(pagination.offset);

    const data = await dataQuery;

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / pagination.pageSize);
    const hasNext = pagination.page < totalPages;
    const hasPrev = pagination.page > 1;

    return {
      data,
      pagination: {
        page: pagination.page,
        pageSize: pagination.pageSize,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
      filters: options.filters,
      sort: options.sort,
      search: options.search,
    };
  }

  /**
   * Get company statistics
   */
  async getCompanyStats(userId?: string): Promise<{
    totalCompanies: number;
    companiesCreatedToday: number;
    companiesCreatedThisWeek: number;
    companiesCreatedThisMonth: number;
    averageCompaniesPerUser: number;
  }> {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
    const monthAgo = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();

    const baseFilters: FilterCondition[] = [];
    if (userId) {
      baseFilters.push({ field: 'userId', operator: 'equals', value: userId });
    }

    const [
      totalCompanies,
      companiesCreatedToday,
      companiesCreatedThisWeek,
      companiesCreatedThisMonth,
    ] = await Promise.all([
      this.count(baseFilters),
      this.count([...baseFilters, { field: 'createdAt', operator: 'greater_than_or_equal', value: today }]),
      this.count([...baseFilters, { field: 'createdAt', operator: 'greater_than_or_equal', value: weekAgo }]),
      this.count([...baseFilters, { field: 'createdAt', operator: 'greater_than_or_equal', value: monthAgo }]),
    ]);

    // Calculate average companies per user
    const db = await this.initDb();
    const userCountResult = await db.select({ count: sql`COUNT(DISTINCT user_id)` }).from(companies);
    const uniqueUsers = Number(userCountResult[0].count);
    const averageCompaniesPerUser = uniqueUsers > 0 ? totalCompanies / uniqueUsers : 0;

    return {
      totalCompanies,
      companiesCreatedToday,
      companiesCreatedThisWeek,
      companiesCreatedThisMonth,
      averageCompaniesPerUser: Math.round(averageCompaniesPerUser * 100) / 100,
    };
  }

  /**
   * Get recently created companies
   */
  async getRecentCompanies(limit: number = 10, userId?: string) {
    const options: CompanyQueryOptions = {
      pagination: { pageSize: limit },
      sort: [{ field: 'createdAt', order: 'desc' }],
      userId,
    };

    const result = await this.getCompaniesWithUsers(options);
    return result.data;
  }

  /**
   * Check if company name is available for user
   */
  async isCompanyNameAvailable(name: string, userId: string, excludeCompanyId?: string): Promise<boolean> {
    const conditions: FilterCondition[] = [
      { field: 'name', operator: 'equals', value: name.trim() },
      { field: 'userId', operator: 'equals', value: userId },
    ];

    if (excludeCompanyId) {
      conditions.push({ field: 'id', operator: 'not_equals', value: excludeCompanyId });
    }

    const existingCompany = await this.findOne(conditions);
    return existingCompany === null;
  }

  /**
   * Get companies by name pattern
   */
  async findByNamePattern(pattern: string, userId?: string, options: QueryOptions = {}) {
    const filters: FilterCondition[] = [
      { field: 'name', operator: 'contains', value: pattern },
      ...(options.filters || []),
    ];

    if (userId) {
      filters.push({ field: 'userId', operator: 'equals', value: userId });
    }

    return await this.findAll({
      ...options,
      filters,
    });
  }

  /**
   * Bulk create companies
   */
  async bulkCreateCompanies(companiesData: CreateCompanyInput[]): Promise<SelectCompany[]> {
    const companiesToCreate: InsertCompany[] = [];

    for (const companyData of companiesData) {
      // Check if company name is available for user
      const isAvailable = await this.isCompanyNameAvailable(companyData.name, companyData.userId);
      if (!isAvailable) {
        throw new Error(`Company with name "${companyData.name}" already exists for this user`);
      }

      // Generate company ID
      const companyId = `company_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      companiesToCreate.push({
        id: companyId,
        name: companyData.name.trim(),
        description: companyData.description?.trim() || null,
        userId: companyData.userId,
      });
    }

    return await this.bulkCreate(companiesToCreate);
  }

  /**
   * Validate company data
   */
  validateCompanyData(companyData: CreateCompanyInput | UpdateCompanyInput): string[] {
    const errors: string[] = [];

    if ('name' in companyData && companyData.name) {
      if (companyData.name.trim().length < 1) {
        errors.push('Company name is required');
      }
      if (companyData.name.trim().length > 200) {
        errors.push('Company name must be less than 200 characters');
      }
    }

    if ('description' in companyData && companyData.description) {
      if (companyData.description.trim().length > 1000) {
        errors.push('Company description must be less than 1000 characters');
      }
    }

    return errors;
  }
}
