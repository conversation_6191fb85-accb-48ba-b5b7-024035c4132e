{"version": 3, "sources": ["../../src/sql-js/driver.ts"], "sourcesContent": ["import type { Database } from 'sql.js';\nimport { DefaultLogger } from '~/logger.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { BaseSQLiteDatabase } from '~/sqlite-core/db.ts';\nimport { SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { SQLJsSession } from './session.ts';\n\nexport type SQLJsDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> = BaseSQLiteDatabase<'sync', void, TSchema>;\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: Database,\n\tconfig: DrizzleConfig<TSchema> = {},\n): SQLJsDatabase<TSchema> {\n\tconst dialect = new SQLiteSyncDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new SQLJsSession(client, dialect, schema, { logger });\n\treturn new BaseSQLiteDatabase('sync', dialect, session, schema) as SQLJsDatabase<TSchema>;\n}\n"], "mappings": "AACA,SAAS,qBAAqB;AAC9B;AAAA,EACC;AAAA,EACA;AAAA,OAGM;AACP,SAAS,0BAA0B;AACnC,SAAS,yBAAyB;AAElC,SAAS,oBAAoB;AAMtB,SAAS,QACf,QACA,SAAiC,CAAC,GACT;AACzB,QAAM,UAAU,IAAI,kBAAkB,EAAE,QAAQ,OAAO,OAAO,CAAC;AAC/D,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,aAAa,QAAQ,SAAS,QAAQ,EAAE,OAAO,CAAC;AACpE,SAAO,IAAI,mBAAmB,QAAQ,SAAS,SAAS,MAAM;AAC/D;", "names": []}