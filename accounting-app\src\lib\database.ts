/**
 * Database Configuration and Connection using Drizzle ORM
 *
 * This module handles database connection and configuration using Drizzle ORM.
 * It provides type-safe database operations and proper ORM patterns.
 *
 * Following DRY principle: Single database configuration
 * Following YAGNI principle: Only essential database setup with proper ORM
 */

import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import { migrate } from 'drizzle-orm/better-sqlite3/migrator';
import path from 'path';
import fs from 'fs';
import * as schema from '@/db/schema';

/**
 * Database configuration interface
 */
interface DatabaseConfig {
  /** Path to the SQLite database file */
  path: string;
  /** Whether to enable WAL mode for better performance */
  enableWAL: boolean;
  /** Whether to enable foreign key constraints */
  enableForeignKeys: boolean;
  /** Connection timeout in milliseconds */
  timeout: number;
}

/**
 * Default database configuration
 * Following YAGNI: Only essential configuration options
 */
const DEFAULT_CONFIG: DatabaseConfig = {
  path: path.join(process.cwd(), 'data', 'accounting.db'),
  enableWAL: true,
  enableForeignKeys: true,
  timeout: 5000,
};

/**
 * Database connection instance
 * Singleton pattern to ensure single connection throughout the application
 */
let dbInstance: Database.Database | null = null;

/**
 * Initialize and return the database connection
 *
 * @param config - Optional database configuration
 * @returns Database instance
 *
 * Following DRY principle: Single initialization function
 */
export function getDatabase(config: Partial<DatabaseConfig> = {}): Database.Database {
  // Return existing instance if available (Singleton pattern)
  if (dbInstance) {
    return dbInstance;
  }

  // Merge default config with provided config
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  try {
    // Create database instance
    dbInstance = new Database(finalConfig.path, {
      timeout: finalConfig.timeout,
    });

    // Enable WAL mode for better performance
    if (finalConfig.enableWAL) {
      dbInstance.pragma('journal_mode = WAL');
    }

    // Enable foreign key constraints
    if (finalConfig.enableForeignKeys) {
      dbInstance.pragma('foreign_keys = ON');
    }

    // Set other performance optimizations
    dbInstance.pragma('synchronous = NORMAL');
    dbInstance.pragma('cache_size = 1000000');
    dbInstance.pragma('temp_store = memory');

    console.log('Database connected successfully');
    return dbInstance;
  } catch (error) {
    console.error('Failed to connect to database:', error);
    throw new Error('Database connection failed');
  }
}

/**
 * Close the database connection
 * Should be called when the application shuts down
 */
export function closeDatabase(): void {
  if (dbInstance) {
    try {
      dbInstance.close();
      dbInstance = null;
      console.log('Database connection closed');
    } catch (error) {
      console.error('Error closing database:', error);
    }
  }
}

/**
 * Initialize database schema
 * Creates all necessary tables if they don't exist
 *
 * Following DRY principle: Single schema initialization
 */
export function initializeSchema(): void {
  const db = getDatabase();

  // Begin transaction for schema creation
  const transaction = db.transaction(() => {
    // Users table
    db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        password_hash TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Companies table
    db.exec(`
      CREATE TABLE IF NOT EXISTS companies (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        user_id TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )
    `);

    // Financial Years table
    db.exec(`
      CREATE TABLE IF NOT EXISTS financial_years (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        is_active BOOLEAN DEFAULT 0,
        company_id TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE
      )
    `);

    // Account Types table
    db.exec(`
      CREATE TABLE IF NOT EXISTS account_types (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        category TEXT NOT NULL CHECK (category IN ('ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE')),
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Accounts table
    db.exec(`
      CREATE TABLE IF NOT EXISTS accounts (
        id TEXT PRIMARY KEY,
        code TEXT NOT NULL,
        name TEXT NOT NULL,
        account_type_id TEXT NOT NULL,
        parent_account_id TEXT,
        is_active BOOLEAN DEFAULT 1,
        company_id TEXT NOT NULL,
        financial_year_id TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_type_id) REFERENCES account_types (id),
        FOREIGN KEY (parent_account_id) REFERENCES accounts (id),
        FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE,
        FOREIGN KEY (financial_year_id) REFERENCES financial_years (id) ON DELETE CASCADE,
        UNIQUE (company_id, financial_year_id, code)
      )
    `);

    // Journal Entries table
    db.exec(`
      CREATE TABLE IF NOT EXISTS journal_entries (
        id TEXT PRIMARY KEY,
        entry_number TEXT NOT NULL,
        date DATE NOT NULL,
        description TEXT NOT NULL,
        reference TEXT,
        total_debit DECIMAL(15,2) NOT NULL DEFAULT 0,
        total_credit DECIMAL(15,2) NOT NULL DEFAULT 0,
        status TEXT NOT NULL DEFAULT 'DRAFT' CHECK (status IN ('DRAFT', 'POSTED', 'REVERSED')),
        company_id TEXT NOT NULL,
        financial_year_id TEXT NOT NULL,
        created_by TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE,
        FOREIGN KEY (financial_year_id) REFERENCES financial_years (id) ON DELETE CASCADE,
        FOREIGN KEY (created_by) REFERENCES users (id),
        UNIQUE (company_id, financial_year_id, entry_number)
      )
    `);

    // Journal Entry Lines table
    db.exec(`
      CREATE TABLE IF NOT EXISTS journal_entry_lines (
        id TEXT PRIMARY KEY,
        journal_entry_id TEXT NOT NULL,
        account_id TEXT NOT NULL,
        description TEXT,
        debit_amount DECIMAL(15,2) DEFAULT 0,
        credit_amount DECIMAL(15,2) DEFAULT 0,
        line_number INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (journal_entry_id) REFERENCES journal_entries (id) ON DELETE CASCADE,
        FOREIGN KEY (account_id) REFERENCES accounts (id),
        UNIQUE (journal_entry_id, line_number)
      )
    `);

    // Create indexes for better performance
    db.exec(`
      CREATE INDEX IF NOT EXISTS idx_companies_user_id ON companies (user_id);
      CREATE INDEX IF NOT EXISTS idx_financial_years_company_id ON financial_years (company_id);
      CREATE INDEX IF NOT EXISTS idx_accounts_company_id ON accounts (company_id);
      CREATE INDEX IF NOT EXISTS idx_accounts_financial_year_id ON accounts (financial_year_id);
      CREATE INDEX IF NOT EXISTS idx_journal_entries_company_id ON journal_entries (company_id);
      CREATE INDEX IF NOT EXISTS idx_journal_entries_financial_year_id ON journal_entries (financial_year_id);
      CREATE INDEX IF NOT EXISTS idx_journal_entry_lines_journal_entry_id ON journal_entry_lines (journal_entry_id);
    `);

    // Insert default account types
    const insertAccountType = db.prepare(`
      INSERT OR IGNORE INTO account_types (id, name, category, description)
      VALUES (?, ?, ?, ?)
    `);

    const defaultAccountTypes = [
      ['current-assets', 'Current Assets', 'ASSET', 'Assets that can be converted to cash within one year'],
      ['fixed-assets', 'Fixed Assets', 'ASSET', 'Long-term assets used in business operations'],
      ['current-liabilities', 'Current Liabilities', 'LIABILITY', 'Debts due within one year'],
      ['long-term-liabilities', 'Long-term Liabilities', 'LIABILITY', 'Debts due after one year'],
      ['owners-equity', 'Owner\'s Equity', 'EQUITY', 'Owner\'s stake in the business'],
      ['revenue', 'Revenue', 'REVENUE', 'Income from business operations'],
      ['operating-expenses', 'Operating Expenses', 'EXPENSE', 'Costs of running the business'],
      ['other-income', 'Other Income', 'REVENUE', 'Non-operating income'],
      ['other-expenses', 'Other Expenses', 'EXPENSE', 'Non-operating expenses'],
    ];

    defaultAccountTypes.forEach(([id, name, category, description]) => {
      insertAccountType.run(id, name, category, description);
    });
  });

  // Execute the transaction
  transaction();
  console.log('Database schema initialized successfully');
}

/**
 * Create a sample admin user for testing
 * Following YAGNI: Only create what's needed for initial setup
 */
export function createAdminUser(): void {
  const db = getDatabase();
  const bcrypt = require('bcryptjs');

  try {
    // Check if admin user already exists
    const existingUser = db.prepare('SELECT id FROM users WHERE email = ?').get('<EMAIL>');

    if (!existingUser) {
      // Hash the password
      const passwordHash = bcrypt.hashSync('123456', 10);

      // Insert admin user
      const insertUser = db.prepare(`
        INSERT INTO users (id, email, name, password_hash)
        VALUES (?, ?, ?, ?)
      `);

      insertUser.run('admin-user-1', '<EMAIL>', 'Ali Admin', passwordHash);
      console.log('Admin user created successfully');
      console.log('Email: <EMAIL>');
      console.log('Password: 123456');
    } else {
      console.log('Admin user already exists');
    }
  } catch (error) {
    console.error('Error creating admin user:', error);
  }
}

/**
 * Export the database instance getter as default
 * Following DRY principle: Single export point
 */
export default getDatabase;
