{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Box, CircularProgress } from '@mui/material';\n\nexport default function Home() {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!loading) {\n      if (user) {\n        router.push('/dashboard');\n      } else {\n        router.push('/auth/login');\n      }\n    }\n  }, [user, loading, router]);\n\n  return (\n    <Box\n      sx={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      }}\n    >\n      <CircularProgress size={60} sx={{ color: '#fff' }} />\n    </Box>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;YACZ,IAAI,MAAM;gBACR,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,qBACE,8OAAC,wLAAA,CAAA,MAAG;QACF,IAAI;YACF,SAAS;YACT,gBAAgB;YAChB,YAAY;YACZ,WAAW;YACX,YAAY;QACd;kBAEA,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;YAAC,MAAM;YAAI,IAAI;gBAAE,OAAO;YAAO;;;;;;;;;;;AAGtD", "debugId": null}}]}