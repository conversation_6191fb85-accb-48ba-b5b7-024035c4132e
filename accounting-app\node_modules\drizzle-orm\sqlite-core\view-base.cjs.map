{"version": 3, "sources": ["../../src/sqlite-core/view-base.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { ColumnsSelection } from '~/sql/sql.ts';\nimport { View } from '~/sql/sql.ts';\n\nexport abstract class SQLiteViewBase<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelection extends ColumnsSelection = ColumnsSelection,\n> extends View<TName, TExisting, TSelection> {\n\tstatic override readonly [entityKind]: string = 'SQLiteViewBase';\n\n\tdeclare _: View<TName, TExisting, TSelection>['_'] & {\n\t\tviewBrand: 'SQLiteView';\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAE3B,iBAAqB;AAEd,MAAe,uBAIZ,gBAAmC;AAAA,EAC5C,QAA0B,wBAAU,IAAY;AAKjD;", "names": []}