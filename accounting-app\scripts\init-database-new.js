/**
 * Database Initialization Script (New ORM Version)
 * 
 * This script initializes the database using the new database-agnostic ORM structure.
 * It supports SQLite, MySQL, and PostgreSQL databases with automatic migration.
 * 
 * Following DRY principle: Uses migration system
 * Following YAGNI principle: Essential initialization only
 */

const path = require('path');
const fs = require('fs');

/**
 * Simple database initialization using direct operations
 * This avoids TypeScript compilation issues in Node.js scripts
 */
async function initializeDatabase() {
  try {
    console.log('🚀 Starting database initialization...');
    console.log('');

    // Import required modules
    const bcrypt = require('bcryptjs');
    const Database = require('better-sqlite3');

    // Ensure data directory exists
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
      console.log('📁 Created data directory');
    }

    // Database path
    const dbPath = path.join(dataDir, 'accounting.db');

    // Connect to database
    console.log('🔌 Connecting to database...');
    const db = new Database(dbPath);

    // Configure database
    db.pragma('journal_mode = WAL');
    db.pragma('foreign_keys = ON');
    db.pragma('synchronous = NORMAL');
    db.pragma('cache_size = 1000000');
    db.pragma('temp_store = memory');

    console.log('✅ Database connected successfully');

    // Create schema
    console.log('📋 Creating database schema...');
    
    // Begin transaction for schema creation
    const transaction = db.transaction(() => {
      // Users table
      db.exec(`
        CREATE TABLE IF NOT EXISTS users (
          id TEXT PRIMARY KEY,
          email TEXT UNIQUE NOT NULL,
          name TEXT NOT NULL,
          password_hash TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Companies table
      db.exec(`
        CREATE TABLE IF NOT EXISTS companies (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT,
          user_id TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
      `);

      // Financial Years table
      db.exec(`
        CREATE TABLE IF NOT EXISTS financial_years (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          start_date TEXT NOT NULL,
          end_date TEXT NOT NULL,
          is_active BOOLEAN DEFAULT FALSE,
          company_id TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE
        )
      `);

      // Account Types table
      db.exec(`
        CREATE TABLE IF NOT EXISTS account_types (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          category TEXT NOT NULL,
          description TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Accounts table
      db.exec(`
        CREATE TABLE IF NOT EXISTS accounts (
          id TEXT PRIMARY KEY,
          code TEXT NOT NULL,
          name TEXT NOT NULL,
          account_type_id TEXT NOT NULL,
          parent_account_id TEXT,
          is_active BOOLEAN DEFAULT TRUE,
          company_id TEXT NOT NULL,
          financial_year_id TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (account_type_id) REFERENCES account_types (id),
          FOREIGN KEY (parent_account_id) REFERENCES accounts (id),
          FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE,
          FOREIGN KEY (financial_year_id) REFERENCES financial_years (id) ON DELETE CASCADE
        )
      `);

      // Journal Entries table
      db.exec(`
        CREATE TABLE IF NOT EXISTS journal_entries (
          id TEXT PRIMARY KEY,
          entry_number TEXT NOT NULL,
          date TEXT NOT NULL,
          description TEXT NOT NULL,
          reference TEXT,
          total_debit REAL DEFAULT 0,
          total_credit REAL DEFAULT 0,
          status TEXT DEFAULT 'DRAFT',
          company_id TEXT NOT NULL,
          financial_year_id TEXT NOT NULL,
          created_by TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE,
          FOREIGN KEY (financial_year_id) REFERENCES financial_years (id) ON DELETE CASCADE,
          FOREIGN KEY (created_by) REFERENCES users (id)
        )
      `);

      // Journal Entry Lines table
      db.exec(`
        CREATE TABLE IF NOT EXISTS journal_entry_lines (
          id TEXT PRIMARY KEY,
          journal_entry_id TEXT NOT NULL,
          account_id TEXT NOT NULL,
          description TEXT,
          debit_amount REAL DEFAULT 0,
          credit_amount REAL DEFAULT 0,
          line_number INTEGER NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (journal_entry_id) REFERENCES journal_entries (id) ON DELETE CASCADE,
          FOREIGN KEY (account_id) REFERENCES accounts (id)
        )
      `);

      // Create indexes for better performance
      db.exec(`
        CREATE INDEX IF NOT EXISTS idx_companies_user_id ON companies (user_id);
        CREATE INDEX IF NOT EXISTS idx_financial_years_company_id ON financial_years (company_id);
        CREATE INDEX IF NOT EXISTS idx_accounts_company_id ON accounts (company_id);
        CREATE INDEX IF NOT EXISTS idx_accounts_financial_year_id ON accounts (financial_year_id);
        CREATE INDEX IF NOT EXISTS idx_journal_entries_company_id ON journal_entries (company_id);
        CREATE INDEX IF NOT EXISTS idx_journal_entries_financial_year_id ON journal_entries (financial_year_id);
        CREATE INDEX IF NOT EXISTS idx_journal_entry_lines_journal_entry_id ON journal_entry_lines (journal_entry_id);
        CREATE INDEX IF NOT EXISTS idx_users_email ON users (email);
        CREATE INDEX IF NOT EXISTS idx_accounts_code ON accounts (code);
        CREATE INDEX IF NOT EXISTS idx_journal_entries_date ON journal_entries (date);
      `);

      // Insert default account types
      const insertAccountType = db.prepare(`
        INSERT OR IGNORE INTO account_types (id, name, category, description)
        VALUES (?, ?, ?, ?)
      `);

      const defaultAccountTypes = [
        ['current-assets', 'Current Assets', 'ASSET', 'Assets that can be converted to cash within one year'],
        ['fixed-assets', 'Fixed Assets', 'ASSET', 'Long-term assets used in business operations'],
        ['current-liabilities', 'Current Liabilities', 'LIABILITY', 'Debts due within one year'],
        ['long-term-liabilities', 'Long-term Liabilities', 'LIABILITY', 'Debts due after one year'],
        ['owners-equity', 'Owner\'s Equity', 'EQUITY', 'Owner\'s stake in the business'],
        ['revenue', 'Revenue', 'REVENUE', 'Income from business operations'],
        ['operating-expenses', 'Operating Expenses', 'EXPENSE', 'Costs of running the business'],
        ['other-income', 'Other Income', 'REVENUE', 'Non-operating income'],
        ['other-expenses', 'Other Expenses', 'EXPENSE', 'Non-operating expenses'],
      ];

      defaultAccountTypes.forEach(([id, name, category, description]) => {
        insertAccountType.run(id, name, category, description);
      });
    });

    // Execute the transaction
    transaction();
    console.log('✅ Database schema created successfully');

    // Create default admin user
    console.log('👤 Creating default admin user...');
    
    // Check if admin user already exists
    const existingUser = db.prepare('SELECT id FROM users WHERE email = ?').get('<EMAIL>');

    if (!existingUser) {
      // Hash the password
      const passwordHash = await bcrypt.hash('123456', 10);
      const userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Insert admin user
      const insertUser = db.prepare(`
        INSERT INTO users (id, email, name, password_hash)
        VALUES (?, ?, ?, ?)
      `);

      insertUser.run(userId, '<EMAIL>', 'Ali Admin', passwordHash);
      console.log('✅ Default admin user created');
    } else {
      console.log('ℹ️  Default admin user already exists');
    }

    // Close database
    db.close();
    console.log('🔌 Database connection closed');

    console.log('🎉 Database initialization completed successfully!');
    console.log('');
    console.log('📧 Admin Login Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: 123456');
    console.log('');
    console.log('🚀 You can now start the development server with: npm run dev');

  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    throw error;
  }
}

/**
 * Main function
 */
async function main() {
  try {
    await initializeDatabase();
  } catch (error) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  }
}

// Run initialization if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = { initializeDatabase };
