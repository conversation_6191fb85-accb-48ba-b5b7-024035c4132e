{"name": "pg", "version": "8.16.0", "description": "PostgreSQL client - pure javascript & libpq with the same API", "keywords": ["database", "libpq", "pg", "postgre", "postgres", "postgresql", "rdbms"], "homepage": "https://github.com/brianc/node-postgres", "repository": {"type": "git", "url": "git://github.com/brianc/node-postgres.git", "directory": "packages/pg"}, "author": "<PERSON> <brian.m.car<PERSON>@gmail.com>", "main": "./lib", "exports": {".": {"import": "./esm/index.mjs", "require": "./lib/index.js", "default": "./lib/index.js"}, "./lib/*": {"import": "./lib/*", "require": "./lib/*", "default": "./lib/*"}}, "dependencies": {"pg-connection-string": "^2.9.0", "pg-pool": "^3.10.0", "pg-protocol": "^1.10.0", "pg-types": "2.2.0", "pgpass": "1.0.5"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "0.8.23", "@cloudflare/workers-types": "^4.20230404.0", "async": "2.6.4", "bluebird": "3.7.2", "co": "4.6.0", "pg-copy-streams": "0.3.0", "typescript": "^4.0.3", "vitest": "~3.0.9", "wrangler": "^3.x"}, "optionalDependencies": {"pg-cloudflare": "^1.2.5"}, "peerDependencies": {"pg-native": ">=3.0.1"}, "peerDependenciesMeta": {"pg-native": {"optional": true}}, "scripts": {"test": "make test-all"}, "files": ["lib", "esm", "SPONSORS.md"], "license": "MIT", "engines": {"node": ">= 8.0.0"}, "gitHead": "abff18d6f918c975e8b3dfebc0de3b811ae64bcb"}