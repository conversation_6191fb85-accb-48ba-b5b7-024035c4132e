{"version": 3, "sources": ["../../src/sqlite-proxy/session.ts"], "sourcesContent": ["import type { BatchItem } from '~/batch.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport { fillPlaceholders, type Query, sql } from '~/sql/sql.ts';\nimport type { SQLiteAsyncDialect } from '~/sqlite-core/dialect.ts';\nimport { SQLiteTransaction } from '~/sqlite-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/sqlite-core/query-builders/select.types.ts';\nimport type {\n\tPreparedQueryConfig as PreparedQueryConfigBase,\n\tSQLiteExecuteMethod,\n\tSQLiteTransactionConfig,\n} from '~/sqlite-core/session.ts';\nimport { SQLitePreparedQuery, SQLiteSession } from '~/sqlite-core/session.ts';\nimport { mapResultRow } from '~/utils.ts';\nimport type { AsyncBatchRemoteCallback, AsyncRemoteCallback, RemoteCallback, SqliteRemoteResult } from './driver.ts';\n\nexport interface SQLiteRemoteSessionOptions {\n\tlogger?: Logger;\n}\n\nexport type PreparedQueryConfig = Omit<PreparedQueryConfigBase, 'statement' | 'run'>;\n\nexport class SQLiteRemoteSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SQLiteSession<'async', SqliteRemoteResult, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'SQLiteRemoteSession';\n\n\tprivate logger: Logger;\n\n\tconstructor(\n\t\tprivate client: RemoteCallback,\n\t\tdialect: SQLiteAsyncDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate batchCLient?: AsyncBatchRemoteCallback,\n\t\toptions: SQLiteRemoteSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends Omit<PreparedQueryConfig, 'run'>>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => unknown,\n\t): RemotePreparedQuery<T> {\n\t\treturn new RemotePreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\texecuteMethod,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\tasync batch<T extends BatchItem<'sqlite'>[] | readonly BatchItem<'sqlite'>[]>(queries: T) {\n\t\tconst preparedQueries: PreparedQuery[] = [];\n\t\tconst builtQueries: { sql: string; params: any[]; method: 'run' | 'all' | 'values' | 'get' }[] = [];\n\n\t\tfor (const query of queries) {\n\t\t\tconst preparedQuery = query._prepare();\n\t\t\tconst builtQuery = (preparedQuery as RemotePreparedQuery).getQuery();\n\t\t\tpreparedQueries.push(preparedQuery);\n\t\t\tbuiltQueries.push({ sql: builtQuery.sql, params: builtQuery.params, method: builtQuery.method });\n\t\t}\n\n\t\tconst batchResults = await (this.batchCLient as AsyncBatchRemoteCallback)(builtQueries);\n\t\treturn batchResults.map((result, i) => preparedQueries[i]!.mapResult(result, true));\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: SQLiteProxyTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: SQLiteTransactionConfig,\n\t): Promise<T> {\n\t\tconst tx = new SQLiteProxyTransaction('async', this.dialect, this, this.schema);\n\t\tawait this.run(sql.raw(`begin${config?.behavior ? ' ' + config.behavior : ''}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait this.run(sql`commit`);\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait this.run(sql`rollback`);\n\t\t\tthrow err;\n\t\t}\n\t}\n\n\toverride extractRawAllValueFromBatchResult(result: unknown): unknown {\n\t\treturn (result as SqliteRemoteResult).rows;\n\t}\n\n\toverride extractRawGetValueFromBatchResult(result: unknown): unknown {\n\t\treturn (result as SqliteRemoteResult).rows![0];\n\t}\n\n\toverride extractRawValuesValueFromBatchResult(result: unknown): unknown {\n\t\treturn (result as SqliteRemoteResult).rows;\n\t}\n}\n\nexport class SQLiteProxyTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SQLiteTransaction<'async', SqliteRemoteResult, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'SQLiteProxyTransaction';\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: SQLiteProxyTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\tconst savepointName = `sp${this.nestedIndex}`;\n\t\tconst tx = new SQLiteProxyTransaction('async', this.dialect, this.session, this.schema, this.nestedIndex + 1);\n\t\tawait this.session.run(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait this.session.run(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait this.session.run(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport class RemotePreparedQuery<T extends PreparedQueryConfig = PreparedQueryConfig> extends SQLitePreparedQuery<\n\t{ type: 'async'; run: SqliteRemoteResult; all: T['all']; get: T['get']; values: T['values']; execute: T['execute'] }\n> {\n\tstatic override readonly [entityKind]: string = 'SQLiteProxyPreparedQuery';\n\n\tprivate method: SQLiteExecuteMethod;\n\n\tconstructor(\n\t\tprivate client: RemoteCallback,\n\t\tquery: Query,\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\t/** @internal */ public customResultMapper?: (\n\t\t\trows: unknown[][],\n\t\t\tmapColumnValue?: (value: unknown) => unknown,\n\t\t) => unknown,\n\t) {\n\t\tsuper('async', executeMethod, query);\n\t\tthis.customResultMapper = customResultMapper;\n\t\tthis.method = executeMethod;\n\t}\n\n\toverride getQuery(): Query & { method: SQLiteExecuteMethod } {\n\t\treturn { ...this.query, method: this.method };\n\t}\n\n\trun(placeholderValues?: Record<string, unknown>): Promise<SqliteRemoteResult> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn (this.client as AsyncRemoteCallback)(this.query.sql, params, 'run') as Promise<\n\t\t\tSqliteRemoteResult\n\t\t>;\n\t}\n\n\toverride mapAllResult(rows: unknown, isFromBatch?: boolean): unknown {\n\t\tif (isFromBatch) {\n\t\t\trows = (rows as SqliteRemoteResult).rows;\n\t\t}\n\n\t\tif (!this.fields && !this.customResultMapper) {\n\t\t\treturn rows;\n\t\t}\n\n\t\tif (this.customResultMapper) {\n\t\t\treturn this.customResultMapper(rows as unknown[][]) as T['all'];\n\t\t}\n\n\t\treturn (rows as unknown[][]).map((row) => {\n\t\t\treturn mapResultRow(\n\t\t\t\tthis.fields!,\n\t\t\t\trow,\n\t\t\t\tthis.joinsNotNullableMap,\n\t\t\t);\n\t\t});\n\t}\n\n\tasync all(placeholderValues?: Record<string, unknown>): Promise<T['all']> {\n\t\tconst { query, logger, client } = this;\n\n\t\tconst params = fillPlaceholders(query.params, placeholderValues ?? {});\n\t\tlogger.logQuery(query.sql, params);\n\n\t\tconst { rows } = await (client as AsyncRemoteCallback)(query.sql, params, 'all');\n\t\treturn this.mapAllResult(rows);\n\t}\n\n\tasync get(placeholderValues?: Record<string, unknown>): Promise<T['get']> {\n\t\tconst { query, logger, client } = this;\n\n\t\tconst params = fillPlaceholders(query.params, placeholderValues ?? {});\n\t\tlogger.logQuery(query.sql, params);\n\n\t\tconst clientResult = await (client as AsyncRemoteCallback)(query.sql, params, 'get');\n\n\t\treturn this.mapGetResult(clientResult.rows);\n\t}\n\n\toverride mapGetResult(rows: unknown, isFromBatch?: boolean): unknown {\n\t\tif (isFromBatch) {\n\t\t\trows = (rows as SqliteRemoteResult).rows;\n\t\t}\n\n\t\tconst row = rows as unknown[];\n\n\t\tif (!this.fields && !this.customResultMapper) {\n\t\t\treturn row;\n\t\t}\n\n\t\tif (!row) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (this.customResultMapper) {\n\t\t\treturn this.customResultMapper([rows] as unknown[][]) as T['get'];\n\t\t}\n\n\t\treturn mapResultRow(\n\t\t\tthis.fields!,\n\t\t\trow,\n\t\t\tthis.joinsNotNullableMap,\n\t\t);\n\t}\n\n\tasync values<T extends any[] = unknown[]>(placeholderValues?: Record<string, unknown>): Promise<T[]> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\tconst clientResult = await (this.client as AsyncRemoteCallback)(this.query.sql, params, 'values');\n\t\treturn clientResult.rows as T[];\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n"], "mappings": "AACA,SAAS,kBAAkB;AAE3B,SAAS,kBAAkB;AAG3B,SAAS,kBAA8B,WAAW;AAElD,SAAS,yBAAyB;AAOlC,SAAS,qBAAqB,qBAAqB;AACnD,SAAS,oBAAoB;AAStB,MAAM,4BAGH,cAAiE;AAAA,EAK1E,YACS,QACR,SACQ,QACA,aACR,UAAsC,CAAC,GACtC;AACD,UAAM,OAAO;AANL;AAEA;AACA;AAIR,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;AAAA,EAChD;AAAA,EAbA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EAaR,aACC,OACA,QACA,eACA,uBACA,oBACyB;AACzB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,MAAwE,SAAY;AACzF,UAAM,kBAAmC,CAAC;AAC1C,UAAM,eAA2F,CAAC;AAElG,eAAW,SAAS,SAAS;AAC5B,YAAM,gBAAgB,MAAM,SAAS;AACrC,YAAM,aAAc,cAAsC,SAAS;AACnE,sBAAgB,KAAK,aAAa;AAClC,mBAAa,KAAK,EAAE,KAAK,WAAW,KAAK,QAAQ,WAAW,QAAQ,QAAQ,WAAW,OAAO,CAAC;AAAA,IAChG;AAEA,UAAM,eAAe,MAAO,KAAK,YAAyC,YAAY;AACtF,WAAO,aAAa,IAAI,CAAC,QAAQ,MAAM,gBAAgB,CAAC,EAAG,UAAU,QAAQ,IAAI,CAAC;AAAA,EACnF;AAAA,EAEA,MAAe,YACd,aACA,QACa;AACb,UAAM,KAAK,IAAI,uBAAuB,SAAS,KAAK,SAAS,MAAM,KAAK,MAAM;AAC9E,UAAM,KAAK,IAAI,IAAI,IAAI,QAAQ,QAAQ,WAAW,MAAM,OAAO,WAAW,EAAE,EAAE,CAAC;AAC/E,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,KAAK,IAAI,WAAW;AAC1B,aAAO;AAAA,IACR,SAAS,KAAK;AACb,YAAM,KAAK,IAAI,aAAa;AAC5B,YAAM;AAAA,IACP;AAAA,EACD;AAAA,EAES,kCAAkC,QAA0B;AACpE,WAAQ,OAA8B;AAAA,EACvC;AAAA,EAES,kCAAkC,QAA0B;AACpE,WAAQ,OAA8B,KAAM,CAAC;AAAA,EAC9C;AAAA,EAES,qCAAqC,QAA0B;AACvE,WAAQ,OAA8B;AAAA,EACvC;AACD;AAEO,MAAM,+BAGH,kBAAqE;AAAA,EAC9E,QAA0B,UAAU,IAAY;AAAA,EAEhD,MAAe,YACd,aACa;AACb,UAAM,gBAAgB,KAAK,KAAK,WAAW;AAC3C,UAAM,KAAK,IAAI,uBAAuB,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,cAAc,CAAC;AAC5G,UAAM,KAAK,QAAQ,IAAI,IAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AAC5D,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,KAAK,QAAQ,IAAI,IAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AACpE,aAAO;AAAA,IACR,SAAS,KAAK;AACb,YAAM,KAAK,QAAQ,IAAI,IAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AACxE,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEO,MAAM,4BAAiF,oBAE5F;AAAA,EAKD,YACS,QACR,OACQ,QACA,QACR,eACQ,wBACgB,oBAIvB;AACD,UAAM,SAAS,eAAe,KAAK;AAX3B;AAEA;AACA;AAEA;AACgB;AAMxB,SAAK,qBAAqB;AAC1B,SAAK,SAAS;AAAA,EACf;AAAA,EAnBA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EAmBC,WAAoD;AAC5D,WAAO,EAAE,GAAG,KAAK,OAAO,QAAQ,KAAK,OAAO;AAAA,EAC7C;AAAA,EAEA,IAAI,mBAA0E;AAC7E,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAQ,KAAK,OAA+B,KAAK,MAAM,KAAK,QAAQ,KAAK;AAAA,EAG1E;AAAA,EAES,aAAa,MAAe,aAAgC;AACpE,QAAI,aAAa;AAChB,aAAQ,KAA4B;AAAA,IACrC;AAEA,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,oBAAoB;AAC7C,aAAO;AAAA,IACR;AAEA,QAAI,KAAK,oBAAoB;AAC5B,aAAO,KAAK,mBAAmB,IAAmB;AAAA,IACnD;AAEA,WAAQ,KAAqB,IAAI,CAAC,QAAQ;AACzC,aAAO;AAAA,QACN,KAAK;AAAA,QACL;AAAA,QACA,KAAK;AAAA,MACN;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,IAAI,mBAAgE;AACzE,UAAM,EAAE,OAAO,QAAQ,OAAO,IAAI;AAElC,UAAM,SAAS,iBAAiB,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AACrE,WAAO,SAAS,MAAM,KAAK,MAAM;AAEjC,UAAM,EAAE,KAAK,IAAI,MAAO,OAA+B,MAAM,KAAK,QAAQ,KAAK;AAC/E,WAAO,KAAK,aAAa,IAAI;AAAA,EAC9B;AAAA,EAEA,MAAM,IAAI,mBAAgE;AACzE,UAAM,EAAE,OAAO,QAAQ,OAAO,IAAI;AAElC,UAAM,SAAS,iBAAiB,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AACrE,WAAO,SAAS,MAAM,KAAK,MAAM;AAEjC,UAAM,eAAe,MAAO,OAA+B,MAAM,KAAK,QAAQ,KAAK;AAEnF,WAAO,KAAK,aAAa,aAAa,IAAI;AAAA,EAC3C;AAAA,EAES,aAAa,MAAe,aAAgC;AACpE,QAAI,aAAa;AAChB,aAAQ,KAA4B;AAAA,IACrC;AAEA,UAAM,MAAM;AAEZ,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,oBAAoB;AAC7C,aAAO;AAAA,IACR;AAEA,QAAI,CAAC,KAAK;AACT,aAAO;AAAA,IACR;AAEA,QAAI,KAAK,oBAAoB;AAC5B,aAAO,KAAK,mBAAmB,CAAC,IAAI,CAAgB;AAAA,IACrD;AAEA,WAAO;AAAA,MACN,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AAAA,EAEA,MAAM,OAAoC,mBAA2D;AACpG,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,UAAM,eAAe,MAAO,KAAK,OAA+B,KAAK,MAAM,KAAK,QAAQ,QAAQ;AAChG,WAAO,aAAa;AAAA,EACrB;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;", "names": []}