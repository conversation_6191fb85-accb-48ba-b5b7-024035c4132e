{"version": 3, "sources": ["../../../src/singlestore-core/columns/boolean.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { SingleStoreColumn, SingleStoreColumnBuilder } from './common.ts';\n\nexport type SingleStoreBooleanBuilderInitial<TName extends string> = SingleStoreBooleanBuilder<{\n\tname: TName;\n\tdataType: 'boolean';\n\tcolumnType: 'SingleStoreBoolean';\n\tdata: boolean;\n\tdriverParam: number | boolean;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreBooleanBuilder<T extends ColumnBuilderBaseConfig<'boolean', 'SingleStoreBoolean'>>\n\textends SingleStoreColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreBooleanBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'boolean', 'SingleStoreBoolean');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreBoolean<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreBoolean<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreBoolean<T extends ColumnBaseConfig<'boolean', 'SingleStoreBoolean'>>\n\textends SingleStoreColumn<T>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreBoolean';\n\n\tgetSQLType(): string {\n\t\treturn 'boolean';\n\t}\n\n\toverride mapFromDriverValue(value: number | boolean): boolean {\n\t\tif (typeof value === 'boolean') {\n\t\t\treturn value;\n\t\t}\n\t\treturn value === 1;\n\t}\n}\n\nexport function boolean(): SingleStoreBooleanBuilderInitial<''>;\nexport function boolean<TName extends string>(name: TName): SingleStoreBooleanBuilderInitial<TName>;\nexport function boolean(name?: string) {\n\treturn new SingleStoreBooleanBuilder(name ?? '');\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,mBAAmB,gCAAgC;AAYrD,MAAM,kCACJ,yBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,WAAW,oBAAoB;AAAA,EAC5C;AAAA;AAAA,EAGS,MACR,OACsD;AACtD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,2BACJ,kBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAkC;AAC7D,QAAI,OAAO,UAAU,WAAW;AAC/B,aAAO;AAAA,IACR;AACA,WAAO,UAAU;AAAA,EAClB;AACD;AAIO,SAAS,QAAQ,MAAe;AACtC,SAAO,IAAI,0BAA0B,QAAQ,EAAE;AAChD;", "names": []}