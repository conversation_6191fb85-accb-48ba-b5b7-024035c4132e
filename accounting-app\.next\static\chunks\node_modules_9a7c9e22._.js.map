{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/clsx/dist/clsx.mjs"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "names": [], "mappings": ";;;;AAAA,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE,IAAE;IAAG,IAAG,YAAU,OAAO,KAAG,YAAU,OAAO,GAAE,KAAG;SAAO,IAAG,YAAU,OAAO,GAAE,IAAG,MAAM,OAAO,CAAC,IAAG;QAAC,IAAI,IAAE,EAAE,MAAM;QAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAC,OAAM,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;AAAQ,SAAS;IAAO,IAAI,IAAI,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,MAAM,EAAC,IAAE,GAAE,IAAI,CAAC,IAAE,SAAS,CAAC,EAAE,KAAG,CAAC,IAAE,EAAE,EAAE,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;uCAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40emotion/is-prop-valid/dist/emotion-is-prop-valid.esm.js"], "sourcesContent": ["import memoize from '@emotion/memoize';\n\n// eslint-disable-next-line no-undef\nvar reactPropsRegex = /^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/; // https://esbench.com/bench/5bfee68a4cd7e6009ef61d23\n\nvar isPropValid = /* #__PURE__ */memoize(function (prop) {\n  return reactPropsRegex.test(prop) || prop.charCodeAt(0) === 111\n  /* o */\n  && prop.charCodeAt(1) === 110\n  /* n */\n  && prop.charCodeAt(2) < 91;\n}\n/* Z+1 */\n);\n\nexport { isPropValid as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,oCAAoC;AACpC,IAAI,kBAAkB,ugIAAugI,qDAAqD;AAEllI,IAAI,cAAc,aAAa,GAAE,CAAA,GAAA,4KAAA,CAAA,UAAO,AAAD,EAAE,SAAU,IAAI;IACrD,OAAO,gBAAgB,IAAI,CAAC,SAAS,KAAK,UAAU,CAAC,OAAO,OAEzD,KAAK,UAAU,CAAC,OAAO,OAEvB,KAAK,UAAU,CAAC,KAAK;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40emotion/styled/base/dist/emotion-styled-base.browser.development.esm.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { withEmotionCache, ThemeContext } from '@emotion/react';\nimport { serializeStyles } from '@emotion/serialize';\nimport { useInsertionEffectAlwaysWithSyncFallback } from '@emotion/use-insertion-effect-with-fallbacks';\nimport { getRegisteredStyles, registerStyles, insertStyles } from '@emotion/utils';\nimport * as React from 'react';\nimport isPropValid from '@emotion/is-prop-valid';\n\nvar isDevelopment = true;\n\nvar testOmitPropsOnStringTag = isPropValid;\n\nvar testOmitPropsOnComponent = function testOmitPropsOnComponent(key) {\n  return key !== 'theme';\n};\n\nvar getDefaultShouldForwardProp = function getDefaultShouldForwardProp(tag) {\n  return typeof tag === 'string' && // 96 is one less than the char code\n  // for \"a\" so this is checking that\n  // it's a lowercase character\n  tag.charCodeAt(0) > 96 ? testOmitPropsOnStringTag : testOmitPropsOnComponent;\n};\nvar composeShouldForwardProps = function composeShouldForwardProps(tag, options, isReal) {\n  var shouldForwardProp;\n\n  if (options) {\n    var optionsShouldForwardProp = options.shouldForwardProp;\n    shouldForwardProp = tag.__emotion_forwardProp && optionsShouldForwardProp ? function (propName) {\n      return tag.__emotion_forwardProp(propName) && optionsShouldForwardProp(propName);\n    } : optionsShouldForwardProp;\n  }\n\n  if (typeof shouldForwardProp !== 'function' && isReal) {\n    shouldForwardProp = tag.__emotion_forwardProp;\n  }\n\n  return shouldForwardProp;\n};\n\nvar ILLEGAL_ESCAPE_SEQUENCE_ERROR = \"You have illegal escape sequence in your template literal, most likely inside content's property value.\\nBecause you write your CSS inside a JavaScript string you actually have to do double escaping, so for example \\\"content: '\\\\00d7';\\\" should become \\\"content: '\\\\\\\\00d7';\\\".\\nYou can read more about this here:\\nhttps://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Template_literals#ES2018_revision_of_illegal_escape_sequences\";\n\nvar Insertion = function Insertion(_ref) {\n  var cache = _ref.cache,\n      serialized = _ref.serialized,\n      isStringTag = _ref.isStringTag;\n  registerStyles(cache, serialized, isStringTag);\n  useInsertionEffectAlwaysWithSyncFallback(function () {\n    return insertStyles(cache, serialized, isStringTag);\n  });\n\n  return null;\n};\n\nvar createStyled = function createStyled(tag, options) {\n  {\n    if (tag === undefined) {\n      throw new Error('You are trying to create a styled element with an undefined component.\\nYou may have forgotten to import it.');\n    }\n  }\n\n  var isReal = tag.__emotion_real === tag;\n  var baseTag = isReal && tag.__emotion_base || tag;\n  var identifierName;\n  var targetClassName;\n\n  if (options !== undefined) {\n    identifierName = options.label;\n    targetClassName = options.target;\n  }\n\n  var shouldForwardProp = composeShouldForwardProps(tag, options, isReal);\n  var defaultShouldForwardProp = shouldForwardProp || getDefaultShouldForwardProp(baseTag);\n  var shouldUseAs = !defaultShouldForwardProp('as');\n  return function () {\n    // eslint-disable-next-line prefer-rest-params\n    var args = arguments;\n    var styles = isReal && tag.__emotion_styles !== undefined ? tag.__emotion_styles.slice(0) : [];\n\n    if (identifierName !== undefined) {\n      styles.push(\"label:\" + identifierName + \";\");\n    }\n\n    if (args[0] == null || args[0].raw === undefined) {\n      // eslint-disable-next-line prefer-spread\n      styles.push.apply(styles, args);\n    } else {\n      var templateStringsArr = args[0];\n\n      if (templateStringsArr[0] === undefined) {\n        console.error(ILLEGAL_ESCAPE_SEQUENCE_ERROR);\n      }\n\n      styles.push(templateStringsArr[0]);\n      var len = args.length;\n      var i = 1;\n\n      for (; i < len; i++) {\n        if (templateStringsArr[i] === undefined) {\n          console.error(ILLEGAL_ESCAPE_SEQUENCE_ERROR);\n        }\n\n        styles.push(args[i], templateStringsArr[i]);\n      }\n    }\n\n    var Styled = withEmotionCache(function (props, cache, ref) {\n      var FinalTag = shouldUseAs && props.as || baseTag;\n      var className = '';\n      var classInterpolations = [];\n      var mergedProps = props;\n\n      if (props.theme == null) {\n        mergedProps = {};\n\n        for (var key in props) {\n          mergedProps[key] = props[key];\n        }\n\n        mergedProps.theme = React.useContext(ThemeContext);\n      }\n\n      if (typeof props.className === 'string') {\n        className = getRegisteredStyles(cache.registered, classInterpolations, props.className);\n      } else if (props.className != null) {\n        className = props.className + \" \";\n      }\n\n      var serialized = serializeStyles(styles.concat(classInterpolations), cache.registered, mergedProps);\n      className += cache.key + \"-\" + serialized.name;\n\n      if (targetClassName !== undefined) {\n        className += \" \" + targetClassName;\n      }\n\n      var finalShouldForwardProp = shouldUseAs && shouldForwardProp === undefined ? getDefaultShouldForwardProp(FinalTag) : defaultShouldForwardProp;\n      var newProps = {};\n\n      for (var _key in props) {\n        if (shouldUseAs && _key === 'as') continue;\n\n        if (finalShouldForwardProp(_key)) {\n          newProps[_key] = props[_key];\n        }\n      }\n\n      newProps.className = className;\n\n      if (ref) {\n        newProps.ref = ref;\n      }\n\n      return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Insertion, {\n        cache: cache,\n        serialized: serialized,\n        isStringTag: typeof FinalTag === 'string'\n      }), /*#__PURE__*/React.createElement(FinalTag, newProps));\n    });\n    Styled.displayName = identifierName !== undefined ? identifierName : \"Styled(\" + (typeof baseTag === 'string' ? baseTag : baseTag.displayName || baseTag.name || 'Component') + \")\";\n    Styled.defaultProps = tag.defaultProps;\n    Styled.__emotion_real = Styled;\n    Styled.__emotion_base = baseTag;\n    Styled.__emotion_styles = styles;\n    Styled.__emotion_forwardProp = shouldForwardProp;\n    Object.defineProperty(Styled, 'toString', {\n      value: function value() {\n        if (targetClassName === undefined && isDevelopment) {\n          return 'NO_COMPONENT_SELECTOR';\n        }\n\n        return \".\" + targetClassName;\n      }\n    });\n\n    Styled.withComponent = function (nextTag, nextOptions) {\n      var newStyled = createStyled(nextTag, _extends({}, options, nextOptions, {\n        shouldForwardProp: composeShouldForwardProps(Styled, nextOptions, true)\n      }));\n      return newStyled.apply(void 0, styles);\n    };\n\n    return Styled;\n  };\n};\n\nexport { createStyled as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,IAAI,gBAAgB;AAEpB,IAAI,2BAA2B,oMAAA,CAAA,UAAW;AAE1C,IAAI,2BAA2B,SAAS,yBAAyB,GAAG;IAClE,OAAO,QAAQ;AACjB;AAEA,IAAI,8BAA8B,SAAS,4BAA4B,GAAG;IACxE,OAAO,OAAO,QAAQ,YAAY,oCAAoC;IACtE,mCAAmC;IACnC,6BAA6B;IAC7B,IAAI,UAAU,CAAC,KAAK,KAAK,2BAA2B;AACtD;AACA,IAAI,4BAA4B,SAAS,0BAA0B,GAAG,EAAE,OAAO,EAAE,MAAM;IACrF,IAAI;IAEJ,IAAI,SAAS;QACX,IAAI,2BAA2B,QAAQ,iBAAiB;QACxD,oBAAoB,IAAI,qBAAqB,IAAI,2BAA2B,SAAU,QAAQ;YAC5F,OAAO,IAAI,qBAAqB,CAAC,aAAa,yBAAyB;QACzE,IAAI;IACN;IAEA,IAAI,OAAO,sBAAsB,cAAc,QAAQ;QACrD,oBAAoB,IAAI,qBAAqB;IAC/C;IAEA,OAAO;AACT;AAEA,IAAI,gCAAgC;AAEpC,IAAI,YAAY,SAAS,UAAU,IAAI;IACrC,IAAI,QAAQ,KAAK,KAAK,EAClB,aAAa,KAAK,UAAU,EAC5B,cAAc,KAAK,WAAW;IAClC,CAAA,GAAA,mLAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,YAAY;IAClC,CAAA,GAAA,uQAAA,CAAA,2CAAwC,AAAD;8DAAE;YACvC,OAAO,CAAA,GAAA,mLAAA,CAAA,eAAY,AAAD,EAAE,OAAO,YAAY;QACzC;;IAEA,OAAO;AACT;AAEA,IAAI,eAAe,SAAS,aAAa,GAAG,EAAE,OAAO;IACnD;QACE,IAAI,QAAQ,WAAW;YACrB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,IAAI,SAAS,IAAI,cAAc,KAAK;IACpC,IAAI,UAAU,UAAU,IAAI,cAAc,IAAI;IAC9C,IAAI;IACJ,IAAI;IAEJ,IAAI,YAAY,WAAW;QACzB,iBAAiB,QAAQ,KAAK;QAC9B,kBAAkB,QAAQ,MAAM;IAClC;IAEA,IAAI,oBAAoB,0BAA0B,KAAK,SAAS;IAChE,IAAI,2BAA2B,qBAAqB,4BAA4B;IAChF,IAAI,cAAc,CAAC,yBAAyB;IAC5C,OAAO;QACL,8CAA8C;QAC9C,IAAI,OAAO;QACX,IAAI,SAAS,UAAU,IAAI,gBAAgB,KAAK,YAAY,IAAI,gBAAgB,CAAC,KAAK,CAAC,KAAK,EAAE;QAE9F,IAAI,mBAAmB,WAAW;YAChC,OAAO,IAAI,CAAC,WAAW,iBAAiB;QAC1C;QAEA,IAAI,IAAI,CAAC,EAAE,IAAI,QAAQ,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,WAAW;YAChD,yCAAyC;YACzC,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;QAC5B,OAAO;YACL,IAAI,qBAAqB,IAAI,CAAC,EAAE;YAEhC,IAAI,kBAAkB,CAAC,EAAE,KAAK,WAAW;gBACvC,QAAQ,KAAK,CAAC;YAChB;YAEA,OAAO,IAAI,CAAC,kBAAkB,CAAC,EAAE;YACjC,IAAI,MAAM,KAAK,MAAM;YACrB,IAAI,IAAI;YAER,MAAO,IAAI,KAAK,IAAK;gBACnB,IAAI,kBAAkB,CAAC,EAAE,KAAK,WAAW;oBACvC,QAAQ,KAAK,CAAC;gBAChB;gBAEA,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,kBAAkB,CAAC,EAAE;YAC5C;QACF;QAEA,IAAI,SAAS,CAAA,GAAA,yPAAA,CAAA,mBAAgB,AAAD,EAAE,SAAU,KAAK,EAAE,KAAK,EAAE,GAAG;YACvD,IAAI,WAAW,eAAe,MAAM,EAAE,IAAI;YAC1C,IAAI,YAAY;YAChB,IAAI,sBAAsB,EAAE;YAC5B,IAAI,cAAc;YAElB,IAAI,MAAM,KAAK,IAAI,MAAM;gBACvB,cAAc,CAAC;gBAEf,IAAK,IAAI,OAAO,MAAO;oBACrB,WAAW,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;gBAC/B;gBAEA,YAAY,KAAK,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE,qPAAA,CAAA,eAAY;YACnD;YAEA,IAAI,OAAO,MAAM,SAAS,KAAK,UAAU;gBACvC,YAAY,CAAA,GAAA,mLAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,UAAU,EAAE,qBAAqB,MAAM,SAAS;YACxF,OAAO,IAAI,MAAM,SAAS,IAAI,MAAM;gBAClC,YAAY,MAAM,SAAS,GAAG;YAChC;YAEA,IAAI,aAAa,CAAA,GAAA,+LAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,MAAM,CAAC,sBAAsB,MAAM,UAAU,EAAE;YACvF,aAAa,MAAM,GAAG,GAAG,MAAM,WAAW,IAAI;YAE9C,IAAI,oBAAoB,WAAW;gBACjC,aAAa,MAAM;YACrB;YAEA,IAAI,yBAAyB,eAAe,sBAAsB,YAAY,4BAA4B,YAAY;YACtH,IAAI,WAAW,CAAC;YAEhB,IAAK,IAAI,QAAQ,MAAO;gBACtB,IAAI,eAAe,SAAS,MAAM;gBAElC,IAAI,uBAAuB,OAAO;oBAChC,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;gBAC9B;YACF;YAEA,SAAS,SAAS,GAAG;YAErB,IAAI,KAAK;gBACP,SAAS,GAAG,GAAG;YACjB;YAEA,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,6JAAA,CAAA,WAAc,EAAE,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,WAAW;gBACxG,OAAO;gBACP,YAAY;gBACZ,aAAa,OAAO,aAAa;YACnC,IAAI,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,UAAU;QACjD;QACA,OAAO,WAAW,GAAG,mBAAmB,YAAY,iBAAiB,YAAY,CAAC,OAAO,YAAY,WAAW,UAAU,QAAQ,WAAW,IAAI,QAAQ,IAAI,IAAI,WAAW,IAAI;QAChL,OAAO,YAAY,GAAG,IAAI,YAAY;QACtC,OAAO,cAAc,GAAG;QACxB,OAAO,cAAc,GAAG;QACxB,OAAO,gBAAgB,GAAG;QAC1B,OAAO,qBAAqB,GAAG;QAC/B,OAAO,cAAc,CAAC,QAAQ,YAAY;YACxC,OAAO,SAAS;gBACd,IAAI,oBAAoB,aAAa,eAAe;oBAClD,OAAO;gBACT;gBAEA,OAAO,MAAM;YACf;QACF;QAEA,OAAO,aAAa,GAAG,SAAU,OAAO,EAAE,WAAW;YACnD,IAAI,YAAY,aAAa,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,SAAS,aAAa;gBACvE,mBAAmB,0BAA0B,QAAQ,aAAa;YACpE;YACA,OAAO,UAAU,KAAK,CAAC,KAAK,GAAG;QACjC;QAEA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40emotion/styled/dist/emotion-styled.browser.development.esm.js"], "sourcesContent": ["import createStyled from '../base/dist/emotion-styled-base.browser.development.esm.js';\nimport '@babel/runtime/helpers/extends';\nimport '@emotion/react';\nimport '@emotion/serialize';\nimport '@emotion/use-insertion-effect-with-fallbacks';\nimport '@emotion/utils';\nimport 'react';\nimport '@emotion/is-prop-valid';\n\nvar tags = ['a', 'abbr', 'address', 'area', 'article', 'aside', 'audio', 'b', 'base', 'bdi', 'bdo', 'big', 'blockquote', 'body', 'br', 'button', 'canvas', 'caption', 'cite', 'code', 'col', 'colgroup', 'data', 'datalist', 'dd', 'del', 'details', 'dfn', 'dialog', 'div', 'dl', 'dt', 'em', 'embed', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hgroup', 'hr', 'html', 'i', 'iframe', 'img', 'input', 'ins', 'kbd', 'keygen', 'label', 'legend', 'li', 'link', 'main', 'map', 'mark', 'marquee', 'menu', 'menuitem', 'meta', 'meter', 'nav', 'noscript', 'object', 'ol', 'optgroup', 'option', 'output', 'p', 'param', 'picture', 'pre', 'progress', 'q', 'rp', 'rt', 'ruby', 's', 'samp', 'script', 'section', 'select', 'small', 'source', 'span', 'strong', 'style', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'textarea', 'tfoot', 'th', 'thead', 'time', 'title', 'tr', 'track', 'u', 'ul', 'var', 'video', 'wbr', // SVG\n'circle', 'clipPath', 'defs', 'ellipse', 'foreignObject', 'g', 'image', 'line', 'linearGradient', 'mask', 'path', 'pattern', 'polygon', 'polyline', 'radialGradient', 'rect', 'stop', 'svg', 'text', 'tspan'];\n\n// bind it to avoid mutating the original function\nvar newStyled = createStyled.bind(null);\ntags.forEach(function (tagName) {\n  newStyled[tagName] = newStyled(tagName);\n});\n\nexport { newStyled as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,IAAI,OAAO;IAAC;IAAK;IAAQ;IAAW;IAAQ;IAAW;IAAS;IAAS;IAAK;IAAQ;IAAO;IAAO;IAAO;IAAc;IAAQ;IAAM;IAAU;IAAU;IAAW;IAAQ;IAAQ;IAAO;IAAY;IAAQ;IAAY;IAAM;IAAO;IAAW;IAAO;IAAU;IAAO;IAAM;IAAM;IAAM;IAAS;IAAY;IAAc;IAAU;IAAU;IAAQ;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAQ;IAAU;IAAU;IAAM;IAAQ;IAAK;IAAU;IAAO;IAAS;IAAO;IAAO;IAAU;IAAS;IAAU;IAAM;IAAQ;IAAQ;IAAO;IAAQ;IAAW;IAAQ;IAAY;IAAQ;IAAS;IAAO;IAAY;IAAU;IAAM;IAAY;IAAU;IAAU;IAAK;IAAS;IAAW;IAAO;IAAY;IAAK;IAAM;IAAM;IAAQ;IAAK;IAAQ;IAAU;IAAW;IAAU;IAAS;IAAU;IAAQ;IAAU;IAAS;IAAO;IAAW;IAAO;IAAS;IAAS;IAAM;IAAY;IAAS;IAAM;IAAS;IAAQ;IAAS;IAAM;IAAS;IAAK;IAAM;IAAO;IAAS;IAC77B;IAAU;IAAY;IAAQ;IAAW;IAAiB;IAAK;IAAS;IAAQ;IAAkB;IAAQ;IAAQ;IAAW;IAAW;IAAY;IAAkB;IAAQ;IAAQ;IAAO;IAAQ;CAAQ;AAE7M,kDAAkD;AAClD,IAAI,YAAY,oNAAA,CAAA,UAAY,CAAC,IAAI,CAAC;AAClC,KAAK,OAAO,CAAC,SAAU,OAAO;IAC5B,SAAS,CAAC,QAAQ,GAAG,UAAU;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/styled-engine/esm/index.js"], "sourcesContent": ["/**\n * @mui/styled-engine v7.1.0\n *\n * @license MIT\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use client';\n\n/* eslint-disable no-underscore-dangle */\nimport emStyled from '@emotion/styled';\nimport { serializeStyles as emSerializeStyles } from '@emotion/serialize';\nexport default function styled(tag, options) {\n  const stylesFactory = emStyled(tag, options);\n  if (process.env.NODE_ENV !== 'production') {\n    return (...styles) => {\n      const component = typeof tag === 'string' ? `\"${tag}\"` : 'component';\n      if (styles.length === 0) {\n        console.error([`MUI: Seems like you called \\`styled(${component})()\\` without a \\`style\\` argument.`, 'You must provide a `styles` argument: `styled(\"div\")(styleYouForgotToPass)`.'].join('\\n'));\n      } else if (styles.some(style => style === undefined)) {\n        console.error(`MUI: the styled(${component})(...args) API requires all its args to be defined.`);\n      }\n      return stylesFactory(...styles);\n    };\n  }\n  return stylesFactory;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function internal_mutateStyles(tag, processor) {\n  // Emotion attaches all the styles as `__emotion_styles`.\n  // Ref: https://github.com/emotion-js/emotion/blob/16d971d0da229596d6bcc39d282ba9753c9ee7cf/packages/styled/src/base.js#L186\n  if (Array.isArray(tag.__emotion_styles)) {\n    tag.__emotion_styles = processor(tag.__emotion_styles);\n  }\n}\n\n// Emotion only accepts an array, but we want to avoid allocations\nconst wrapper = [];\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function internal_serializeStyles(styles) {\n  wrapper[0] = styles;\n  return emSerializeStyles(wrapper);\n}\nexport { ThemeContext, keyframes, css } from '@emotion/react';\nexport { default as StyledEngineProvider } from \"./StyledEngineProvider/index.js\";\nexport { default as GlobalStyles } from \"./GlobalStyles/index.js\";"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;;AAQK;AALN,uCAAuC,GACvC;AACA;AAJA;;;AAKe,SAAS,OAAO,GAAG,EAAE,OAAO;IACzC,MAAM,gBAAgB,CAAA,GAAA,oMAAA,CAAA,UAAQ,AAAD,EAAE,KAAK;IACpC,wCAA2C;QACzC,OAAO,CAAC,GAAG;YACT,MAAM,YAAY,OAAO,QAAQ,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG;YACzD,IAAI,OAAO,MAAM,KAAK,GAAG;gBACvB,QAAQ,KAAK,CAAC;oBAAC,CAAC,oCAAoC,EAAE,UAAU,mCAAmC,CAAC;oBAAE;iBAA+E,CAAC,IAAI,CAAC;YAC7L,OAAO,IAAI,OAAO,IAAI,CAAC,CAAA,QAAS,UAAU,YAAY;gBACpD,QAAQ,KAAK,CAAC,CAAC,gBAAgB,EAAE,UAAU,mDAAmD,CAAC;YACjG;YACA,OAAO,iBAAiB;QAC1B;IACF;;AAEF;AAGO,SAAS,sBAAsB,GAAG,EAAE,SAAS;IAClD,yDAAyD;IACzD,4HAA4H;IAC5H,IAAI,MAAM,OAAO,CAAC,IAAI,gBAAgB,GAAG;QACvC,IAAI,gBAAgB,GAAG,UAAU,IAAI,gBAAgB;IACvD;AACF;AAEA,kEAAkE;AAClE,MAAM,UAAU,EAAE;AAEX,SAAS,yBAAyB,MAAM;IAC7C,OAAO,CAAC,EAAE,GAAG;IACb,OAAO,CAAA,GAAA,+LAAA,CAAA,kBAAiB,AAAD,EAAE;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/system/esm/createBox/createBox.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport styled from '@mui/styled-engine';\nimport styleFunctionSx, { extendSxProp } from \"../styleFunctionSx/index.js\";\nimport useTheme from \"../useTheme/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function createBox(options = {}) {\n  const {\n    themeId,\n    defaultTheme,\n    defaultClassName = 'MuiBox-root',\n    generateClassName\n  } = options;\n  const BoxRoot = styled('div', {\n    shouldForwardProp: prop => prop !== 'theme' && prop !== 'sx' && prop !== 'as'\n  })(styleFunctionSx);\n  const Box = /*#__PURE__*/React.forwardRef(function Box(inProps, ref) {\n    const theme = useTheme(defaultTheme);\n    const {\n      className,\n      component = 'div',\n      ...other\n    } = extendSxProp(inProps);\n    return /*#__PURE__*/_jsx(BoxRoot, {\n      as: component,\n      ref: ref,\n      className: clsx(className, generateClassName ? generateClassName(defaultClassName) : defaultClassName),\n      theme: themeId ? theme[themeId] || theme : theme,\n      ...other\n    });\n  });\n  return Box;\n}"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAPA;;;;;;;AAQe,SAAS,UAAU,UAAU,CAAC,CAAC;IAC5C,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,mBAAmB,aAAa,EAChC,iBAAiB,EAClB,GAAG;IACJ,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,UAAM,AAAD,EAAE,OAAO;QAC5B,mBAAmB,CAAA,OAAQ,SAAS,WAAW,SAAS,QAAQ,SAAS;IAC3E,GAAG,+KAAA,CAAA,UAAe;IAClB,MAAM,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,IAAI,OAAO,EAAE,GAAG;QACjE,MAAM,QAAQ,CAAA,GAAA,iKAAA,CAAA,UAAQ,AAAD,EAAE;QACvB,MAAM,EACJ,SAAS,EACT,YAAY,KAAK,EACjB,GAAG,OACJ,GAAG,CAAA,GAAA,uNAAA,CAAA,eAAY,AAAD,EAAE;QACjB,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,SAAS;YAChC,IAAI;YACJ,KAAK;YACL,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,WAAW,oBAAoB,kBAAkB,oBAAoB;YACrF,OAAO,UAAU,KAAK,CAAC,QAAQ,IAAI,QAAQ;YAC3C,GAAG,KAAK;QACV;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/system/esm/preprocessStyles.js"], "sourcesContent": ["import { internal_serializeStyles } from '@mui/styled-engine';\nexport default function preprocessStyles(input) {\n  const {\n    variants,\n    ...style\n  } = input;\n  const result = {\n    variants,\n    style: internal_serializeStyles(style),\n    isProcessed: true\n  };\n\n  // Not supported on styled-components\n  if (result.style === style) {\n    return result;\n  }\n  if (variants) {\n    variants.forEach(variant => {\n      if (typeof variant.style !== 'function') {\n        variant.style = internal_serializeStyles(variant.style);\n      }\n    });\n  }\n  return result;\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,iBAAiB,KAAK;IAC5C,MAAM,EACJ,QAAQ,EACR,GAAG,OACJ,GAAG;IACJ,MAAM,SAAS;QACb;QACA,OAAO,CAAA,GAAA,4KAAA,CAAA,2BAAwB,AAAD,EAAE;QAChC,aAAa;IACf;IAEA,qCAAqC;IACrC,IAAI,OAAO,KAAK,KAAK,OAAO;QAC1B,OAAO;IACT;IACA,IAAI,UAAU;QACZ,SAAS,OAAO,CAAC,CAAA;YACf,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY;gBACvC,QAAQ,KAAK,GAAG,CAAA,GAAA,4KAAA,CAAA,2BAAwB,AAAD,EAAE,QAAQ,KAAK;YACxD;QACF;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 538, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/system/esm/createStyled/createStyled.js"], "sourcesContent": ["import styledEngineStyled, { internal_mutateStyles as mutateStyles } from '@mui/styled-engine';\nimport { isPlainObject } from '@mui/utils/deepmerge';\nimport capitalize from '@mui/utils/capitalize';\nimport getDisplayName from '@mui/utils/getDisplayName';\nimport createTheme from \"../createTheme/index.js\";\nimport styleFunctionSx from \"../styleFunctionSx/index.js\";\nimport preprocessStyles from \"../preprocessStyles.js\";\n\n/* eslint-disable no-underscore-dangle */\n/* eslint-disable no-labels */\n/* eslint-disable no-lone-blocks */\n\nexport const systemDefaultTheme = createTheme();\n\n// Update /system/styled/#api in case if this changes\nexport function shouldForwardProp(prop) {\n  return prop !== 'ownerState' && prop !== 'theme' && prop !== 'sx' && prop !== 'as';\n}\nfunction defaultOverridesResolver(slot) {\n  if (!slot) {\n    return null;\n  }\n  return (_props, styles) => styles[slot];\n}\nfunction attachTheme(props, themeId, defaultTheme) {\n  props.theme = isObjectEmpty(props.theme) ? defaultTheme : props.theme[themeId] || props.theme;\n}\nfunction processStyle(props, style) {\n  /*\n   * Style types:\n   *  - null/undefined\n   *  - string\n   *  - CSS style object: { [cssKey]: [cssValue], variants }\n   *  - Processed style object: { style, variants, isProcessed: true }\n   *  - Array of any of the above\n   */\n\n  const resolvedStyle = typeof style === 'function' ? style(props) : style;\n  if (Array.isArray(resolvedStyle)) {\n    return resolvedStyle.flatMap(subStyle => processStyle(props, subStyle));\n  }\n  if (Array.isArray(resolvedStyle?.variants)) {\n    let rootStyle;\n    if (resolvedStyle.isProcessed) {\n      rootStyle = resolvedStyle.style;\n    } else {\n      const {\n        variants,\n        ...otherStyles\n      } = resolvedStyle;\n      rootStyle = otherStyles;\n    }\n    return processStyleVariants(props, resolvedStyle.variants, [rootStyle]);\n  }\n  if (resolvedStyle?.isProcessed) {\n    return resolvedStyle.style;\n  }\n  return resolvedStyle;\n}\nfunction processStyleVariants(props, variants, results = []) {\n  let mergedState; // We might not need it, initialized lazily\n\n  variantLoop: for (let i = 0; i < variants.length; i += 1) {\n    const variant = variants[i];\n    if (typeof variant.props === 'function') {\n      mergedState ??= {\n        ...props,\n        ...props.ownerState,\n        ownerState: props.ownerState\n      };\n      if (!variant.props(mergedState)) {\n        continue;\n      }\n    } else {\n      for (const key in variant.props) {\n        if (props[key] !== variant.props[key] && props.ownerState?.[key] !== variant.props[key]) {\n          continue variantLoop;\n        }\n      }\n    }\n    if (typeof variant.style === 'function') {\n      mergedState ??= {\n        ...props,\n        ...props.ownerState,\n        ownerState: props.ownerState\n      };\n      results.push(variant.style(mergedState));\n    } else {\n      results.push(variant.style);\n    }\n  }\n  return results;\n}\nexport default function createStyled(input = {}) {\n  const {\n    themeId,\n    defaultTheme = systemDefaultTheme,\n    rootShouldForwardProp = shouldForwardProp,\n    slotShouldForwardProp = shouldForwardProp\n  } = input;\n  function styleAttachTheme(props) {\n    attachTheme(props, themeId, defaultTheme);\n  }\n  const styled = (tag, inputOptions = {}) => {\n    // If `tag` is already a styled component, filter out the `sx` style function\n    // to prevent unnecessary styles generated by the composite components.\n    mutateStyles(tag, styles => styles.filter(style => style !== styleFunctionSx));\n    const {\n      name: componentName,\n      slot: componentSlot,\n      skipVariantsResolver: inputSkipVariantsResolver,\n      skipSx: inputSkipSx,\n      // TODO v6: remove `lowercaseFirstLetter()` in the next major release\n      // For more details: https://github.com/mui/material-ui/pull/37908\n      overridesResolver = defaultOverridesResolver(lowercaseFirstLetter(componentSlot)),\n      ...options\n    } = inputOptions;\n\n    // if skipVariantsResolver option is defined, take the value, otherwise, true for root and false for other slots.\n    const skipVariantsResolver = inputSkipVariantsResolver !== undefined ? inputSkipVariantsResolver :\n    // TODO v6: remove `Root` in the next major release\n    // For more details: https://github.com/mui/material-ui/pull/37908\n    componentSlot && componentSlot !== 'Root' && componentSlot !== 'root' || false;\n    const skipSx = inputSkipSx || false;\n    let shouldForwardPropOption = shouldForwardProp;\n\n    // TODO v6: remove `Root` in the next major release\n    // For more details: https://github.com/mui/material-ui/pull/37908\n    if (componentSlot === 'Root' || componentSlot === 'root') {\n      shouldForwardPropOption = rootShouldForwardProp;\n    } else if (componentSlot) {\n      // any other slot specified\n      shouldForwardPropOption = slotShouldForwardProp;\n    } else if (isStringTag(tag)) {\n      // for string (html) tag, preserve the behavior in emotion & styled-components.\n      shouldForwardPropOption = undefined;\n    }\n    const defaultStyledResolver = styledEngineStyled(tag, {\n      shouldForwardProp: shouldForwardPropOption,\n      label: generateStyledLabel(componentName, componentSlot),\n      ...options\n    });\n    const transformStyle = style => {\n      // On the server Emotion doesn't use React.forwardRef for creating components, so the created\n      // component stays as a function. This condition makes sure that we do not interpolate functions\n      // which are basically components used as a selectors.\n      if (typeof style === 'function' && style.__emotion_real !== style) {\n        return function styleFunctionProcessor(props) {\n          return processStyle(props, style);\n        };\n      }\n      if (isPlainObject(style)) {\n        const serialized = preprocessStyles(style);\n        if (!serialized.variants) {\n          return serialized.style;\n        }\n        return function styleObjectProcessor(props) {\n          return processStyle(props, serialized);\n        };\n      }\n      return style;\n    };\n    const muiStyledResolver = (...expressionsInput) => {\n      const expressionsHead = [];\n      const expressionsBody = expressionsInput.map(transformStyle);\n      const expressionsTail = [];\n\n      // Preprocess `props` to set the scoped theme value.\n      // This must run before any other expression.\n      expressionsHead.push(styleAttachTheme);\n      if (componentName && overridesResolver) {\n        expressionsTail.push(function styleThemeOverrides(props) {\n          const theme = props.theme;\n          const styleOverrides = theme.components?.[componentName]?.styleOverrides;\n          if (!styleOverrides) {\n            return null;\n          }\n          const resolvedStyleOverrides = {};\n\n          // TODO: v7 remove iteration and use `resolveStyleArg(styleOverrides[slot])` directly\n          // eslint-disable-next-line guard-for-in\n          for (const slotKey in styleOverrides) {\n            resolvedStyleOverrides[slotKey] = processStyle(props, styleOverrides[slotKey]);\n          }\n          return overridesResolver(props, resolvedStyleOverrides);\n        });\n      }\n      if (componentName && !skipVariantsResolver) {\n        expressionsTail.push(function styleThemeVariants(props) {\n          const theme = props.theme;\n          const themeVariants = theme?.components?.[componentName]?.variants;\n          if (!themeVariants) {\n            return null;\n          }\n          return processStyleVariants(props, themeVariants);\n        });\n      }\n      if (!skipSx) {\n        expressionsTail.push(styleFunctionSx);\n      }\n\n      // This function can be called as a tagged template, so the first argument would contain\n      // CSS `string[]` values.\n      if (Array.isArray(expressionsBody[0])) {\n        const inputStrings = expressionsBody.shift();\n\n        // We need to add placeholders in the tagged template for the custom functions we have\n        // possibly added (attachTheme, overrides, variants, and sx).\n        const placeholdersHead = new Array(expressionsHead.length).fill('');\n        const placeholdersTail = new Array(expressionsTail.length).fill('');\n        let outputStrings;\n        // prettier-ignore\n        {\n          outputStrings = [...placeholdersHead, ...inputStrings, ...placeholdersTail];\n          outputStrings.raw = [...placeholdersHead, ...inputStrings.raw, ...placeholdersTail];\n        }\n\n        // The only case where we put something before `attachTheme`\n        expressionsHead.unshift(outputStrings);\n      }\n      const expressions = [...expressionsHead, ...expressionsBody, ...expressionsTail];\n      const Component = defaultStyledResolver(...expressions);\n      if (tag.muiName) {\n        Component.muiName = tag.muiName;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        Component.displayName = generateDisplayName(componentName, componentSlot, tag);\n      }\n      return Component;\n    };\n    if (defaultStyledResolver.withConfig) {\n      muiStyledResolver.withConfig = defaultStyledResolver.withConfig;\n    }\n    return muiStyledResolver;\n  };\n  return styled;\n}\nfunction generateDisplayName(componentName, componentSlot, tag) {\n  if (componentName) {\n    return `${componentName}${capitalize(componentSlot || '')}`;\n  }\n  return `Styled(${getDisplayName(tag)})`;\n}\nfunction generateStyledLabel(componentName, componentSlot) {\n  let label;\n  if (process.env.NODE_ENV !== 'production') {\n    if (componentName) {\n      // TODO v6: remove `lowercaseFirstLetter()` in the next major release\n      // For more details: https://github.com/mui/material-ui/pull/37908\n      label = `${componentName}-${lowercaseFirstLetter(componentSlot || 'Root')}`;\n    }\n  }\n  return label;\n}\nfunction isObjectEmpty(object) {\n  // eslint-disable-next-line\n  for (const _ in object) {\n    return false;\n  }\n  return true;\n}\n\n// https://github.com/emotion-js/emotion/blob/26ded6109fcd8ca9875cc2ce4564fee678a3f3c5/packages/styled/src/utils.js#L40\nfunction isStringTag(tag) {\n  return typeof tag === 'string' &&\n  // 96 is one less than the char code\n  // for \"a\" so this is checking that\n  // it's a lowercase character\n  tag.charCodeAt(0) > 96;\n}\nfunction lowercaseFirstLetter(string) {\n  if (!string) {\n    return string;\n  }\n  return string.charAt(0).toLowerCase() + string.slice(1);\n}"], "names": [], "mappings": ";;;;;AAiOU;AAjOV;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAMO,MAAM,qBAAqB,CAAA,GAAA,uKAAA,CAAA,UAAW,AAAD;AAGrC,SAAS,kBAAkB,IAAI;IACpC,OAAO,SAAS,gBAAgB,SAAS,WAAW,SAAS,QAAQ,SAAS;AAChF;AACA,SAAS,yBAAyB,IAAI;IACpC,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IACA,OAAO,CAAC,QAAQ,SAAW,MAAM,CAAC,KAAK;AACzC;AACA,SAAS,YAAY,KAAK,EAAE,OAAO,EAAE,YAAY;IAC/C,MAAM,KAAK,GAAG,cAAc,MAAM,KAAK,IAAI,eAAe,MAAM,KAAK,CAAC,QAAQ,IAAI,MAAM,KAAK;AAC/F;AACA,SAAS,aAAa,KAAK,EAAE,KAAK;IAChC;;;;;;;GAOC,GAED,MAAM,gBAAgB,OAAO,UAAU,aAAa,MAAM,SAAS;IACnE,IAAI,MAAM,OAAO,CAAC,gBAAgB;QAChC,OAAO,cAAc,OAAO,CAAC,CAAA,WAAY,aAAa,OAAO;IAC/D;IACA,IAAI,MAAM,OAAO,CAAC,eAAe,WAAW;QAC1C,IAAI;QACJ,IAAI,cAAc,WAAW,EAAE;YAC7B,YAAY,cAAc,KAAK;QACjC,OAAO;YACL,MAAM,EACJ,QAAQ,EACR,GAAG,aACJ,GAAG;YACJ,YAAY;QACd;QACA,OAAO,qBAAqB,OAAO,cAAc,QAAQ,EAAE;YAAC;SAAU;IACxE;IACA,IAAI,eAAe,aAAa;QAC9B,OAAO,cAAc,KAAK;IAC5B;IACA,OAAO;AACT;AACA,SAAS,qBAAqB,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE;IACzD,IAAI,aAAa,2CAA2C;IAE5D,aAAa,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,EAAG;QACxD,MAAM,UAAU,QAAQ,CAAC,EAAE;QAC3B,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY;YACvC,gBAAgB;gBACd,GAAG,KAAK;gBACR,GAAG,MAAM,UAAU;gBACnB,YAAY,MAAM,UAAU;YAC9B;YACA,IAAI,CAAC,QAAQ,KAAK,CAAC,cAAc;gBAC/B;YACF;QACF,OAAO;YACL,IAAK,MAAM,OAAO,QAAQ,KAAK,CAAE;gBAC/B,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,KAAK,CAAC,IAAI,IAAI,MAAM,UAAU,EAAE,CAAC,IAAI,KAAK,QAAQ,KAAK,CAAC,IAAI,EAAE;oBACvF,SAAS;gBACX;YACF;QACF;QACA,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY;YACvC,gBAAgB;gBACd,GAAG,KAAK;gBACR,GAAG,MAAM,UAAU;gBACnB,YAAY,MAAM,UAAU;YAC9B;YACA,QAAQ,IAAI,CAAC,QAAQ,KAAK,CAAC;QAC7B,OAAO;YACL,QAAQ,IAAI,CAAC,QAAQ,KAAK;QAC5B;IACF;IACA,OAAO;AACT;AACe,SAAS,aAAa,QAAQ,CAAC,CAAC;IAC7C,MAAM,EACJ,OAAO,EACP,eAAe,kBAAkB,EACjC,wBAAwB,iBAAiB,EACzC,wBAAwB,iBAAiB,EAC1C,GAAG;IACJ,SAAS,iBAAiB,KAAK;QAC7B,YAAY,OAAO,SAAS;IAC9B;IACA,MAAM,SAAS,CAAC,KAAK,eAAe,CAAC,CAAC;QACpC,6EAA6E;QAC7E,uEAAuE;QACvE,CAAA,GAAA,4KAAA,CAAA,wBAAY,AAAD,EAAE,KAAK,CAAA,SAAU,OAAO,MAAM,CAAC,CAAA,QAAS,UAAU,+KAAA,CAAA,UAAe;QAC5E,MAAM,EACJ,MAAM,aAAa,EACnB,MAAM,aAAa,EACnB,sBAAsB,yBAAyB,EAC/C,QAAQ,WAAW,EACnB,qEAAqE;QACrE,kEAAkE;QAClE,oBAAoB,yBAAyB,qBAAqB,eAAe,EACjF,GAAG,SACJ,GAAG;QAEJ,iHAAiH;QACjH,MAAM,uBAAuB,8BAA8B,YAAY,4BACvE,mDAAmD;QACnD,kEAAkE;QAClE,iBAAiB,kBAAkB,UAAU,kBAAkB,UAAU;QACzE,MAAM,SAAS,eAAe;QAC9B,IAAI,0BAA0B;QAE9B,mDAAmD;QACnD,kEAAkE;QAClE,IAAI,kBAAkB,UAAU,kBAAkB,QAAQ;YACxD,0BAA0B;QAC5B,OAAO,IAAI,eAAe;YACxB,2BAA2B;YAC3B,0BAA0B;QAC5B,OAAO,IAAI,YAAY,MAAM;YAC3B,+EAA+E;YAC/E,0BAA0B;QAC5B;QACA,MAAM,wBAAwB,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,KAAK;YACpD,mBAAmB;YACnB,OAAO,oBAAoB,eAAe;YAC1C,GAAG,OAAO;QACZ;QACA,MAAM,iBAAiB,CAAA;YACrB,6FAA6F;YAC7F,gGAAgG;YAChG,sDAAsD;YACtD,IAAI,OAAO,UAAU,cAAc,MAAM,cAAc,KAAK,OAAO;gBACjE,OAAO,SAAS,uBAAuB,KAAK;oBAC1C,OAAO,aAAa,OAAO;gBAC7B;YACF;YACA,IAAI,CAAA,GAAA,kKAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;gBACxB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAgB,AAAD,EAAE;gBACpC,IAAI,CAAC,WAAW,QAAQ,EAAE;oBACxB,OAAO,WAAW,KAAK;gBACzB;gBACA,OAAO,SAAS,qBAAqB,KAAK;oBACxC,OAAO,aAAa,OAAO;gBAC7B;YACF;YACA,OAAO;QACT;QACA,MAAM,oBAAoB,CAAC,GAAG;YAC5B,MAAM,kBAAkB,EAAE;YAC1B,MAAM,kBAAkB,iBAAiB,GAAG,CAAC;YAC7C,MAAM,kBAAkB,EAAE;YAE1B,oDAAoD;YACpD,6CAA6C;YAC7C,gBAAgB,IAAI,CAAC;YACrB,IAAI,iBAAiB,mBAAmB;gBACtC,gBAAgB,IAAI,CAAC,SAAS,oBAAoB,KAAK;oBACrD,MAAM,QAAQ,MAAM,KAAK;oBACzB,MAAM,iBAAiB,MAAM,UAAU,EAAE,CAAC,cAAc,EAAE;oBAC1D,IAAI,CAAC,gBAAgB;wBACnB,OAAO;oBACT;oBACA,MAAM,yBAAyB,CAAC;oBAEhC,qFAAqF;oBACrF,wCAAwC;oBACxC,IAAK,MAAM,WAAW,eAAgB;wBACpC,sBAAsB,CAAC,QAAQ,GAAG,aAAa,OAAO,cAAc,CAAC,QAAQ;oBAC/E;oBACA,OAAO,kBAAkB,OAAO;gBAClC;YACF;YACA,IAAI,iBAAiB,CAAC,sBAAsB;gBAC1C,gBAAgB,IAAI,CAAC,SAAS,mBAAmB,KAAK;oBACpD,MAAM,QAAQ,MAAM,KAAK;oBACzB,MAAM,gBAAgB,OAAO,YAAY,CAAC,cAAc,EAAE;oBAC1D,IAAI,CAAC,eAAe;wBAClB,OAAO;oBACT;oBACA,OAAO,qBAAqB,OAAO;gBACrC;YACF;YACA,IAAI,CAAC,QAAQ;gBACX,gBAAgB,IAAI,CAAC,+KAAA,CAAA,UAAe;YACtC;YAEA,wFAAwF;YACxF,yBAAyB;YACzB,IAAI,MAAM,OAAO,CAAC,eAAe,CAAC,EAAE,GAAG;gBACrC,MAAM,eAAe,gBAAgB,KAAK;gBAE1C,sFAAsF;gBACtF,6DAA6D;gBAC7D,MAAM,mBAAmB,IAAI,MAAM,gBAAgB,MAAM,EAAE,IAAI,CAAC;gBAChE,MAAM,mBAAmB,IAAI,MAAM,gBAAgB,MAAM,EAAE,IAAI,CAAC;gBAChE,IAAI;gBACJ,kBAAkB;gBAClB;oBACE,gBAAgB;2BAAI;2BAAqB;2BAAiB;qBAAiB;oBAC3E,cAAc,GAAG,GAAG;2BAAI;2BAAqB,aAAa,GAAG;2BAAK;qBAAiB;gBACrF;gBAEA,4DAA4D;gBAC5D,gBAAgB,OAAO,CAAC;YAC1B;YACA,MAAM,cAAc;mBAAI;mBAAoB;mBAAoB;aAAgB;YAChF,MAAM,YAAY,yBAAyB;YAC3C,IAAI,IAAI,OAAO,EAAE;gBACf,UAAU,OAAO,GAAG,IAAI,OAAO;YACjC;YACA,wCAA2C;gBACzC,UAAU,WAAW,GAAG,oBAAoB,eAAe,eAAe;YAC5E;YACA,OAAO;QACT;QACA,IAAI,sBAAsB,UAAU,EAAE;YACpC,kBAAkB,UAAU,GAAG,sBAAsB,UAAU;QACjE;QACA,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,oBAAoB,aAAa,EAAE,aAAa,EAAE,GAAG;IAC5D,IAAI,eAAe;QACjB,OAAO,GAAG,gBAAgB,CAAA,GAAA,oKAAA,CAAA,UAAU,AAAD,EAAE,iBAAiB,KAAK;IAC7D;IACA,OAAO,CAAC,OAAO,EAAE,CAAA,GAAA,4KAAA,CAAA,UAAc,AAAD,EAAE,KAAK,CAAC,CAAC;AACzC;AACA,SAAS,oBAAoB,aAAa,EAAE,aAAa;IACvD,IAAI;IACJ,wCAA2C;QACzC,IAAI,eAAe;YACjB,qEAAqE;YACrE,kEAAkE;YAClE,QAAQ,GAAG,cAAc,CAAC,EAAE,qBAAqB,iBAAiB,SAAS;QAC7E;IACF;IACA,OAAO;AACT;AACA,SAAS,cAAc,MAAM;IAC3B,2BAA2B;IAC3B,IAAK,MAAM,KAAK,OAAQ;QACtB,OAAO;IACT;IACA,OAAO;AACT;AAEA,uHAAuH;AACvH,SAAS,YAAY,GAAG;IACtB,OAAO,OAAO,QAAQ,YACtB,oCAAoC;IACpC,mCAAmC;IACnC,6BAA6B;IAC7B,IAAI,UAAU,CAAC,KAAK;AACtB;AACA,SAAS,qBAAqB,MAAM;IAClC,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IACA,OAAO,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/system/esm/styled/styled.js"], "sourcesContent": ["import createStyled from \"../createStyled/index.js\";\nconst styled = createStyled();\nexport default styled;"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,SAAS,CAAA,GAAA,yKAAA,CAAA,UAAY,AAAD;uCACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 825, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/system/esm/useThemeProps/getThemeProps.js"], "sourcesContent": ["import resolveProps from '@mui/utils/resolveProps';\nexport default function getThemeProps(params) {\n  const {\n    theme,\n    name,\n    props\n  } = params;\n  if (!theme || !theme.components || !theme.components[name] || !theme.components[name].defaultProps) {\n    return props;\n  }\n  return resolveProps(theme.components[name].defaultProps, props);\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,cAAc,MAAM;IAC1C,MAAM,EACJ,KAAK,EACL,IAAI,EACJ,KAAK,EACN,GAAG;IACJ,IAAI,CAAC,SAAS,CAAC,MAAM,UAAU,IAAI,CAAC,MAAM,UAAU,CAAC,KAAK,IAAI,CAAC,MAAM,UAAU,CAAC,KAAK,CAAC,YAAY,EAAE;QAClG,OAAO;IACT;IACA,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAY,AAAD,EAAE,MAAM,UAAU,CAAC,KAAK,CAAC,YAAY,EAAE;AAC3D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 843, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/system/esm/useThemeProps/useThemeProps.js"], "sourcesContent": ["'use client';\n\nimport getThemeProps from \"./getThemeProps.js\";\nimport useTheme from \"../useTheme/index.js\";\nexport default function useThemeProps({\n  props,\n  name,\n  defaultTheme,\n  themeId\n}) {\n  let theme = useTheme(defaultTheme);\n  if (themeId) {\n    theme = theme[themeId] || theme;\n  }\n  return getThemeProps({\n    theme,\n    name,\n    props\n  });\n}"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAIe,SAAS,cAAc,EACpC,KAAK,EACL,IAAI,EACJ,YAAY,EACZ,OAAO,EACR;IACC,IAAI,QAAQ,CAAA,GAAA,iKAAA,CAAA,UAAQ,AAAD,EAAE;IACrB,IAAI,SAAS;QACX,QAAQ,KAAK,CAAC,QAAQ,IAAI;IAC5B;IACA,OAAO,CAAA,GAAA,2KAAA,CAAA,UAAa,AAAD,EAAE;QACnB;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/system/esm/Grid/traverseBreakpoints.js"], "sourcesContent": ["export const filterBreakpointKeys = (breakpointsKeys, responsiveKeys) => breakpointsKeys.filter(key => responsiveKeys.includes(key));\nexport const traverseBreakpoints = (breakpoints, responsive, iterator) => {\n  const smallestBreakpoint = breakpoints.keys[0]; // the keys is sorted from smallest to largest by `createBreakpoints`.\n\n  if (Array.isArray(responsive)) {\n    responsive.forEach((breakpointValue, index) => {\n      iterator((responsiveStyles, style) => {\n        if (index <= breakpoints.keys.length - 1) {\n          if (index === 0) {\n            Object.assign(responsiveStyles, style);\n          } else {\n            responsiveStyles[breakpoints.up(breakpoints.keys[index])] = style;\n          }\n        }\n      }, breakpointValue);\n    });\n  } else if (responsive && typeof responsive === 'object') {\n    // prevent null\n    // responsive could be a very big object, pick the smallest responsive values\n\n    const keys = Object.keys(responsive).length > breakpoints.keys.length ? breakpoints.keys : filterBreakpointKeys(breakpoints.keys, Object.keys(responsive));\n    keys.forEach(key => {\n      if (breakpoints.keys.includes(key)) {\n        // @ts-ignore already checked that responsive is an object\n        const breakpointValue = responsive[key];\n        if (breakpointValue !== undefined) {\n          iterator((responsiveStyles, style) => {\n            if (smallestBreakpoint === key) {\n              Object.assign(responsiveStyles, style);\n            } else {\n              responsiveStyles[breakpoints.up(key)] = style;\n            }\n          }, breakpointValue);\n        }\n      }\n    });\n  } else if (typeof responsive === 'number' || typeof responsive === 'string') {\n    iterator((responsiveStyles, style) => {\n      Object.assign(responsiveStyles, style);\n    }, responsive);\n  }\n};"], "names": [], "mappings": ";;;;AAAO,MAAM,uBAAuB,CAAC,iBAAiB,iBAAmB,gBAAgB,MAAM,CAAC,CAAA,MAAO,eAAe,QAAQ,CAAC;AACxH,MAAM,sBAAsB,CAAC,aAAa,YAAY;IAC3D,MAAM,qBAAqB,YAAY,IAAI,CAAC,EAAE,EAAE,sEAAsE;IAEtH,IAAI,MAAM,OAAO,CAAC,aAAa;QAC7B,WAAW,OAAO,CAAC,CAAC,iBAAiB;YACnC,SAAS,CAAC,kBAAkB;gBAC1B,IAAI,SAAS,YAAY,IAAI,CAAC,MAAM,GAAG,GAAG;oBACxC,IAAI,UAAU,GAAG;wBACf,OAAO,MAAM,CAAC,kBAAkB;oBAClC,OAAO;wBACL,gBAAgB,CAAC,YAAY,EAAE,CAAC,YAAY,IAAI,CAAC,MAAM,EAAE,GAAG;oBAC9D;gBACF;YACF,GAAG;QACL;IACF,OAAO,IAAI,cAAc,OAAO,eAAe,UAAU;QACvD,eAAe;QACf,6EAA6E;QAE7E,MAAM,OAAO,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,YAAY,IAAI,CAAC,MAAM,GAAG,YAAY,IAAI,GAAG,qBAAqB,YAAY,IAAI,EAAE,OAAO,IAAI,CAAC;QAC9I,KAAK,OAAO,CAAC,CAAA;YACX,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,MAAM;gBAClC,0DAA0D;gBAC1D,MAAM,kBAAkB,UAAU,CAAC,IAAI;gBACvC,IAAI,oBAAoB,WAAW;oBACjC,SAAS,CAAC,kBAAkB;wBAC1B,IAAI,uBAAuB,KAAK;4BAC9B,OAAO,MAAM,CAAC,kBAAkB;wBAClC,OAAO;4BACL,gBAAgB,CAAC,YAAY,EAAE,CAAC,KAAK,GAAG;wBAC1C;oBACF,GAAG;gBACL;YACF;QACF;IACF,OAAO,IAAI,OAAO,eAAe,YAAY,OAAO,eAAe,UAAU;QAC3E,SAAS,CAAC,kBAAkB;YAC1B,OAAO,MAAM,CAAC,kBAAkB;QAClC,GAAG;IACL;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/system/esm/Grid/gridGenerator.js"], "sourcesContent": ["import { traverseBreakpoints } from \"./traverseBreakpoints.js\";\nfunction getSelfSpacingVar(axis) {\n  return `--Grid-${axis}Spacing`;\n}\nfunction getParentSpacingVar(axis) {\n  return `--Grid-parent-${axis}Spacing`;\n}\nconst selfColumnsVar = '--Grid-columns';\nconst parentColumnsVar = '--Grid-parent-columns';\nexport const generateGridSizeStyles = ({\n  theme,\n  ownerState\n}) => {\n  const styles = {};\n  traverseBreakpoints(theme.breakpoints, ownerState.size, (appendStyle, value) => {\n    let style = {};\n    if (value === 'grow') {\n      style = {\n        flexBasis: 0,\n        flexGrow: 1,\n        maxWidth: '100%'\n      };\n    }\n    if (value === 'auto') {\n      style = {\n        flexBasis: 'auto',\n        flexGrow: 0,\n        flexShrink: 0,\n        maxWidth: 'none',\n        width: 'auto'\n      };\n    }\n    if (typeof value === 'number') {\n      style = {\n        flexGrow: 0,\n        flexBasis: 'auto',\n        width: `calc(100% * ${value} / var(${parentColumnsVar}) - (var(${parentColumnsVar}) - ${value}) * (var(${getParentSpacingVar('column')}) / var(${parentColumnsVar})))`\n      };\n    }\n    appendStyle(styles, style);\n  });\n  return styles;\n};\nexport const generateGridOffsetStyles = ({\n  theme,\n  ownerState\n}) => {\n  const styles = {};\n  traverseBreakpoints(theme.breakpoints, ownerState.offset, (appendStyle, value) => {\n    let style = {};\n    if (value === 'auto') {\n      style = {\n        marginLeft: 'auto'\n      };\n    }\n    if (typeof value === 'number') {\n      style = {\n        marginLeft: value === 0 ? '0px' : `calc(100% * ${value} / var(${parentColumnsVar}) + var(${getParentSpacingVar('column')}) * ${value} / var(${parentColumnsVar}))`\n      };\n    }\n    appendStyle(styles, style);\n  });\n  return styles;\n};\nexport const generateGridColumnsStyles = ({\n  theme,\n  ownerState\n}) => {\n  if (!ownerState.container) {\n    return {};\n  }\n  const styles = {\n    [selfColumnsVar]: 12\n  };\n  traverseBreakpoints(theme.breakpoints, ownerState.columns, (appendStyle, value) => {\n    const columns = value ?? 12;\n    appendStyle(styles, {\n      [selfColumnsVar]: columns,\n      '> *': {\n        [parentColumnsVar]: columns\n      }\n    });\n  });\n  return styles;\n};\nexport const generateGridRowSpacingStyles = ({\n  theme,\n  ownerState\n}) => {\n  if (!ownerState.container) {\n    return {};\n  }\n  const styles = {};\n  traverseBreakpoints(theme.breakpoints, ownerState.rowSpacing, (appendStyle, value) => {\n    const spacing = typeof value === 'string' ? value : theme.spacing?.(value);\n    appendStyle(styles, {\n      [getSelfSpacingVar('row')]: spacing,\n      '> *': {\n        [getParentSpacingVar('row')]: spacing\n      }\n    });\n  });\n  return styles;\n};\nexport const generateGridColumnSpacingStyles = ({\n  theme,\n  ownerState\n}) => {\n  if (!ownerState.container) {\n    return {};\n  }\n  const styles = {};\n  traverseBreakpoints(theme.breakpoints, ownerState.columnSpacing, (appendStyle, value) => {\n    const spacing = typeof value === 'string' ? value : theme.spacing?.(value);\n    appendStyle(styles, {\n      [getSelfSpacingVar('column')]: spacing,\n      '> *': {\n        [getParentSpacingVar('column')]: spacing\n      }\n    });\n  });\n  return styles;\n};\nexport const generateGridDirectionStyles = ({\n  theme,\n  ownerState\n}) => {\n  if (!ownerState.container) {\n    return {};\n  }\n  const styles = {};\n  traverseBreakpoints(theme.breakpoints, ownerState.direction, (appendStyle, value) => {\n    appendStyle(styles, {\n      flexDirection: value\n    });\n  });\n  return styles;\n};\nexport const generateGridStyles = ({\n  ownerState\n}) => {\n  return {\n    minWidth: 0,\n    boxSizing: 'border-box',\n    ...(ownerState.container && {\n      display: 'flex',\n      flexWrap: 'wrap',\n      ...(ownerState.wrap && ownerState.wrap !== 'wrap' && {\n        flexWrap: ownerState.wrap\n      }),\n      gap: `var(${getSelfSpacingVar('row')}) var(${getSelfSpacingVar('column')})`\n    })\n  };\n};\nexport const generateSizeClassNames = size => {\n  const classNames = [];\n  Object.entries(size).forEach(([key, value]) => {\n    if (value !== false && value !== undefined) {\n      classNames.push(`grid-${key}-${String(value)}`);\n    }\n  });\n  return classNames;\n};\nexport const generateSpacingClassNames = (spacing, smallestBreakpoint = 'xs') => {\n  function isValidSpacing(val) {\n    if (val === undefined) {\n      return false;\n    }\n    return typeof val === 'string' && !Number.isNaN(Number(val)) || typeof val === 'number' && val > 0;\n  }\n  if (isValidSpacing(spacing)) {\n    return [`spacing-${smallestBreakpoint}-${String(spacing)}`];\n  }\n  if (typeof spacing === 'object' && !Array.isArray(spacing)) {\n    const classNames = [];\n    Object.entries(spacing).forEach(([key, value]) => {\n      if (isValidSpacing(value)) {\n        classNames.push(`spacing-${key}-${String(value)}`);\n      }\n    });\n    return classNames;\n  }\n  return [];\n};\nexport const generateDirectionClasses = direction => {\n  if (direction === undefined) {\n    return [];\n  }\n  if (typeof direction === 'object') {\n    return Object.entries(direction).map(([key, value]) => `direction-${key}-${value}`);\n  }\n  return [`direction-xs-${String(direction)}`];\n};"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AACA,SAAS,kBAAkB,IAAI;IAC7B,OAAO,CAAC,OAAO,EAAE,KAAK,OAAO,CAAC;AAChC;AACA,SAAS,oBAAoB,IAAI;IAC/B,OAAO,CAAC,cAAc,EAAE,KAAK,OAAO,CAAC;AACvC;AACA,MAAM,iBAAiB;AACvB,MAAM,mBAAmB;AAClB,MAAM,yBAAyB,CAAC,EACrC,KAAK,EACL,UAAU,EACX;IACC,MAAM,SAAS,CAAC;IAChB,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,WAAW,EAAE,WAAW,IAAI,EAAE,CAAC,aAAa;QACpE,IAAI,QAAQ,CAAC;QACb,IAAI,UAAU,QAAQ;YACpB,QAAQ;gBACN,WAAW;gBACX,UAAU;gBACV,UAAU;YACZ;QACF;QACA,IAAI,UAAU,QAAQ;YACpB,QAAQ;gBACN,WAAW;gBACX,UAAU;gBACV,YAAY;gBACZ,UAAU;gBACV,OAAO;YACT;QACF;QACA,IAAI,OAAO,UAAU,UAAU;YAC7B,QAAQ;gBACN,UAAU;gBACV,WAAW;gBACX,OAAO,CAAC,YAAY,EAAE,MAAM,OAAO,EAAE,iBAAiB,SAAS,EAAE,iBAAiB,IAAI,EAAE,MAAM,SAAS,EAAE,oBAAoB,UAAU,QAAQ,EAAE,iBAAiB,GAAG,CAAC;YACxK;QACF;QACA,YAAY,QAAQ;IACtB;IACA,OAAO;AACT;AACO,MAAM,2BAA2B,CAAC,EACvC,KAAK,EACL,UAAU,EACX;IACC,MAAM,SAAS,CAAC;IAChB,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,WAAW,EAAE,WAAW,MAAM,EAAE,CAAC,aAAa;QACtE,IAAI,QAAQ,CAAC;QACb,IAAI,UAAU,QAAQ;YACpB,QAAQ;gBACN,YAAY;YACd;QACF;QACA,IAAI,OAAO,UAAU,UAAU;YAC7B,QAAQ;gBACN,YAAY,UAAU,IAAI,QAAQ,CAAC,YAAY,EAAE,MAAM,OAAO,EAAE,iBAAiB,QAAQ,EAAE,oBAAoB,UAAU,IAAI,EAAE,MAAM,OAAO,EAAE,iBAAiB,EAAE,CAAC;YACpK;QACF;QACA,YAAY,QAAQ;IACtB;IACA,OAAO;AACT;AACO,MAAM,4BAA4B,CAAC,EACxC,KAAK,EACL,UAAU,EACX;IACC,IAAI,CAAC,WAAW,SAAS,EAAE;QACzB,OAAO,CAAC;IACV;IACA,MAAM,SAAS;QACb,CAAC,eAAe,EAAE;IACpB;IACA,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,WAAW,EAAE,WAAW,OAAO,EAAE,CAAC,aAAa;QACvE,MAAM,UAAU,SAAS;QACzB,YAAY,QAAQ;YAClB,CAAC,eAAe,EAAE;YAClB,OAAO;gBACL,CAAC,iBAAiB,EAAE;YACtB;QACF;IACF;IACA,OAAO;AACT;AACO,MAAM,+BAA+B,CAAC,EAC3C,KAAK,EACL,UAAU,EACX;IACC,IAAI,CAAC,WAAW,SAAS,EAAE;QACzB,OAAO,CAAC;IACV;IACA,MAAM,SAAS,CAAC;IAChB,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,WAAW,EAAE,WAAW,UAAU,EAAE,CAAC,aAAa;QAC1E,MAAM,UAAU,OAAO,UAAU,WAAW,QAAQ,MAAM,OAAO,GAAG;QACpE,YAAY,QAAQ;YAClB,CAAC,kBAAkB,OAAO,EAAE;YAC5B,OAAO;gBACL,CAAC,oBAAoB,OAAO,EAAE;YAChC;QACF;IACF;IACA,OAAO;AACT;AACO,MAAM,kCAAkC,CAAC,EAC9C,KAAK,EACL,UAAU,EACX;IACC,IAAI,CAAC,WAAW,SAAS,EAAE;QACzB,OAAO,CAAC;IACV;IACA,MAAM,SAAS,CAAC;IAChB,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,WAAW,EAAE,WAAW,aAAa,EAAE,CAAC,aAAa;QAC7E,MAAM,UAAU,OAAO,UAAU,WAAW,QAAQ,MAAM,OAAO,GAAG;QACpE,YAAY,QAAQ;YAClB,CAAC,kBAAkB,UAAU,EAAE;YAC/B,OAAO;gBACL,CAAC,oBAAoB,UAAU,EAAE;YACnC;QACF;IACF;IACA,OAAO;AACT;AACO,MAAM,8BAA8B,CAAC,EAC1C,KAAK,EACL,UAAU,EACX;IACC,IAAI,CAAC,WAAW,SAAS,EAAE;QACzB,OAAO,CAAC;IACV;IACA,MAAM,SAAS,CAAC;IAChB,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,WAAW,EAAE,WAAW,SAAS,EAAE,CAAC,aAAa;QACzE,YAAY,QAAQ;YAClB,eAAe;QACjB;IACF;IACA,OAAO;AACT;AACO,MAAM,qBAAqB,CAAC,EACjC,UAAU,EACX;IACC,OAAO;QACL,UAAU;QACV,WAAW;QACX,GAAI,WAAW,SAAS,IAAI;YAC1B,SAAS;YACT,UAAU;YACV,GAAI,WAAW,IAAI,IAAI,WAAW,IAAI,KAAK,UAAU;gBACnD,UAAU,WAAW,IAAI;YAC3B,CAAC;YACD,KAAK,CAAC,IAAI,EAAE,kBAAkB,OAAO,MAAM,EAAE,kBAAkB,UAAU,CAAC,CAAC;QAC7E,CAAC;IACH;AACF;AACO,MAAM,yBAAyB,CAAA;IACpC,MAAM,aAAa,EAAE;IACrB,OAAO,OAAO,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QACxC,IAAI,UAAU,SAAS,UAAU,WAAW;YAC1C,WAAW,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,OAAO,QAAQ;QAChD;IACF;IACA,OAAO;AACT;AACO,MAAM,4BAA4B,CAAC,SAAS,qBAAqB,IAAI;IAC1E,SAAS,eAAe,GAAG;QACzB,IAAI,QAAQ,WAAW;YACrB,OAAO;QACT;QACA,OAAO,OAAO,QAAQ,YAAY,CAAC,OAAO,KAAK,CAAC,OAAO,SAAS,OAAO,QAAQ,YAAY,MAAM;IACnG;IACA,IAAI,eAAe,UAAU;QAC3B,OAAO;YAAC,CAAC,QAAQ,EAAE,mBAAmB,CAAC,EAAE,OAAO,UAAU;SAAC;IAC7D;IACA,IAAI,OAAO,YAAY,YAAY,CAAC,MAAM,OAAO,CAAC,UAAU;QAC1D,MAAM,aAAa,EAAE;QACrB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,eAAe,QAAQ;gBACzB,WAAW,IAAI,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,OAAO,QAAQ;YACnD;QACF;QACA,OAAO;IACT;IACA,OAAO,EAAE;AACX;AACO,MAAM,2BAA2B,CAAA;IACtC,IAAI,cAAc,WAAW;QAC3B,OAAO,EAAE;IACX;IACA,IAAI,OAAO,cAAc,UAAU;QACjC,OAAO,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,OAAO;IACpF;IACA,OAAO;QAAC,CAAC,aAAa,EAAE,OAAO,YAAY;KAAC;AAC9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/system/esm/Grid/deleteLegacyGridProps.js"], "sourcesContent": ["const getLegacyGridWarning = propName => {\n  if (['item', 'zeroMinWidth'].includes(propName)) {\n    return `The \\`${propName}\\` prop has been removed and is no longer necessary. You can safely remove it.`;\n  }\n\n  // #host-reference\n  return `The \\`${propName}\\` prop has been removed. See https://mui.com/material-ui/migration/upgrade-to-grid-v2/ for migration instructions.`;\n};\nconst warnedAboutProps = [];\n\n/**\n * Deletes the legacy Grid component props from the `props` object and warns once about them if found.\n *\n * @param {object} props The props object to remove the legacy Grid props from.\n * @param {Breakpoints} breakpoints The breakpoints object.\n */\nexport default function deleteLegacyGridProps(props, breakpoints) {\n  const propsToWarn = [];\n  if (props.item !== undefined) {\n    delete props.item;\n    propsToWarn.push('item');\n  }\n  if (props.zeroMinWidth !== undefined) {\n    delete props.zeroMinWidth;\n    propsToWarn.push('zeroMinWidth');\n  }\n  breakpoints.keys.forEach(breakpoint => {\n    if (props[breakpoint] !== undefined) {\n      propsToWarn.push(breakpoint);\n      delete props[breakpoint];\n    }\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    propsToWarn.forEach(prop => {\n      if (!warnedAboutProps.includes(prop)) {\n        warnedAboutProps.push(prop);\n        console.warn(`MUI Grid: ${getLegacyGridWarning(prop)}\\n`);\n      }\n    });\n  }\n}"], "names": [], "mappings": ";;;AAgCM;AAhCN,MAAM,uBAAuB,CAAA;IAC3B,IAAI;QAAC;QAAQ;KAAe,CAAC,QAAQ,CAAC,WAAW;QAC/C,OAAO,CAAC,MAAM,EAAE,SAAS,8EAA8E,CAAC;IAC1G;IAEA,kBAAkB;IAClB,OAAO,CAAC,MAAM,EAAE,SAAS,mHAAmH,CAAC;AAC/I;AACA,MAAM,mBAAmB,EAAE;AAQZ,SAAS,sBAAsB,KAAK,EAAE,WAAW;IAC9D,MAAM,cAAc,EAAE;IACtB,IAAI,MAAM,IAAI,KAAK,WAAW;QAC5B,OAAO,MAAM,IAAI;QACjB,YAAY,IAAI,CAAC;IACnB;IACA,IAAI,MAAM,YAAY,KAAK,WAAW;QACpC,OAAO,MAAM,YAAY;QACzB,YAAY,IAAI,CAAC;IACnB;IACA,YAAY,IAAI,CAAC,OAAO,CAAC,CAAA;QACvB,IAAI,KAAK,CAAC,WAAW,KAAK,WAAW;YACnC,YAAY,IAAI,CAAC;YACjB,OAAO,KAAK,CAAC,WAAW;QAC1B;IACF;IACA,wCAA2C;QACzC,YAAY,OAAO,CAAC,CAAA;YAClB,IAAI,CAAC,iBAAiB,QAAQ,CAAC,OAAO;gBACpC,iBAAiB,IAAI,CAAC;gBACtB,QAAQ,IAAI,CAAC,CAAC,UAAU,EAAE,qBAAqB,MAAM,EAAE,CAAC;YAC1D;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1160, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/system/esm/Grid/createGrid.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport isMuiElement from '@mui/utils/isMuiElement';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport composeClasses from '@mui/utils/composeClasses';\nimport systemStyled from \"../styled/index.js\";\nimport useThemePropsSystem from \"../useThemeProps/index.js\";\nimport useThemeSystem from \"../useTheme/index.js\";\nimport { extendSxProp } from \"../styleFunctionSx/index.js\";\nimport createTheme from \"../createTheme/index.js\";\nimport { generateGridStyles, generateGridSizeStyles, generateGridColumnsStyles, generateGridColumnSpacingStyles, generateGridRowSpacingStyles, generateGridDirectionStyles, generateGridOffsetStyles, generateSizeClassNames, generateSpacingClassNames, generateDirectionClasses } from \"./gridGenerator.js\";\nimport deleteLegacyGridProps from \"./deleteLegacyGridProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultTheme = createTheme();\n\n// widening Theme to any so that the consumer can own the theme structure.\nconst defaultCreateStyledComponent = systemStyled('div', {\n  name: 'MuiGrid',\n  slot: 'Root'\n});\nfunction useThemePropsDefault(props) {\n  return useThemePropsSystem({\n    props,\n    name: 'MuiGrid',\n    defaultTheme\n  });\n}\nexport default function createGrid(options = {}) {\n  const {\n    // This will allow adding custom styled fn (for example for custom sx style function)\n    createStyledComponent = defaultCreateStyledComponent,\n    useThemeProps = useThemePropsDefault,\n    useTheme = useThemeSystem,\n    componentName = 'MuiGrid'\n  } = options;\n  const useUtilityClasses = (ownerState, theme) => {\n    const {\n      container,\n      direction,\n      spacing,\n      wrap,\n      size\n    } = ownerState;\n    const slots = {\n      root: ['root', container && 'container', wrap !== 'wrap' && `wrap-xs-${String(wrap)}`, ...generateDirectionClasses(direction), ...generateSizeClassNames(size), ...(container ? generateSpacingClassNames(spacing, theme.breakpoints.keys[0]) : [])]\n    };\n    return composeClasses(slots, slot => generateUtilityClass(componentName, slot), {});\n  };\n  function parseResponsiveProp(propValue, breakpoints, shouldUseValue = () => true) {\n    const parsedProp = {};\n    if (propValue === null) {\n      return parsedProp;\n    }\n    if (Array.isArray(propValue)) {\n      propValue.forEach((value, index) => {\n        if (value !== null && shouldUseValue(value) && breakpoints.keys[index]) {\n          parsedProp[breakpoints.keys[index]] = value;\n        }\n      });\n    } else if (typeof propValue === 'object') {\n      Object.keys(propValue).forEach(key => {\n        const value = propValue[key];\n        if (value !== null && value !== undefined && shouldUseValue(value)) {\n          parsedProp[key] = value;\n        }\n      });\n    } else {\n      parsedProp[breakpoints.keys[0]] = propValue;\n    }\n    return parsedProp;\n  }\n  const GridRoot = createStyledComponent(generateGridColumnsStyles, generateGridColumnSpacingStyles, generateGridRowSpacingStyles, generateGridSizeStyles, generateGridDirectionStyles, generateGridStyles, generateGridOffsetStyles);\n  const Grid = /*#__PURE__*/React.forwardRef(function Grid(inProps, ref) {\n    const theme = useTheme();\n    const themeProps = useThemeProps(inProps);\n    const props = extendSxProp(themeProps); // `color` type conflicts with html color attribute.\n\n    // TODO v8: Remove when removing the legacy Grid component\n    deleteLegacyGridProps(props, theme.breakpoints);\n    const {\n      className,\n      children,\n      columns: columnsProp = 12,\n      container = false,\n      component = 'div',\n      direction = 'row',\n      wrap = 'wrap',\n      size: sizeProp = {},\n      offset: offsetProp = {},\n      spacing: spacingProp = 0,\n      rowSpacing: rowSpacingProp = spacingProp,\n      columnSpacing: columnSpacingProp = spacingProp,\n      unstable_level: level = 0,\n      ...other\n    } = props;\n    const size = parseResponsiveProp(sizeProp, theme.breakpoints, val => val !== false);\n    const offset = parseResponsiveProp(offsetProp, theme.breakpoints);\n    const columns = inProps.columns ?? (level ? undefined : columnsProp);\n    const spacing = inProps.spacing ?? (level ? undefined : spacingProp);\n    const rowSpacing = inProps.rowSpacing ?? inProps.spacing ?? (level ? undefined : rowSpacingProp);\n    const columnSpacing = inProps.columnSpacing ?? inProps.spacing ?? (level ? undefined : columnSpacingProp);\n    const ownerState = {\n      ...props,\n      level,\n      columns,\n      container,\n      direction,\n      wrap,\n      spacing,\n      rowSpacing,\n      columnSpacing,\n      size,\n      offset\n    };\n    const classes = useUtilityClasses(ownerState, theme);\n    return /*#__PURE__*/_jsx(GridRoot, {\n      ref: ref,\n      as: component,\n      ownerState: ownerState,\n      className: clsx(classes.root, className),\n      ...other,\n      children: React.Children.map(children, child => {\n        if (/*#__PURE__*/React.isValidElement(child) && isMuiElement(child, ['Grid']) && container && child.props.container) {\n          return /*#__PURE__*/React.cloneElement(child, {\n            unstable_level: child.props?.unstable_level ?? level + 1\n          });\n        }\n        return child;\n      })\n    });\n  });\n  process.env.NODE_ENV !== \"production\" ? Grid.propTypes /* remove-proptypes */ = {\n    children: PropTypes.node,\n    className: PropTypes.string,\n    columns: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.number), PropTypes.number, PropTypes.object]),\n    columnSpacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n    component: PropTypes.elementType,\n    container: PropTypes.bool,\n    direction: PropTypes.oneOfType([PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row']), PropTypes.arrayOf(PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row'])), PropTypes.object]),\n    offset: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])), PropTypes.object]),\n    rowSpacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n    size: PropTypes.oneOfType([PropTypes.string, PropTypes.bool, PropTypes.number, PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.bool, PropTypes.number])), PropTypes.object]),\n    spacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    wrap: PropTypes.oneOf(['nowrap', 'wrap-reverse', 'wrap'])\n  } : void 0;\n\n  // @ts-ignore internal logic for nested grid\n  Grid.muiName = 'Grid';\n  return Grid;\n}"], "names": [], "mappings": ";;;AAsIE;AApIF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;;;;AAgBA,MAAM,eAAe,CAAA,GAAA,uKAAA,CAAA,UAAW,AAAD;AAE/B,0EAA0E;AAC1E,MAAM,+BAA+B,CAAA,GAAA,6JAAA,CAAA,UAAY,AAAD,EAAE,OAAO;IACvD,MAAM;IACN,MAAM;AACR;AACA,SAAS,qBAAqB,KAAK;IACjC,OAAO,CAAA,GAAA,2KAAA,CAAA,UAAmB,AAAD,EAAE;QACzB;QACA,MAAM;QACN;IACF;AACF;AACe,SAAS,WAAW,UAAU,CAAC,CAAC;IAC7C,MAAM,EACJ,qFAAqF;IACrF,wBAAwB,4BAA4B,EACpD,gBAAgB,oBAAoB,EACpC,WAAW,iKAAA,CAAA,UAAc,EACzB,gBAAgB,SAAS,EAC1B,GAAG;IACJ,MAAM,oBAAoB,CAAC,YAAY;QACrC,MAAM,EACJ,SAAS,EACT,SAAS,EACT,OAAO,EACP,IAAI,EACJ,IAAI,EACL,GAAG;QACJ,MAAM,QAAQ;YACZ,MAAM;gBAAC;gBAAQ,aAAa;gBAAa,SAAS,UAAU,CAAC,QAAQ,EAAE,OAAO,OAAO;mBAAK,CAAA,GAAA,kKAAA,CAAA,2BAAwB,AAAD,EAAE;mBAAe,CAAA,GAAA,kKAAA,CAAA,yBAAsB,AAAD,EAAE;mBAAW,YAAY,CAAA,GAAA,kKAAA,CAAA,4BAAyB,AAAD,EAAE,SAAS,MAAM,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE;aAAE;QACtP;QACA,OAAO,CAAA,GAAA,4KAAA,CAAA,UAAc,AAAD,EAAE,OAAO,CAAA,OAAQ,CAAA,GAAA,wLAAA,CAAA,UAAoB,AAAD,EAAE,eAAe,OAAO,CAAC;IACnF;IACA,SAAS,oBAAoB,SAAS,EAAE,WAAW,EAAE,iBAAiB,IAAM,IAAI;QAC9E,MAAM,aAAa,CAAC;QACpB,IAAI,cAAc,MAAM;YACtB,OAAO;QACT;QACA,IAAI,MAAM,OAAO,CAAC,YAAY;YAC5B,UAAU,OAAO,CAAC,CAAC,OAAO;gBACxB,IAAI,UAAU,QAAQ,eAAe,UAAU,YAAY,IAAI,CAAC,MAAM,EAAE;oBACtE,UAAU,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,GAAG;gBACxC;YACF;QACF,OAAO,IAAI,OAAO,cAAc,UAAU;YACxC,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,CAAA;gBAC7B,MAAM,QAAQ,SAAS,CAAC,IAAI;gBAC5B,IAAI,UAAU,QAAQ,UAAU,aAAa,eAAe,QAAQ;oBAClE,UAAU,CAAC,IAAI,GAAG;gBACpB;YACF;QACF,OAAO;YACL,UAAU,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG;QACpC;QACA,OAAO;IACT;IACA,MAAM,WAAW,sBAAsB,kKAAA,CAAA,4BAAyB,EAAE,kKAAA,CAAA,kCAA+B,EAAE,kKAAA,CAAA,+BAA4B,EAAE,kKAAA,CAAA,yBAAsB,EAAE,kKAAA,CAAA,8BAA2B,EAAE,kKAAA,CAAA,qBAAkB,EAAE,kKAAA,CAAA,2BAAwB;IAClO,MAAM,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,KAAK,OAAO,EAAE,GAAG;QACnE,MAAM,QAAQ;QACd,MAAM,aAAa,cAAc;QACjC,MAAM,QAAQ,CAAA,GAAA,uNAAA,CAAA,eAAY,AAAD,EAAE,aAAa,oDAAoD;QAE5F,0DAA0D;QAC1D,CAAA,GAAA,0KAAA,CAAA,UAAqB,AAAD,EAAE,OAAO,MAAM,WAAW;QAC9C,MAAM,EACJ,SAAS,EACT,QAAQ,EACR,SAAS,cAAc,EAAE,EACzB,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,OAAO,MAAM,EACb,MAAM,WAAW,CAAC,CAAC,EACnB,QAAQ,aAAa,CAAC,CAAC,EACvB,SAAS,cAAc,CAAC,EACxB,YAAY,iBAAiB,WAAW,EACxC,eAAe,oBAAoB,WAAW,EAC9C,gBAAgB,QAAQ,CAAC,EACzB,GAAG,OACJ,GAAG;QACJ,MAAM,OAAO,oBAAoB,UAAU,MAAM,WAAW,EAAE,CAAA,MAAO,QAAQ;QAC7E,MAAM,SAAS,oBAAoB,YAAY,MAAM,WAAW;QAChE,MAAM,UAAU,QAAQ,OAAO,IAAI,CAAC,QAAQ,YAAY,WAAW;QACnE,MAAM,UAAU,QAAQ,OAAO,IAAI,CAAC,QAAQ,YAAY,WAAW;QACnE,MAAM,aAAa,QAAQ,UAAU,IAAI,QAAQ,OAAO,IAAI,CAAC,QAAQ,YAAY,cAAc;QAC/F,MAAM,gBAAgB,QAAQ,aAAa,IAAI,QAAQ,OAAO,IAAI,CAAC,QAAQ,YAAY,iBAAiB;QACxG,MAAM,aAAa;YACjB,GAAG,KAAK;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;QACA,MAAM,UAAU,kBAAkB,YAAY;QAC9C,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,UAAU;YACjC,KAAK;YACL,IAAI;YACJ,YAAY;YACZ,WAAW,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,QAAQ,IAAI,EAAE;YAC9B,GAAG,KAAK;YACR,UAAU,6JAAA,CAAA,WAAc,CAAC,GAAG,CAAC,UAAU,CAAA;gBACrC,IAAI,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,iBAAoB,AAAD,EAAE,UAAU,CAAA,GAAA,wKAAA,CAAA,UAAY,AAAD,EAAE,OAAO;oBAAC;iBAAO,KAAK,aAAa,MAAM,KAAK,CAAC,SAAS,EAAE;oBACnH,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,eAAkB,AAAD,EAAE,OAAO;wBAC5C,gBAAgB,MAAM,KAAK,EAAE,kBAAkB,QAAQ;oBACzD;gBACF;gBACA,OAAO;YACT;QACF;IACF;IACA,uCAAwC,KAAK,SAAS,GAA0B;QAC9E,UAAU,yIAAA,CAAA,UAAS,CAAC,IAAI;QACxB,WAAW,yIAAA,CAAA,UAAS,CAAC,MAAM;QAC3B,SAAS,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,yIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,yIAAA,CAAA,UAAS,CAAC,MAAM;YAAG,yIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QACtG,eAAe,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,yIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;gBAAC,yIAAA,CAAA,UAAS,CAAC,MAAM;gBAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;aAAC;YAAI,yIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QACvK,WAAW,yIAAA,CAAA,UAAS,CAAC,WAAW;QAChC,WAAW,yIAAA,CAAA,UAAS,CAAC,IAAI;QACzB,WAAW,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;gBAAC;gBAAkB;gBAAU;gBAAe;aAAM;YAAG,yIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;gBAAC;gBAAkB;gBAAU;gBAAe;aAAM;YAAI,yIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QAC9M,QAAQ,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,yIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,yIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;gBAAC,yIAAA,CAAA,UAAS,CAAC,MAAM;gBAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;aAAC;YAAI,yIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QAChK,YAAY,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,yIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;gBAAC,yIAAA,CAAA,UAAS,CAAC,MAAM;gBAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;aAAC;YAAI,yIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QACpK,MAAM,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,yIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,yIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,yIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;gBAAC,yIAAA,CAAA,UAAS,CAAC,MAAM;gBAAE,yIAAA,CAAA,UAAS,CAAC,IAAI;gBAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;aAAC;YAAI,yIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QAC9L,SAAS,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,yIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;gBAAC,yIAAA,CAAA,UAAS,CAAC,MAAM;gBAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;aAAC;YAAI,yIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;YAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QACjK,IAAI,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;YAAC,yIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;gBAAC,yIAAA,CAAA,UAAS,CAAC,IAAI;gBAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;gBAAE,yIAAA,CAAA,UAAS,CAAC,IAAI;aAAC;YAAI,yIAAA,CAAA,UAAS,CAAC,IAAI;YAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;SAAC;QACtJ,MAAM,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;YAAC;YAAU;YAAgB;SAAO;IAC1D;IAEA,4CAA4C;IAC5C,KAAK,OAAO,GAAG;IACf,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1409, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/system/esm/memoTheme.js"], "sourcesContent": ["import preprocessStyles from \"./preprocessStyles.js\";\n\n/* eslint-disable @typescript-eslint/naming-convention */\n\n// We need to pass an argument as `{ theme }` for PigmentCSS, but we don't want to\n// allocate more objects.\nconst arg = {\n  theme: undefined\n};\n\n/**\n * Memoize style function on theme.\n * Intended to be used in styled() calls that only need access to the theme.\n */\nexport default function unstable_memoTheme(styleFn) {\n  let lastValue;\n  let lastTheme;\n  return function styleMemoized(props) {\n    let value = lastValue;\n    if (value === undefined || props.theme !== lastTheme) {\n      arg.theme = props.theme;\n      value = preprocessStyles(styleFn(arg));\n      lastValue = value;\n      lastTheme = props.theme;\n    }\n    return value;\n  };\n}"], "names": [], "mappings": ";;;AAAA;;AAEA,uDAAuD,GAEvD,kFAAkF;AAClF,yBAAyB;AACzB,MAAM,MAAM;IACV,OAAO;AACT;AAMe,SAAS,mBAAmB,OAAO;IAChD,IAAI;IACJ,IAAI;IACJ,OAAO,SAAS,cAAc,KAAK;QACjC,IAAI,QAAQ;QACZ,IAAI,UAAU,aAAa,MAAM,KAAK,KAAK,WAAW;YACpD,IAAI,KAAK,GAAG,MAAM,KAAK;YACvB,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAgB,AAAD,EAAE,QAAQ;YACjC,YAAY;YACZ,YAAY,MAAM,KAAK;QACzB;QACA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1459, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/system/esm/useMediaQuery/useMediaQuery.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport { getThemeProps } from \"../useThemeProps/index.js\";\nimport useTheme from \"../useThemeWithoutDefault/index.js\";\n// TODO React 17: Remove `useMediaQueryOld` once React 17 support is removed\nfunction useMediaQueryOld(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr) {\n  const [match, setMatch] = React.useState(() => {\n    if (noSsr && matchMedia) {\n      return matchMedia(query).matches;\n    }\n    if (ssrMatchMedia) {\n      return ssrMatchMedia(query).matches;\n    }\n\n    // Once the component is mounted, we rely on the\n    // event listeners to return the correct matches value.\n    return defaultMatches;\n  });\n  useEnhancedEffect(() => {\n    if (!matchMedia) {\n      return undefined;\n    }\n    const queryList = matchMedia(query);\n    const updateMatch = () => {\n      setMatch(queryList.matches);\n    };\n    updateMatch();\n    queryList.addEventListener('change', updateMatch);\n    return () => {\n      queryList.removeEventListener('change', updateMatch);\n    };\n  }, [query, matchMedia]);\n  return match;\n}\n\n// See https://github.com/mui/material-ui/issues/41190#issuecomment-********** for why\nconst safeReact = {\n  ...React\n};\nconst maybeReactUseSyncExternalStore = safeReact.useSyncExternalStore;\nfunction useMediaQueryNew(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr) {\n  const getDefaultSnapshot = React.useCallback(() => defaultMatches, [defaultMatches]);\n  const getServerSnapshot = React.useMemo(() => {\n    if (noSsr && matchMedia) {\n      return () => matchMedia(query).matches;\n    }\n    if (ssrMatchMedia !== null) {\n      const {\n        matches\n      } = ssrMatchMedia(query);\n      return () => matches;\n    }\n    return getDefaultSnapshot;\n  }, [getDefaultSnapshot, query, ssrMatchMedia, noSsr, matchMedia]);\n  const [getSnapshot, subscribe] = React.useMemo(() => {\n    if (matchMedia === null) {\n      return [getDefaultSnapshot, () => () => {}];\n    }\n    const mediaQueryList = matchMedia(query);\n    return [() => mediaQueryList.matches, notify => {\n      mediaQueryList.addEventListener('change', notify);\n      return () => {\n        mediaQueryList.removeEventListener('change', notify);\n      };\n    }];\n  }, [getDefaultSnapshot, matchMedia, query]);\n  const match = maybeReactUseSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n  return match;\n}\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function unstable_createUseMediaQuery(params = {}) {\n  const {\n    themeId\n  } = params;\n  return function useMediaQuery(queryInput, options = {}) {\n    let theme = useTheme();\n    if (theme && themeId) {\n      theme = theme[themeId] || theme;\n    }\n    // Wait for jsdom to support the match media feature.\n    // All the browsers MUI support have this built-in.\n    // This defensive check is here for simplicity.\n    // Most of the time, the match media logic isn't central to people tests.\n    const supportMatchMedia = typeof window !== 'undefined' && typeof window.matchMedia !== 'undefined';\n    const {\n      defaultMatches = false,\n      matchMedia = supportMatchMedia ? window.matchMedia : null,\n      ssrMatchMedia = null,\n      noSsr = false\n    } = getThemeProps({\n      name: 'MuiUseMediaQuery',\n      props: options,\n      theme\n    });\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof queryInput === 'function' && theme === null) {\n        console.error(['MUI: The `query` argument provided is invalid.', 'You are providing a function without a theme in the context.', 'One of the parent elements needs to use a ThemeProvider.'].join('\\n'));\n      }\n    }\n    let query = typeof queryInput === 'function' ? queryInput(theme) : queryInput;\n    query = query.replace(/^@media( ?)/m, '');\n    if (query.includes('print')) {\n      console.warn([`MUI: You have provided a \\`print\\` query to the \\`useMediaQuery\\` hook.`, 'Using the print media query to modify print styles can lead to unexpected results.', 'Consider using the `displayPrint` field in the `sx` prop instead.', 'More information about `displayPrint` on our docs: https://mui.com/system/display/#display-in-print.'].join('\\n'));\n    }\n    const useMediaQueryImplementation = maybeReactUseSyncExternalStore !== undefined ? useMediaQueryNew : useMediaQueryOld;\n    const match = useMediaQueryImplementation(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr);\n    if (process.env.NODE_ENV !== 'production') {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      React.useDebugValue({\n        query,\n        match\n      });\n    }\n    return match;\n  };\n}\nconst useMediaQuery = unstable_createUseMediaQuery();\nexport default useMediaQuery;"], "names": [], "mappings": ";;;;AAiGQ;AA/FR;AACA;AACA;AACA;AALA;;;;;AAMA,4EAA4E;AAC5E,SAAS,iBAAiB,KAAK,EAAE,cAAc,EAAE,UAAU,EAAE,aAAa,EAAE,KAAK;IAC/E,MAAM,CAAC,OAAO,SAAS,GAAG,8JAAM,QAAQ;qCAAC;YACvC,IAAI,SAAS,YAAY;gBACvB,OAAO,WAAW,OAAO,OAAO;YAClC;YACA,IAAI,eAAe;gBACjB,OAAO,cAAc,OAAO,OAAO;YACrC;YAEA,gDAAgD;YAChD,uDAAuD;YACvD,OAAO;QACT;;IACA,CAAA,GAAA,kLAAA,CAAA,UAAiB,AAAD;8CAAE;YAChB,IAAI,CAAC,YAAY;gBACf,OAAO;YACT;YACA,MAAM,YAAY,WAAW;YAC7B,MAAM;kEAAc;oBAClB,SAAS,UAAU,OAAO;gBAC5B;;YACA;YACA,UAAU,gBAAgB,CAAC,UAAU;YACrC;sDAAO;oBACL,UAAU,mBAAmB,CAAC,UAAU;gBAC1C;;QACF;6CAAG;QAAC;QAAO;KAAW;IACtB,OAAO;AACT;AAEA,sFAAsF;AACtF,MAAM,YAAY;IAChB,GAAG,6JAAK;AACV;AACA,MAAM,iCAAiC,UAAU,oBAAoB;AACrE,SAAS,iBAAiB,KAAK,EAAE,cAAc,EAAE,UAAU,EAAE,aAAa,EAAE,KAAK;IAC/E,MAAM,qBAAqB,8JAAM,WAAW;4DAAC,IAAM;2DAAgB;QAAC;KAAe;IACnF,MAAM,oBAAoB,8JAAM,OAAO;uDAAC;YACtC,IAAI,SAAS,YAAY;gBACvB;mEAAO,IAAM,WAAW,OAAO,OAAO;;YACxC;YACA,IAAI,kBAAkB,MAAM;gBAC1B,MAAM,EACJ,OAAO,EACR,GAAG,cAAc;gBAClB;mEAAO,IAAM;;YACf;YACA,OAAO;QACT;sDAAG;QAAC;QAAoB;QAAO;QAAe;QAAO;KAAW;IAChE,MAAM,CAAC,aAAa,UAAU,GAAG,8JAAM,OAAO;oCAAC;YAC7C,IAAI,eAAe,MAAM;gBACvB,OAAO;oBAAC;;oDAAoB;4DAAM,KAAO;;;iBAAE;YAC7C;YACA,MAAM,iBAAiB,WAAW;YAClC,OAAO;;gDAAC,IAAM,eAAe,OAAO;;;gDAAE,CAAA;wBACpC,eAAe,gBAAgB,CAAC,UAAU;wBAC1C;wDAAO;gCACL,eAAe,mBAAmB,CAAC,UAAU;4BAC/C;;oBACF;;aAAE;QACJ;mCAAG;QAAC;QAAoB;QAAY;KAAM;IAC1C,MAAM,QAAQ,+BAA+B,WAAW,aAAa;IACrE,OAAO;AACT;AAGO,SAAS,6BAA6B,SAAS,CAAC,CAAC;IACtD,MAAM,EACJ,OAAO,EACR,GAAG;IACJ,OAAO,SAAS,cAAc,UAAU,EAAE,UAAU,CAAC,CAAC;QACpD,IAAI,QAAQ,CAAA,GAAA,6LAAA,CAAA,UAAQ,AAAD;QACnB,IAAI,SAAS,SAAS;YACpB,QAAQ,KAAK,CAAC,QAAQ,IAAI;QAC5B;QACA,qDAAqD;QACrD,mDAAmD;QACnD,+CAA+C;QAC/C,yEAAyE;QACzE,MAAM,oBAAoB,OAAO,WAAW,eAAe,OAAO,OAAO,UAAU,KAAK;QACxF,MAAM,EACJ,iBAAiB,KAAK,EACtB,aAAa,oBAAoB,OAAO,UAAU,GAAG,IAAI,EACzD,gBAAgB,IAAI,EACpB,QAAQ,KAAK,EACd,GAAG,CAAA,GAAA,uNAAA,CAAA,gBAAa,AAAD,EAAE;YAChB,MAAM;YACN,OAAO;YACP;QACF;QACA,wCAA2C;YACzC,IAAI,OAAO,eAAe,cAAc,UAAU,MAAM;gBACtD,QAAQ,KAAK,CAAC;oBAAC;oBAAkD;oBAAgE;iBAA2D,CAAC,IAAI,CAAC;YACpM;QACF;QACA,IAAI,QAAQ,OAAO,eAAe,aAAa,WAAW,SAAS;QACnE,QAAQ,MAAM,OAAO,CAAC,gBAAgB;QACtC,IAAI,MAAM,QAAQ,CAAC,UAAU;YAC3B,QAAQ,IAAI,CAAC;gBAAC,CAAC,uEAAuE,CAAC;gBAAE;gBAAsF;gBAAqE;aAAuG,CAAC,IAAI,CAAC;QACnW;QACA,MAAM,8BAA8B,mCAAmC,YAAY,mBAAmB;QACtG,MAAM,QAAQ,4BAA4B,OAAO,gBAAgB,YAAY,eAAe;QAC5F,wCAA2C;YACzC,sDAAsD;YACtD,8JAAM,aAAa,CAAC;gBAClB;gBACA;YACF;QACF;QACA,OAAO;IACT;AACF;AACA,MAAM,gBAAgB;uCACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1649, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/generateUtilityClasses/generateUtilityClasses.js"], "sourcesContent": ["import generateUtilityClass from \"../generateUtilityClass/index.js\";\nexport default function generateUtilityClasses(componentName, slots, globalStatePrefix = 'Mui') {\n  const result = {};\n  slots.forEach(slot => {\n    result[slot] = generateUtilityClass(componentName, slot, globalStatePrefix);\n  });\n  return result;\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,uBAAuB,aAAa,EAAE,KAAK,EAAE,oBAAoB,KAAK;IAC5F,MAAM,SAAS,CAAC;IAChB,MAAM,OAAO,CAAC,CAAA;QACZ,MAAM,CAAC,KAAK,GAAG,CAAA,GAAA,wLAAA,CAAA,UAAoB,AAAD,EAAE,eAAe,MAAM;IAC3D;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/isMuiElement/isMuiElement.js"], "sourcesContent": ["import * as React from 'react';\nexport default function isMuiElement(element, muiNames) {\n  return /*#__PURE__*/React.isValidElement(element) && muiNames.indexOf(\n  // For server components `muiName` is avaialble in element.type._payload.value.muiName\n  // relevant info - https://github.com/facebook/react/blob/2807d781a08db8e9873687fccc25c0f12b4fb3d4/packages/react/src/ReactLazy.js#L45\n  // eslint-disable-next-line no-underscore-dangle\n  element.type.muiName ?? element.type?._payload?.value?.muiName) !== -1;\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,aAAa,OAAO,EAAE,QAAQ;IACpD,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,iBAAoB,AAAD,EAAE,YAAY,SAAS,OAAO,CACrE,sFAAsF;IACtF,sIAAsI;IACtI,gDAAgD;IAChD,QAAQ,IAAI,CAAC,OAAO,IAAI,QAAQ,IAAI,EAAE,UAAU,OAAO,aAAa,CAAC;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1684, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/composeClasses/composeClasses.js"], "sourcesContent": ["/* eslint no-restricted-syntax: 0, prefer-template: 0, guard-for-in: 0\n   ---\n   These rules are preventing the performance optimizations below.\n */\n\n/**\n * Compose classes from multiple sources.\n *\n * @example\n * ```tsx\n * const slots = {\n *  root: ['root', 'primary'],\n *  label: ['label'],\n * };\n *\n * const getUtilityClass = (slot) => `MuiButton-${slot}`;\n *\n * const classes = {\n *   root: 'my-root-class',\n * };\n *\n * const output = composeClasses(slots, getUtilityClass, classes);\n * // {\n * //   root: 'MuiButton-root MuiButton-primary my-root-class',\n * //   label: 'MuiButton-label',\n * // }\n * ```\n *\n * @param slots a list of classes for each possible slot\n * @param getUtilityClass a function to resolve the class based on the slot name\n * @param classes the input classes from props\n * @returns the resolved classes for all slots\n */\nexport default function composeClasses(slots, getUtilityClass, classes = undefined) {\n  const output = {};\n  for (const slotName in slots) {\n    const slot = slots[slotName];\n    let buffer = '';\n    let start = true;\n    for (let i = 0; i < slot.length; i += 1) {\n      const value = slot[i];\n      if (value) {\n        buffer += (start === true ? '' : ' ') + getUtilityClass(value);\n        start = false;\n        if (classes && classes[value]) {\n          buffer += ' ' + classes[value];\n        }\n      }\n    }\n    output[slotName] = buffer;\n  }\n  return output;\n}"], "names": [], "mappings": "AAAA;;;CAGC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BC;;;AACc,SAAS,eAAe,KAAK,EAAE,eAAe,EAAE,UAAU,SAAS;IAChF,MAAM,SAAS,CAAC;IAChB,IAAK,MAAM,YAAY,MAAO;QAC5B,MAAM,OAAO,KAAK,CAAC,SAAS;QAC5B,IAAI,SAAS;QACb,IAAI,QAAQ;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACvC,MAAM,QAAQ,IAAI,CAAC,EAAE;YACrB,IAAI,OAAO;gBACT,UAAU,CAAC,UAAU,OAAO,KAAK,GAAG,IAAI,gBAAgB;gBACxD,QAAQ;gBACR,IAAI,WAAW,OAAO,CAAC,MAAM,EAAE;oBAC7B,UAAU,MAAM,OAAO,CAAC,MAAM;gBAChC;YACF;QACF;QACA,MAAM,CAAC,SAAS,GAAG;IACrB;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1743, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/getDisplayName/getDisplayName.js"], "sourcesContent": ["import { ForwardRef, Memo } from 'react-is';\nfunction getFunctionComponentName(Component, fallback = '') {\n  return Component.displayName || Component.name || fallback;\n}\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  const functionName = getFunctionComponentName(innerType);\n  return outerType.displayName || (functionName !== '' ? `${wrapperName}(${functionName})` : wrapperName);\n}\n\n/**\n * cherry-pick from\n * https://github.com/facebook/react/blob/769b1f270e1251d9dbdce0fcbd9e92e502d059b8/packages/shared/getComponentName.js\n * originally forked from recompose/getDisplayName\n */\nexport default function getDisplayName(Component) {\n  if (Component == null) {\n    return undefined;\n  }\n  if (typeof Component === 'string') {\n    return Component;\n  }\n  if (typeof Component === 'function') {\n    return getFunctionComponentName(Component, 'Component');\n  }\n\n  // TypeScript can't have components as objects but they exist in the form of `memo` or `Suspense`\n  if (typeof Component === 'object') {\n    switch (Component.$$typeof) {\n      case ForwardRef:\n        return getWrappedName(Component, Component.render, 'ForwardRef');\n      case Memo:\n        return getWrappedName(Component, Component.type, 'memo');\n      default:\n        return undefined;\n    }\n  }\n  return undefined;\n}"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,yBAAyB,SAAS,EAAE,WAAW,EAAE;IACxD,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI;AACpD;AACA,SAAS,eAAe,SAAS,EAAE,SAAS,EAAE,WAAW;IACvD,MAAM,eAAe,yBAAyB;IAC9C,OAAO,UAAU,WAAW,IAAI,CAAC,iBAAiB,KAAK,GAAG,YAAY,CAAC,EAAE,aAAa,CAAC,CAAC,GAAG,WAAW;AACxG;AAOe,SAAS,eAAe,SAAS;IAC9C,IAAI,aAAa,MAAM;QACrB,OAAO;IACT;IACA,IAAI,OAAO,cAAc,UAAU;QACjC,OAAO;IACT;IACA,IAAI,OAAO,cAAc,YAAY;QACnC,OAAO,yBAAyB,WAAW;IAC7C;IAEA,iGAAiG;IACjG,IAAI,OAAO,cAAc,UAAU;QACjC,OAAQ,UAAU,QAAQ;YACxB,KAAK,yKAAA,CAAA,aAAU;gBACb,OAAO,eAAe,WAAW,UAAU,MAAM,EAAE;YACrD,KAAK,yKAAA,CAAA,OAAI;gBACP,OAAO,eAAe,WAAW,UAAU,IAAI,EAAE;YACnD;gBACE,OAAO;QACX;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/requirePropFactory/requirePropFactory.js"], "sourcesContent": ["export default function requirePropFactory(componentNameInError, Component) {\n  if (process.env.NODE_ENV === 'production') {\n    return () => () => null;\n  }\n\n  // eslint-disable-next-line react/forbid-foreign-prop-types\n  const prevPropTypes = Component ? {\n    ...Component.propTypes\n  } : null;\n  const requireProp = requiredProp => (props, propName, componentName, location, propFullName, ...args) => {\n    const propFullNameSafe = propFullName || propName;\n    const defaultTypeChecker = prevPropTypes?.[propFullNameSafe];\n    if (defaultTypeChecker) {\n      const typeCheckerResult = defaultTypeChecker(props, propName, componentName, location, propFullName, ...args);\n      if (typeCheckerResult) {\n        return typeCheckerResult;\n      }\n    }\n    if (typeof props[propName] !== 'undefined' && !props[requiredProp]) {\n      return new Error(`The prop \\`${propFullNameSafe}\\` of ` + `\\`${componentNameInError}\\` can only be used together with the \\`${requiredProp}\\` prop.`);\n    }\n    return null;\n  };\n  return requireProp;\n}"], "names": [], "mappings": ";;;AACM;AADS,SAAS,mBAAmB,oBAAoB,EAAE,SAAS;IACxE,uCAA2C;;IAE3C;IAEA,2DAA2D;IAC3D,MAAM,gBAAgB,YAAY;QAChC,GAAG,UAAU,SAAS;IACxB,IAAI;IACJ,MAAM,cAAc,CAAA,eAAgB,CAAC,OAAO,UAAU,eAAe,UAAU,cAAc,GAAG;YAC9F,MAAM,mBAAmB,gBAAgB;YACzC,MAAM,qBAAqB,eAAe,CAAC,iBAAiB;YAC5D,IAAI,oBAAoB;gBACtB,MAAM,oBAAoB,mBAAmB,OAAO,UAAU,eAAe,UAAU,iBAAiB;gBACxG,IAAI,mBAAmB;oBACrB,OAAO;gBACT;YACF;YACA,IAAI,OAAO,KAAK,CAAC,SAAS,KAAK,eAAe,CAAC,KAAK,CAAC,aAAa,EAAE;gBAClE,OAAO,IAAI,MAAM,CAAC,WAAW,EAAE,iBAAiB,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,qBAAqB,wCAAwC,EAAE,aAAa,QAAQ,CAAC;YACtJ;YACA,OAAO;QACT;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1818, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/chainPropTypes/chainPropTypes.js"], "sourcesContent": ["export default function chainPropTypes(propType1, propType2) {\n  if (process.env.NODE_ENV === 'production') {\n    return () => null;\n  }\n  return function validate(...args) {\n    return propType1(...args) || propType2(...args);\n  };\n}"], "names": [], "mappings": ";;;AACM;AADS,SAAS,eAAe,SAAS,EAAE,SAAS;IACzD,uCAA2C;;IAE3C;IACA,OAAO,SAAS,SAAS,GAAG,IAAI;QAC9B,OAAO,aAAa,SAAS,aAAa;IAC5C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1836, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/integerPropType/integerPropType.js"], "sourcesContent": ["export function getTypeByValue(value) {\n  const valueType = typeof value;\n  switch (valueType) {\n    case 'number':\n      if (Number.isNaN(value)) {\n        return 'NaN';\n      }\n      if (!Number.isFinite(value)) {\n        return 'Infinity';\n      }\n      if (value !== Math.floor(value)) {\n        return 'float';\n      }\n      return 'number';\n    case 'object':\n      if (value === null) {\n        return 'null';\n      }\n      return value.constructor.name;\n    default:\n      return valueType;\n  }\n}\nfunction requiredInteger(props, propName, componentName, location) {\n  const propValue = props[propName];\n  if (propValue == null || !Number.isInteger(propValue)) {\n    const propType = getTypeByValue(propValue);\n    return new RangeError(`Invalid ${location} \\`${propName}\\` of type \\`${propType}\\` supplied to \\`${componentName}\\`, expected \\`integer\\`.`);\n  }\n  return null;\n}\nfunction validator(props, propName, componentName, location) {\n  const propValue = props[propName];\n  if (propValue === undefined) {\n    return null;\n  }\n  return requiredInteger(props, propName, componentName, location);\n}\nfunction validatorNoop() {\n  return null;\n}\nvalidator.isRequired = requiredInteger;\nvalidatorNoop.isRequired = validatorNoop;\nconst integerPropType = process.env.NODE_ENV === 'production' ? validatorNoop : validator;\nexport default integerPropType;"], "names": [], "mappings": ";;;;AA2CwB;AA3CjB,SAAS,eAAe,KAAK;IAClC,MAAM,YAAY,OAAO;IACzB,OAAQ;QACN,KAAK;YACH,IAAI,OAAO,KAAK,CAAC,QAAQ;gBACvB,OAAO;YACT;YACA,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ;gBAC3B,OAAO;YACT;YACA,IAAI,UAAU,KAAK,KAAK,CAAC,QAAQ;gBAC/B,OAAO;YACT;YACA,OAAO;QACT,KAAK;YACH,IAAI,UAAU,MAAM;gBAClB,OAAO;YACT;YACA,OAAO,MAAM,WAAW,CAAC,IAAI;QAC/B;YACE,OAAO;IACX;AACF;AACA,SAAS,gBAAgB,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ;IAC/D,MAAM,YAAY,KAAK,CAAC,SAAS;IACjC,IAAI,aAAa,QAAQ,CAAC,OAAO,SAAS,CAAC,YAAY;QACrD,MAAM,WAAW,eAAe;QAChC,OAAO,IAAI,WAAW,CAAC,QAAQ,EAAE,SAAS,GAAG,EAAE,SAAS,aAAa,EAAE,SAAS,iBAAiB,EAAE,cAAc,yBAAyB,CAAC;IAC7I;IACA,OAAO;AACT;AACA,SAAS,UAAU,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ;IACzD,MAAM,YAAY,KAAK,CAAC,SAAS;IACjC,IAAI,cAAc,WAAW;QAC3B,OAAO;IACT;IACA,OAAO,gBAAgB,OAAO,UAAU,eAAe;AACzD;AACA,SAAS;IACP,OAAO;AACT;AACA,UAAU,UAAU,GAAG;AACvB,cAAc,UAAU,GAAG;AAC3B,MAAM,kBAAkB,6EAAwD;uCACjE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1892, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/useId/useId.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nlet globalId = 0;\n\n// TODO React 17: Remove `useGlobalId` once React 17 support is removed\nfunction useGlobalId(idOverride) {\n  const [defaultId, setDefaultId] = React.useState(idOverride);\n  const id = idOverride || defaultId;\n  React.useEffect(() => {\n    if (defaultId == null) {\n      // Fallback to this default id when possible.\n      // Use the incrementing value for client-side rendering only.\n      // We can't use it server-side.\n      // If you want to use random values please consider the Birthday Problem: https://en.wikipedia.org/wiki/Birthday_problem\n      globalId += 1;\n      setDefaultId(`mui-${globalId}`);\n    }\n  }, [defaultId]);\n  return id;\n}\n\n// See https://github.com/mui/material-ui/issues/41190#issuecomment-********** for why\nconst safeReact = {\n  ...React\n};\nconst maybeReactUseId = safeReact.useId;\n\n/**\n *\n * @example <div id={useId()} />\n * @param idOverride\n * @returns {string}\n */\nexport default function useId(idOverride) {\n  // React.useId() is only available from React 17.0.0.\n  if (maybeReactUseId !== undefined) {\n    const reactId = maybeReactUseId();\n    return idOverride ?? reactId;\n  }\n\n  // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n  // eslint-disable-next-line react-hooks/rules-of-hooks -- `React.useId` is invariant at runtime.\n  return useGlobalId(idOverride);\n}"], "names": [], "mappings": ";;;AAEA;AAFA;;AAGA,IAAI,WAAW;AAEf,uEAAuE;AACvE,SAAS,YAAY,UAAU;IAC7B,MAAM,CAAC,WAAW,aAAa,GAAG,8JAAM,QAAQ,CAAC;IACjD,MAAM,KAAK,cAAc;IACzB,8JAAM,SAAS;iCAAC;YACd,IAAI,aAAa,MAAM;gBACrB,6CAA6C;gBAC7C,6DAA6D;gBAC7D,+BAA+B;gBAC/B,wHAAwH;gBACxH,YAAY;gBACZ,aAAa,CAAC,IAAI,EAAE,UAAU;YAChC;QACF;gCAAG;QAAC;KAAU;IACd,OAAO;AACT;AAEA,sFAAsF;AACtF,MAAM,YAAY;IAChB,GAAG,6JAAK;AACV;AACA,MAAM,kBAAkB,UAAU,KAAK;AAQxB,SAAS,MAAM,UAAU;IACtC,qDAAqD;IACrD,IAAI,oBAAoB,WAAW;QACjC,MAAM,UAAU;QAChB,OAAO,cAAc;IACvB;IAEA,wHAAwH;IACxH,gGAAgG;IAChG,OAAO,YAAY;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1940, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/refType/refType.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nconst refType = PropTypes.oneOfType([PropTypes.func, PropTypes.object]);\nexport default refType;"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,UAAU,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;IAAC,yIAAA,CAAA,UAAS,CAAC,IAAI;IAAE,yIAAA,CAAA,UAAS,CAAC,MAAM;CAAC;uCACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1956, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/elementTypeAcceptingRef/elementTypeAcceptingRef.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nimport chainPropTypes from \"../chainPropTypes/index.js\";\nfunction isClassComponent(elementType) {\n  // elementType.prototype?.isReactComponent\n  const {\n    prototype = {}\n  } = elementType;\n  return Boolean(prototype.isReactComponent);\n}\nfunction elementTypeAcceptingRef(props, propName, componentName, location, propFullName) {\n  const propValue = props[propName];\n  const safePropName = propFullName || propName;\n  if (propValue == null ||\n  // When server-side rendering React doesn't warn either.\n  // This is not an accurate check for SSR.\n  // This is only in place for emotion compat.\n  // TODO: Revisit once https://github.com/facebook/react/issues/20047 is resolved.\n  typeof window === 'undefined') {\n    return null;\n  }\n  let warningHint;\n\n  /**\n   * Blacklisting instead of whitelisting\n   *\n   * Blacklisting will miss some components, such as React.Fragment. Those will at least\n   * trigger a warning in React.\n   * We can't whitelist because there is no safe way to detect React.forwardRef\n   * or class components. \"Safe\" means there's no public API.\n   *\n   */\n  if (typeof propValue === 'function' && !isClassComponent(propValue)) {\n    warningHint = 'Did you accidentally provide a plain function component instead?';\n  }\n  if (warningHint !== undefined) {\n    return new Error(`Invalid ${location} \\`${safePropName}\\` supplied to \\`${componentName}\\`. ` + `Expected an element type that can hold a ref. ${warningHint} ` + 'For more information see https://mui.com/r/caveat-with-refs-guide');\n  }\n  return null;\n}\nexport default chainPropTypes(PropTypes.elementType, elementTypeAcceptingRef);"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,SAAS,iBAAiB,WAAW;IACnC,0CAA0C;IAC1C,MAAM,EACJ,YAAY,CAAC,CAAC,EACf,GAAG;IACJ,OAAO,QAAQ,UAAU,gBAAgB;AAC3C;AACA,SAAS,wBAAwB,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;IACrF,MAAM,YAAY,KAAK,CAAC,SAAS;IACjC,MAAM,eAAe,gBAAgB;IACrC,IAAI,aAAa,QACjB,wDAAwD;IACxD,yCAAyC;IACzC,4CAA4C;IAC5C,iFAAiF;IACjF,OAAO,WAAW,aAAa;QAC7B,OAAO;IACT;IACA,IAAI;IAEJ;;;;;;;;GAQC,GACD,IAAI,OAAO,cAAc,cAAc,CAAC,iBAAiB,YAAY;QACnE,cAAc;IAChB;IACA,IAAI,gBAAgB,WAAW;QAC7B,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,SAAS,GAAG,EAAE,aAAa,iBAAiB,EAAE,cAAc,IAAI,CAAC,GAAG,CAAC,8CAA8C,EAAE,YAAY,CAAC,CAAC,GAAG;IACpK;IACA,OAAO;AACT;uCACe,CAAA,GAAA,4KAAA,CAAA,UAAc,AAAD,EAAE,yIAAA,CAAA,UAAS,CAAC,WAAW,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2002, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/isFocusVisible/isFocusVisible.js"], "sourcesContent": ["/**\n * Returns a boolean indicating if the event's target has :focus-visible\n */\nexport default function isFocusVisible(element) {\n  try {\n    return element.matches(':focus-visible');\n  } catch (error) {\n    // Do not warn on jsdom tests, otherwise all tests that rely on focus have to be skipped\n    // Tests that rely on `:focus-visible` will still have to be skipped in jsdom\n    if (process.env.NODE_ENV !== 'production' && !/jsdom/.test(window.navigator.userAgent)) {\n      console.warn(['MUI: The `:focus-visible` pseudo class is not supported in this browser.', 'Some components rely on this feature to work properly.'].join('\\n'));\n    }\n  }\n  return false;\n}"], "names": [], "mappings": "AAAA;;CAEC;;;AAOO;AANO,SAAS,eAAe,OAAO;IAC5C,IAAI;QACF,OAAO,QAAQ,OAAO,CAAC;IACzB,EAAE,OAAO,OAAO;QACd,wFAAwF;QACxF,6EAA6E;QAC7E,IAAI,oDAAyB,gBAAgB,CAAC,QAAQ,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS,GAAG;YACtF,QAAQ,IAAI,CAAC;gBAAC;gBAA4E;aAAyD,CAAC,IAAI,CAAC;QAC3J;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2029, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/useForkRef/useForkRef.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n\n/**\n * Merges refs into a single memoized callback ref or `null`.\n *\n * ```tsx\n * const rootRef = React.useRef<Instance>(null);\n * const refFork = useForkRef(rootRef, props.ref);\n *\n * return (\n *   <Root {...props} ref={refFork} />\n * );\n * ```\n *\n * @param {Array<React.Ref<Instance> | undefined>} refs The ref array.\n * @returns {React.RefCallback<Instance> | null} The new ref callback.\n */\nexport default function useForkRef(...refs) {\n  const cleanupRef = React.useRef(undefined);\n  const refEffect = React.useCallback(instance => {\n    const cleanups = refs.map(ref => {\n      if (ref == null) {\n        return null;\n      }\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return typeof refCleanup === 'function' ? refCleanup : () => {\n          refCallback(null);\n        };\n      }\n      ref.current = instance;\n      return () => {\n        ref.current = null;\n      };\n    });\n    return () => {\n      cleanups.forEach(refCleanup => refCleanup?.());\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return value => {\n      if (cleanupRef.current) {\n        cleanupRef.current();\n        cleanupRef.current = undefined;\n      }\n      if (value != null) {\n        cleanupRef.current = refEffect(value);\n      }\n    };\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- intentionally ignoring that the dependency array must be an array literal\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}"], "names": [], "mappings": ";;;AAEA;AAFA;;AAmBe,SAAS,WAAW,GAAG,IAAI;IACxC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE;IAChC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;6CAAE,CAAA;YAClC,MAAM,WAAW,KAAK,GAAG;8DAAC,CAAA;oBACxB,IAAI,OAAO,MAAM;wBACf,OAAO;oBACT;oBACA,IAAI,OAAO,QAAQ,YAAY;wBAC7B,MAAM,cAAc;wBACpB,MAAM,aAAa,YAAY;wBAC/B,OAAO,OAAO,eAAe,aAAa;0EAAa;gCACrD,YAAY;4BACd;;oBACF;oBACA,IAAI,OAAO,GAAG;oBACd;sEAAO;4BACL,IAAI,OAAO,GAAG;wBAChB;;gBACF;;YACA;qDAAO;oBACL,SAAS,OAAO;6DAAC,CAAA,aAAc;;gBACjC;;QACA,uDAAuD;QACzD;4CAAG;IACH,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;8BAAE;YACnB,IAAI,KAAK,KAAK;sCAAC,CAAA,MAAO,OAAO;sCAAO;gBAClC,OAAO;YACT;YACA;sCAAO,CAAA;oBACL,IAAI,WAAW,OAAO,EAAE;wBACtB,WAAW,OAAO;wBAClB,WAAW,OAAO,GAAG;oBACvB;oBACA,IAAI,SAAS,MAAM;wBACjB,WAAW,OAAO,GAAG,UAAU;oBACjC;gBACF;;QACA,qMAAqM;QACrM,uDAAuD;QACzD;6BAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/useEventCallback/useEventCallback.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from \"../useEnhancedEffect/index.js\";\n\n/**\n * Inspired by https://github.com/facebook/react/issues/14099#issuecomment-*********\n * See RFC in https://github.com/reactjs/rfcs/pull/220\n */\n\nfunction useEventCallback(fn) {\n  const ref = React.useRef(fn);\n  useEnhancedEffect(() => {\n    ref.current = fn;\n  });\n  return React.useRef((...args) =>\n  // @ts-expect-error hide `this`\n  (0, ref.current)(...args)).current;\n}\nexport default useEventCallback;"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAKA;;;CAGC,GAED,SAAS,iBAAiB,EAAE;IAC1B,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE;IACzB,CAAA,GAAA,kLAAA,CAAA,UAAiB,AAAD;8CAAE;YAChB,IAAI,OAAO,GAAG;QAChB;;IACA,OAAO,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD;mCAAE,CAAC,GAAG,OACxB,+BAA+B;YAC/B,CAAC,GAAG,IAAI,OAAO,KAAK;kCAAO,OAAO;AACpC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/useLazyRef/useLazyRef.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nconst UNINITIALIZED = {};\n\n/**\n * A React.useRef() that is initialized lazily with a function. Note that it accepts an optional\n * initialization argument, so the initialization function doesn't need to be an inline closure.\n *\n * @usage\n *   const ref = useLazyRef(sortColumns, columns)\n */\nexport default function useLazyRef(init, initArg) {\n  const ref = React.useRef(UNINITIALIZED);\n  if (ref.current === UNINITIALIZED) {\n    ref.current = init(initArg);\n  }\n  return ref;\n}"], "names": [], "mappings": ";;;AAEA;AAFA;;AAGA,MAAM,gBAAgB,CAAC;AASR,SAAS,WAAW,IAAI,EAAE,OAAO;IAC9C,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE;IACzB,IAAI,IAAI,OAAO,KAAK,eAAe;QACjC,IAAI,OAAO,GAAG,KAAK;IACrB;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/useOnMount/useOnMount.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nconst EMPTY = [];\n\n/**\n * A React.useEffect equivalent that runs once, when the component is mounted.\n */\nexport default function useOnMount(fn) {\n  // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- no need to put `fn` in the dependency array\n  /* eslint-disable react-hooks/exhaustive-deps */\n  React.useEffect(fn, EMPTY);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}"], "names": [], "mappings": ";;;AAEA;AAFA;;AAGA,MAAM,QAAQ,EAAE;AAKD,SAAS,WAAW,EAAE;IACnC,uKAAuK;IACvK,8CAA8C,GAC9C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD,EAAE,IAAI;AACpB,6CAA6C,GAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/useTimeout/useTimeout.js"], "sourcesContent": ["'use client';\n\nimport useLazyRef from \"../useLazyRef/useLazyRef.js\";\nimport useOnMount from \"../useOnMount/useOnMount.js\";\nexport class Timeout {\n  static create() {\n    return new Timeout();\n  }\n  currentId = null;\n\n  /**\n   * Executes `fn` after `delay`, clearing any previously scheduled call.\n   */\n  start(delay, fn) {\n    this.clear();\n    this.currentId = setTimeout(() => {\n      this.currentId = null;\n      fn();\n    }, delay);\n  }\n  clear = () => {\n    if (this.currentId !== null) {\n      clearTimeout(this.currentId);\n      this.currentId = null;\n    }\n  };\n  disposeEffect = () => {\n    return this.clear;\n  };\n}\nexport default function useTimeout() {\n  const timeout = useLazyRef(Timeout.create).current;\n  useOnMount(timeout.disposeEffect);\n  return timeout;\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAIO,MAAM;IACX,OAAO,SAAS;QACd,OAAO,IAAI;IACb;IACA,YAAY,KAAK;IAEjB;;GAEC,GACD,MAAM,KAAK,EAAE,EAAE,EAAE;QACf,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,SAAS,GAAG,WAAW;YAC1B,IAAI,CAAC,SAAS,GAAG;YACjB;QACF,GAAG;IACL;IACA,QAAQ;QACN,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM;YAC3B,aAAa,IAAI,CAAC,SAAS;YAC3B,IAAI,CAAC,SAAS,GAAG;QACnB;IACF,EAAE;IACF,gBAAgB;QACd,OAAO,IAAI,CAAC,KAAK;IACnB,EAAE;AACJ;AACe,SAAS;IACtB,MAAM,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAU,AAAD,EAAE,QAAQ,MAAM,EAAE,OAAO;IAClD,CAAA,GAAA,oKAAA,CAAA,UAAU,AAAD,EAAE,QAAQ,aAAa;IAChC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/HTMLElementType/HTMLElementType.js"], "sourcesContent": ["export default function HTMLElementType(props, propName, componentName, location, propFullName) {\n  if (process.env.NODE_ENV === 'production') {\n    return null;\n  }\n  const propValue = props[propName];\n  const safePropName = propFullName || propName;\n  if (propValue == null) {\n    return null;\n  }\n  if (propValue && propValue.nodeType !== 1) {\n    return new Error(`Invalid ${location} \\`${safePropName}\\` supplied to \\`${componentName}\\`. ` + `Expected an HTMLElement.`);\n  }\n  return null;\n}"], "names": [], "mappings": ";;;AACM;AADS,SAAS,gBAAgB,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;IAC5F,uCAA2C;;IAE3C;IACA,MAAM,YAAY,KAAK,CAAC,SAAS;IACjC,MAAM,eAAe,gBAAgB;IACrC,IAAI,aAAa,MAAM;QACrB,OAAO;IACT;IACA,IAAI,aAAa,UAAU,QAAQ,KAAK,GAAG;QACzC,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,SAAS,GAAG,EAAE,aAAa,iBAAiB,EAAE,cAAc,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC;IAC5H;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/elementAcceptingRef/elementAcceptingRef.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nimport chainPropTypes from \"../chainPropTypes/index.js\";\nfunction isClassComponent(elementType) {\n  // elementType.prototype?.isReactComponent\n  const {\n    prototype = {}\n  } = elementType;\n  return Boolean(prototype.isReactComponent);\n}\nfunction acceptingRef(props, propName, componentName, location, propFullName) {\n  const element = props[propName];\n  const safePropName = propFullName || propName;\n  if (element == null ||\n  // When server-side rendering React doesn't warn either.\n  // This is not an accurate check for SSR.\n  // This is only in place for Emotion compat.\n  // TODO: Revisit once https://github.com/facebook/react/issues/20047 is resolved.\n  typeof window === 'undefined') {\n    return null;\n  }\n  let warningHint;\n  const elementType = element.type;\n  /**\n   * Blacklisting instead of whitelisting\n   *\n   * Blacklisting will miss some components, such as React.Fragment. Those will at least\n   * trigger a warning in React.\n   * We can't whitelist because there is no safe way to detect React.forwardRef\n   * or class components. \"Safe\" means there's no public API.\n   *\n   */\n  if (typeof elementType === 'function' && !isClassComponent(elementType)) {\n    warningHint = 'Did you accidentally use a plain function component for an element instead?';\n  }\n  if (warningHint !== undefined) {\n    return new Error(`Invalid ${location} \\`${safePropName}\\` supplied to \\`${componentName}\\`. ` + `Expected an element that can hold a ref. ${warningHint} ` + 'For more information see https://mui.com/r/caveat-with-refs-guide');\n  }\n  return null;\n}\nconst elementAcceptingRef = chainPropTypes(PropTypes.element, acceptingRef);\nelementAcceptingRef.isRequired = chainPropTypes(PropTypes.element.isRequired, acceptingRef);\nexport default elementAcceptingRef;"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,SAAS,iBAAiB,WAAW;IACnC,0CAA0C;IAC1C,MAAM,EACJ,YAAY,CAAC,CAAC,EACf,GAAG;IACJ,OAAO,QAAQ,UAAU,gBAAgB;AAC3C;AACA,SAAS,aAAa,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;IAC1E,MAAM,UAAU,KAAK,CAAC,SAAS;IAC/B,MAAM,eAAe,gBAAgB;IACrC,IAAI,WAAW,QACf,wDAAwD;IACxD,yCAAyC;IACzC,4CAA4C;IAC5C,iFAAiF;IACjF,OAAO,WAAW,aAAa;QAC7B,OAAO;IACT;IACA,IAAI;IACJ,MAAM,cAAc,QAAQ,IAAI;IAChC;;;;;;;;GAQC,GACD,IAAI,OAAO,gBAAgB,cAAc,CAAC,iBAAiB,cAAc;QACvE,cAAc;IAChB;IACA,IAAI,gBAAgB,WAAW;QAC7B,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,SAAS,GAAG,EAAE,aAAa,iBAAiB,EAAE,cAAc,IAAI,CAAC,GAAG,CAAC,yCAAyC,EAAE,YAAY,CAAC,CAAC,GAAG;IAC/J;IACA,OAAO;AACT;AACA,MAAM,sBAAsB,CAAA,GAAA,4KAAA,CAAA,UAAc,AAAD,EAAE,yIAAA,CAAA,UAAS,CAAC,OAAO,EAAE;AAC9D,oBAAoB,UAAU,GAAG,CAAA,GAAA,4KAAA,CAAA,UAAc,AAAD,EAAE,yIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,UAAU,EAAE;uCAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/ownerDocument/ownerDocument.js"], "sourcesContent": ["export default function ownerDocument(node) {\n  return node && node.ownerDocument || document;\n}"], "names": [], "mappings": ";;;AAAe,SAAS,cAAc,IAAI;IACxC,OAAO,QAAQ,KAAK,aAAa,IAAI;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/getReactElementRef/getReactElementRef.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Returns the ref of a React element handling differences between React 19 and older versions.\n * It will throw runtime error if the element is not a valid React element.\n *\n * @param element React.ReactElement\n * @returns React.Ref<any> | null\n */\nexport default function getReactElementRef(element) {\n  // 'ref' is passed as prop in React 19, whereas 'ref' is directly attached to children in older versions\n  if (parseInt(React.version, 10) >= 19) {\n    return element?.props?.ref || null;\n  }\n  // @ts-expect-error element.ref is not included in the ReactElement type\n  // https://github.com/DefinitelyTyped/DefinitelyTyped/discussions/70189\n  return element?.ref || null;\n}"], "names": [], "mappings": ";;;AAAA;;AASe,SAAS,mBAAmB,OAAO;IAChD,wGAAwG;IACxG,IAAI,SAAS,6JAAA,CAAA,UAAa,EAAE,OAAO,IAAI;QACrC,OAAO,SAAS,OAAO,OAAO;IAChC;IACA,wEAAwE;IACxE,uEAAuE;IACvE,OAAO,SAAS,OAAO;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2316, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/setRef/setRef.js"], "sourcesContent": ["/**\n * TODO v5: consider making it private\n *\n * passes {value} to {ref}\n *\n * WARNING: Be sure to only call this inside a callback that is passed as a ref.\n * Otherwise, make sure to cleanup the previous {ref} if it changes. See\n * https://github.com/mui/material-ui/issues/13539\n *\n * Useful if you want to expose the ref of an inner component to the public API\n * while still using it inside the component.\n * @param ref A ref callback or ref object. If anything falsy, this is a no-op.\n */\nexport default function setRef(ref, value) {\n  if (typeof ref === 'function') {\n    ref(value);\n  } else if (ref) {\n    ref.current = value;\n  }\n}"], "names": [], "mappings": "AAAA;;;;;;;;;;;;CAYC;;;AACc,SAAS,OAAO,GAAG,EAAE,KAAK;IACvC,IAAI,OAAO,QAAQ,YAAY;QAC7B,IAAI;IACN,OAAO,IAAI,KAAK;QACd,IAAI,OAAO,GAAG;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/isHostComponent/isHostComponent.js"], "sourcesContent": ["/**\n * Determines if a given element is a DOM element name (i.e. not a React component).\n */\nfunction isHostComponent(element) {\n  return typeof element === 'string';\n}\nexport default isHostComponent;"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,SAAS,gBAAgB,OAAO;IAC9B,OAAO,OAAO,YAAY;AAC5B;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/appendOwnerState/appendOwnerState.js"], "sourcesContent": ["import isHostComponent from \"../isHostComponent/index.js\";\n\n/**\n * Type of the ownerState based on the type of an element it applies to.\n * This resolves to the provided OwnerState for React components and `undefined` for host components.\n * Falls back to `OwnerState | undefined` when the exact type can't be determined in development time.\n */\n\n/**\n * Appends the ownerState object to the props, merging with the existing one if necessary.\n *\n * @param elementType Type of the element that owns the `existingProps`. If the element is a DOM node or undefined, `ownerState` is not applied.\n * @param otherProps Props of the element.\n * @param ownerState\n */\nfunction appendOwnerState(elementType, otherProps, ownerState) {\n  if (elementType === undefined || isHostComponent(elementType)) {\n    return otherProps;\n  }\n  return {\n    ...otherProps,\n    ownerState: {\n      ...otherProps.ownerState,\n      ...ownerState\n    }\n  };\n}\nexport default appendOwnerState;"], "names": [], "mappings": ";;;AAAA;;AAEA;;;;CAIC,GAED;;;;;;CAMC,GACD,SAAS,iBAAiB,WAAW,EAAE,UAAU,EAAE,UAAU;IAC3D,IAAI,gBAAgB,aAAa,CAAA,GAAA,8KAAA,CAAA,UAAe,AAAD,EAAE,cAAc;QAC7D,OAAO;IACT;IACA,OAAO;QACL,GAAG,UAAU;QACb,YAAY;YACV,GAAG,WAAW,UAAU;YACxB,GAAG,UAAU;QACf;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/resolveComponentProps/resolveComponentProps.js"], "sourcesContent": ["/**\n * If `componentProps` is a function, calls it with the provided `ownerState`.\n * Otherwise, just returns `componentProps`.\n */\nfunction resolveComponentProps(componentProps, ownerState, slotState) {\n  if (typeof componentProps === 'function') {\n    return componentProps(ownerState, slotState);\n  }\n  return componentProps;\n}\nexport default resolveComponentProps;"], "names": [], "mappings": "AAAA;;;CAGC;;;AACD,SAAS,sBAAsB,cAAc,EAAE,UAAU,EAAE,SAAS;IAClE,IAAI,OAAO,mBAAmB,YAAY;QACxC,OAAO,eAAe,YAAY;IACpC;IACA,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/extractEventHandlers/extractEventHandlers.js"], "sourcesContent": ["/**\n * Extracts event handlers from a given object.\n * A prop is considered an event handler if it is a function and its name starts with `on`.\n *\n * @param object An object to extract event handlers from.\n * @param excludeKeys An array of keys to exclude from the returned object.\n */\nfunction extractEventHandlers(object, excludeKeys = []) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => prop.match(/^on[A-Z]/) && typeof object[prop] === 'function' && !excludeKeys.includes(prop)).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}\nexport default extractEventHandlers;"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AACD,SAAS,qBAAqB,MAAM,EAAE,cAAc,EAAE;IACpD,IAAI,WAAW,WAAW;QACxB,OAAO,CAAC;IACV;IACA,MAAM,SAAS,CAAC;IAChB,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,eAAe,OAAO,MAAM,CAAC,KAAK,KAAK,cAAc,CAAC,YAAY,QAAQ,CAAC,OAAO,OAAO,CAAC,CAAA;QACtI,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;IAC7B;IACA,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2438, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/omitEventHandlers/omitEventHandlers.js"], "sourcesContent": ["/**\n * Removes event handlers from the given object.\n * A field is considered an event handler if it is a function with a name beginning with `on`.\n *\n * @param object Object to remove event handlers from.\n * @returns Object with event handlers removed.\n */\nfunction omitEventHandlers(object) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => !(prop.match(/^on[A-Z]/) && typeof object[prop] === 'function')).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}\nexport default omitEventHandlers;"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AACD,SAAS,kBAAkB,MAAM;IAC/B,IAAI,WAAW,WAAW;QACxB,OAAO,CAAC;IACV;IACA,MAAM,SAAS,CAAC;IAChB,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAA,OAAQ,CAAC,CAAC,KAAK,KAAK,CAAC,eAAe,OAAO,MAAM,CAAC,KAAK,KAAK,UAAU,GAAG,OAAO,CAAC,CAAA;QAC1G,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;IAC7B;IACA,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2464, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/mergeSlotProps/mergeSlotProps.js"], "sourcesContent": ["import clsx from 'clsx';\nimport extractEventHandlers from \"../extractEventHandlers/index.js\";\nimport omitEventHandlers from \"../omitEventHandlers/index.js\";\n/**\n * Merges the slot component internal props (usually coming from a hook)\n * with the externally provided ones.\n *\n * The merge order is (the latter overrides the former):\n * 1. The internal props (specified as a getter function to work with get*Props hook result)\n * 2. Additional props (specified internally on a Base UI component)\n * 3. External props specified on the owner component. These should only be used on a root slot.\n * 4. External props specified in the `slotProps.*` prop.\n * 5. The `className` prop - combined from all the above.\n * @param parameters\n * @returns\n */\nfunction mergeSlotProps(parameters) {\n  const {\n    getSlotProps,\n    additionalProps,\n    externalSlotProps,\n    externalForwardedProps,\n    className\n  } = parameters;\n  if (!getSlotProps) {\n    // The simpler case - getSlotProps is not defined, so no internal event handlers are defined,\n    // so we can simply merge all the props without having to worry about extracting event handlers.\n    const joinedClasses = clsx(additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n    const mergedStyle = {\n      ...additionalProps?.style,\n      ...externalForwardedProps?.style,\n      ...externalSlotProps?.style\n    };\n    const props = {\n      ...additionalProps,\n      ...externalForwardedProps,\n      ...externalSlotProps\n    };\n    if (joinedClasses.length > 0) {\n      props.className = joinedClasses;\n    }\n    if (Object.keys(mergedStyle).length > 0) {\n      props.style = mergedStyle;\n    }\n    return {\n      props,\n      internalRef: undefined\n    };\n  }\n\n  // In this case, getSlotProps is responsible for calling the external event handlers.\n  // We don't need to include them in the merged props because of this.\n\n  const eventHandlers = extractEventHandlers({\n    ...externalForwardedProps,\n    ...externalSlotProps\n  });\n  const componentsPropsWithoutEventHandlers = omitEventHandlers(externalSlotProps);\n  const otherPropsWithoutEventHandlers = omitEventHandlers(externalForwardedProps);\n  const internalSlotProps = getSlotProps(eventHandlers);\n\n  // The order of classes is important here.\n  // Emotion (that we use in libraries consuming Base UI) depends on this order\n  // to properly override style. It requires the most important classes to be last\n  // (see https://github.com/mui/material-ui/pull/33205) for the related discussion.\n  const joinedClasses = clsx(internalSlotProps?.className, additionalProps?.className, className, externalForwardedProps?.className, externalSlotProps?.className);\n  const mergedStyle = {\n    ...internalSlotProps?.style,\n    ...additionalProps?.style,\n    ...externalForwardedProps?.style,\n    ...externalSlotProps?.style\n  };\n  const props = {\n    ...internalSlotProps,\n    ...additionalProps,\n    ...otherPropsWithoutEventHandlers,\n    ...componentsPropsWithoutEventHandlers\n  };\n  if (joinedClasses.length > 0) {\n    props.className = joinedClasses;\n  }\n  if (Object.keys(mergedStyle).length > 0) {\n    props.style = mergedStyle;\n  }\n  return {\n    props,\n    internalRef: internalSlotProps.ref\n  };\n}\nexport default mergeSlotProps;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA;;;;;;;;;;;;CAYC,GACD,SAAS,eAAe,UAAU;IAChC,MAAM,EACJ,YAAY,EACZ,eAAe,EACf,iBAAiB,EACjB,sBAAsB,EACtB,SAAS,EACV,GAAG;IACJ,IAAI,CAAC,cAAc;QACjB,6FAA6F;QAC7F,gGAAgG;QAChG,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,iBAAiB,WAAW,WAAW,wBAAwB,WAAW,mBAAmB;QACxH,MAAM,cAAc;YAClB,GAAG,iBAAiB,KAAK;YACzB,GAAG,wBAAwB,KAAK;YAChC,GAAG,mBAAmB,KAAK;QAC7B;QACA,MAAM,QAAQ;YACZ,GAAG,eAAe;YAClB,GAAG,sBAAsB;YACzB,GAAG,iBAAiB;QACtB;QACA,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,MAAM,SAAS,GAAG;QACpB;QACA,IAAI,OAAO,IAAI,CAAC,aAAa,MAAM,GAAG,GAAG;YACvC,MAAM,KAAK,GAAG;QAChB;QACA,OAAO;YACL;YACA,aAAa;QACf;IACF;IAEA,qFAAqF;IACrF,qEAAqE;IAErE,MAAM,gBAAgB,CAAA,GAAA,wLAAA,CAAA,UAAoB,AAAD,EAAE;QACzC,GAAG,sBAAsB;QACzB,GAAG,iBAAiB;IACtB;IACA,MAAM,sCAAsC,CAAA,GAAA,kLAAA,CAAA,UAAiB,AAAD,EAAE;IAC9D,MAAM,iCAAiC,CAAA,GAAA,kLAAA,CAAA,UAAiB,AAAD,EAAE;IACzD,MAAM,oBAAoB,aAAa;IAEvC,0CAA0C;IAC1C,6EAA6E;IAC7E,gFAAgF;IAChF,kFAAkF;IAClF,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,UAAI,AAAD,EAAE,mBAAmB,WAAW,iBAAiB,WAAW,WAAW,wBAAwB,WAAW,mBAAmB;IACtJ,MAAM,cAAc;QAClB,GAAG,mBAAmB,KAAK;QAC3B,GAAG,iBAAiB,KAAK;QACzB,GAAG,wBAAwB,KAAK;QAChC,GAAG,mBAAmB,KAAK;IAC7B;IACA,MAAM,QAAQ;QACZ,GAAG,iBAAiB;QACpB,GAAG,eAAe;QAClB,GAAG,8BAA8B;QACjC,GAAG,mCAAmC;IACxC;IACA,IAAI,cAAc,MAAM,GAAG,GAAG;QAC5B,MAAM,SAAS,GAAG;IACpB;IACA,IAAI,OAAO,IAAI,CAAC,aAAa,MAAM,GAAG,GAAG;QACvC,MAAM,KAAK,GAAG;IAChB;IACA,OAAO;QACL;QACA,aAAa,kBAAkB,GAAG;IACpC;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2556, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/createChainedFunction/createChainedFunction.js"], "sourcesContent": ["/**\n * Safe chained function.\n *\n * Will only create a new function if needed,\n * otherwise will pass back existing functions or null.\n */\nexport default function createChainedFunction(...funcs) {\n  return funcs.reduce((acc, func) => {\n    if (func == null) {\n      return acc;\n    }\n    return function chainedFunction(...args) {\n      acc.apply(this, args);\n      func.apply(this, args);\n    };\n  }, () => {});\n}"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AACc,SAAS,sBAAsB,GAAG,KAAK;IACpD,OAAO,MAAM,MAAM,CAAC,CAAC,KAAK;QACxB,IAAI,QAAQ,MAAM;YAChB,OAAO;QACT;QACA,OAAO,SAAS,gBAAgB,GAAG,IAAI;YACrC,IAAI,KAAK,CAAC,IAAI,EAAE;YAChB,KAAK,KAAK,CAAC,IAAI,EAAE;QACnB;IACF,GAAG,KAAO;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2581, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/ownerWindow/ownerWindow.js"], "sourcesContent": ["import ownerDocument from \"../ownerDocument/index.js\";\nexport default function ownerWindow(node) {\n  const doc = ownerDocument(node);\n  return doc.defaultView || window;\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,YAAY,IAAI;IACtC,MAAM,MAAM,CAAA,GAAA,0KAAA,CAAA,UAAa,AAAD,EAAE;IAC1B,OAAO,IAAI,WAAW,IAAI;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2596, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/getScrollbarSize/getScrollbarSize.js"], "sourcesContent": ["// A change of the browser zoom change the scrollbar size.\n// Credit https://github.com/twbs/bootstrap/blob/488fd8afc535ca3a6ad4dc581f5e89217b6a36ac/js/src/util/scrollbar.js#L14-L18\nexport default function getScrollbarSize(win = window) {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = win.document.documentElement.clientWidth;\n  return win.innerWidth - documentWidth;\n}"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,0HAA0H;;;;AAC3G,SAAS,iBAAiB,MAAM,MAAM;IACnD,iFAAiF;IACjF,MAAM,gBAAgB,IAAI,QAAQ,CAAC,eAAe,CAAC,WAAW;IAC9D,OAAO,IAAI,UAAU,GAAG;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2612, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/debounce/debounce.js"], "sourcesContent": ["// Corresponds to 10 frames at 60 Hz.\n// A few bytes payload overhead when lodash/debounce is ~3 kB and debounce ~300 B.\nexport default function debounce(func, wait = 166) {\n  let timeout;\n  function debounced(...args) {\n    const later = () => {\n      // @ts-ignore\n      func.apply(this, args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  }\n  debounced.clear = () => {\n    clearTimeout(timeout);\n  };\n  return debounced;\n}"], "names": [], "mappings": "AAAA,qCAAqC;AACrC,kFAAkF;;;;AACnE,SAAS,SAAS,IAAI,EAAE,OAAO,GAAG;IAC/C,IAAI;IACJ,SAAS,UAAU,GAAG,IAAI;QACxB,MAAM,QAAQ;YACZ,aAAa;YACb,KAAK,KAAK,CAAC,IAAI,EAAE;QACnB;QACA,aAAa;QACb,UAAU,WAAW,OAAO;IAC9B;IACA,UAAU,KAAK,GAAG;QAChB,aAAa;IACf;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2638, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/useSlotProps/useSlotProps.js"], "sourcesContent": ["'use client';\n\nimport useForkRef from \"../useForkRef/index.js\";\nimport appendOwnerState from \"../appendOwnerState/index.js\";\nimport mergeSlotProps from \"../mergeSlotProps/index.js\";\nimport resolveComponentProps from \"../resolveComponentProps/index.js\";\n/**\n * @ignore - do not document.\n * Builds the props to be passed into the slot of an unstyled component.\n * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.\n * If the slot component is not a host component, it also merges in the `ownerState`.\n *\n * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.\n */\nfunction useSlotProps(parameters) {\n  const {\n    elementType,\n    externalSlotProps,\n    ownerState,\n    skipResolvingSlotProps = false,\n    ...other\n  } = parameters;\n  const resolvedComponentsProps = skipResolvingSlotProps ? {} : resolveComponentProps(externalSlotProps, ownerState);\n  const {\n    props: mergedProps,\n    internalRef\n  } = mergeSlotProps({\n    ...other,\n    externalSlotProps: resolvedComponentsProps\n  });\n  const ref = useForkRef(internalRef, resolvedComponentsProps?.ref, parameters.additionalProps?.ref);\n  const props = appendOwnerState(elementType, {\n    ...mergedProps,\n    ref\n  }, ownerState);\n  return props;\n}\nexport default useSlotProps;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AALA;;;;;AAMA;;;;;;;CAOC,GACD,SAAS,aAAa,UAAU;IAC9B,MAAM,EACJ,WAAW,EACX,iBAAiB,EACjB,UAAU,EACV,yBAAyB,KAAK,EAC9B,GAAG,OACJ,GAAG;IACJ,MAAM,0BAA0B,yBAAyB,CAAC,IAAI,CAAA,GAAA,0LAAA,CAAA,UAAqB,AAAD,EAAE,mBAAmB;IACvG,MAAM,EACJ,OAAO,WAAW,EAClB,WAAW,EACZ,GAAG,CAAA,GAAA,4KAAA,CAAA,UAAc,AAAD,EAAE;QACjB,GAAG,KAAK;QACR,mBAAmB;IACrB;IACA,MAAM,MAAM,CAAA,GAAA,oKAAA,CAAA,UAAU,AAAD,EAAE,aAAa,yBAAyB,KAAK,WAAW,eAAe,EAAE;IAC9F,MAAM,QAAQ,CAAA,GAAA,gLAAA,CAAA,UAAgB,AAAD,EAAE,aAAa;QAC1C,GAAG,WAAW;QACd;IACF,GAAG;IACH,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/useControlled/useControlled.js"], "sourcesContent": ["'use client';\n\n// TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- process.env never changes, dependency arrays are intentionally ignored\n/* eslint-disable react-hooks/rules-of-hooks, react-hooks/exhaustive-deps */\nimport * as React from 'react';\nexport default function useControlled(props) {\n  const {\n    controlled,\n    default: defaultProp,\n    name,\n    state = 'value'\n  } = props;\n  // isControlled is ignored in the hook dependency lists as it should never change.\n  const {\n    current: isControlled\n  } = React.useRef(controlled !== undefined);\n  const [valueState, setValue] = React.useState(defaultProp);\n  const value = isControlled ? controlled : valueState;\n  if (process.env.NODE_ENV !== 'production') {\n    React.useEffect(() => {\n      if (isControlled !== (controlled !== undefined)) {\n        console.error([`MUI: A component is changing the ${isControlled ? '' : 'un'}controlled ${state} state of ${name} to be ${isControlled ? 'un' : ''}controlled.`, 'Elements should not switch from uncontrolled to controlled (or vice versa).', `Decide between using a controlled or uncontrolled ${name} ` + 'element for the lifetime of the component.', \"The nature of the state is determined during the first render. It's considered controlled if the value is not `undefined`.\", 'More info: https://fb.me/react-controlled-components'].join('\\n'));\n      }\n    }, [state, name, controlled]);\n    const {\n      current: defaultValue\n    } = React.useRef(defaultProp);\n    React.useEffect(() => {\n      // Object.is() is not equivalent to the === operator.\n      // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is for more details.\n      if (!isControlled && !Object.is(defaultValue, defaultProp)) {\n        console.error([`MUI: A component is changing the default ${state} state of an uncontrolled ${name} after being initialized. ` + `To suppress this warning opt to use a controlled ${name}.`].join('\\n'));\n      }\n    }, [JSON.stringify(defaultProp)]);\n  }\n  const setValueIfUncontrolled = React.useCallback(newValue => {\n    if (!isControlled) {\n      setValue(newValue);\n    }\n  }, []);\n\n  // TODO: provide overloads for the useControlled function to account for the case where either\n  // controlled or default is not undefiend.\n  // In that case the return type should be [T, React.Dispatch<React.SetStateAction<T>>]\n  // otherwise it should be [T | undefined, React.Dispatch<React.SetStateAction<T | undefined>>]\n  return [value, setValueIfUncontrolled];\n}"], "names": [], "mappings": ";;;AAkBM;AAhBN,kMAAkM;AAClM,0EAA0E,GAC1E;AAJA;;AAKe,SAAS,cAAc,KAAK;IACzC,MAAM,EACJ,UAAU,EACV,SAAS,WAAW,EACpB,IAAI,EACJ,QAAQ,OAAO,EAChB,GAAG;IACJ,kFAAkF;IAClF,MAAM,EACJ,SAAS,YAAY,EACtB,GAAG,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE,eAAe;IAChC,MAAM,CAAC,YAAY,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAC9C,MAAM,QAAQ,eAAe,aAAa;IAC1C,wCAA2C;QACzC,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;uCAAE;gBACd,IAAI,iBAAiB,CAAC,eAAe,SAAS,GAAG;oBAC/C,QAAQ,KAAK,CAAC;wBAAC,CAAC,iCAAiC,EAAE,eAAe,KAAK,KAAK,WAAW,EAAE,MAAM,UAAU,EAAE,KAAK,OAAO,EAAE,eAAe,OAAO,GAAG,WAAW,CAAC;wBAAE;wBAA+E,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC,GAAG;wBAA8C;wBAA8H;qBAAuD,CAAC,IAAI,CAAC;gBACzhB;YACF;sCAAG;YAAC;YAAO;YAAM;SAAW;QAC5B,MAAM,EACJ,SAAS,YAAY,EACtB,GAAG,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE;QACjB,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;uCAAE;gBACd,qDAAqD;gBACrD,mHAAmH;gBACnH,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,cAAc,cAAc;oBAC1D,QAAQ,KAAK,CAAC;wBAAC,CAAC,yCAAyC,EAAE,MAAM,0BAA0B,EAAE,KAAK,0BAA0B,CAAC,GAAG,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;qBAAC,CAAC,IAAI,CAAC;gBACpM;YACF;sCAAG;YAAC,KAAK,SAAS,CAAC;SAAa;IAClC;IACA,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;6DAAE,CAAA;YAC/C,IAAI,CAAC,cAAc;gBACjB,SAAS;YACX;QACF;4DAAG,EAAE;IAEL,8FAA8F;IAC9F,0CAA0C;IAC1C,sFAAsF;IACtF,8FAA8F;IAC9F,OAAO;QAAC;QAAO;KAAuB;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2747, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/unsupportedProp/unsupportedProp.js"], "sourcesContent": ["export default function unsupportedProp(props, propName, componentName, location, propFullName) {\n  if (process.env.NODE_ENV === 'production') {\n    return null;\n  }\n  const propFullNameSafe = propFullName || propName;\n  if (typeof props[propName] !== 'undefined') {\n    return new Error(`The prop \\`${propFullNameSafe}\\` is not supported. Please remove it.`);\n  }\n  return null;\n}"], "names": [], "mappings": ";;;AACM;AADS,SAAS,gBAAgB,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY;IAC5F,uCAA2C;;IAE3C;IACA,MAAM,mBAAmB,gBAAgB;IACzC,IAAI,OAAO,KAAK,CAAC,SAAS,KAAK,aAAa;QAC1C,OAAO,IAAI,MAAM,CAAC,WAAW,EAAE,iBAAiB,sCAAsC,CAAC;IACzF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2767, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/utils/esm/usePreviousProps/usePreviousProps.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nconst usePreviousProps = value => {\n  const ref = React.useRef({});\n  React.useEffect(() => {\n    ref.current = value;\n  });\n  return ref.current;\n};\nexport default usePreviousProps;"], "names": [], "mappings": ";;;AAEA;AAFA;;AAGA,MAAM,mBAAmB,CAAA;IACvB,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE,CAAC;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;sCAAE;YACd,IAAI,OAAO,GAAG;QAChB;;IACA,OAAO,IAAI,OAAO;AACpB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2789, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js"], "sourcesContent": ["function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nexport { _objectWithoutPropertiesLoose as default };"], "names": [], "mappings": ";;;AAAA,SAAS,8BAA8B,CAAC,EAAE,CAAC;IACzC,IAAI,QAAQ,GAAG,OAAO,CAAC;IACvB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QACjD,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI;QACzB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACb;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2808, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40babel/runtime/helpers/esm/assertThisInitialized.js"], "sourcesContent": ["function _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nexport { _assertThisInitialized as default };"], "names": [], "mappings": ";;;AAAA,SAAS,uBAAuB,CAAC;IAC/B,IAAI,KAAK,MAAM,GAAG,MAAM,IAAI,eAAe;IAC3C,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2822, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40babel/runtime/helpers/esm/setPrototypeOf.js"], "sourcesContent": ["function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAC3B,OAAO,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC;QAC5F,OAAO,EAAE,SAAS,GAAG,GAAG;IAC1B,GAAG,gBAAgB,GAAG;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2837, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40babel/runtime/helpers/esm/inheritsLoose.js"], "sourcesContent": ["import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inheritsLoose(t, o) {\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\n}\nexport { _inheritsLoose as default };"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,eAAe,CAAC,EAAE,CAAC;IAC1B,EAAE,SAAS,GAAG,OAAO,MAAM,CAAC,EAAE,SAAS,GAAG,EAAE,SAAS,CAAC,WAAW,GAAG,GAAG,CAAA,GAAA,yKAAA,CAAA,UAAc,AAAD,EAAE,GAAG;AAC3F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2852, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/react-transition-group/esm/TransitionGroupContext.js"], "sourcesContent": ["import React from 'react';\nexport default React.createContext(null);"], "names": [], "mappings": ";;;AAAA;;uCACe,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2864, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/react-transition-group/esm/utils/ChildMapping.js"], "sourcesContent": ["import { Children, cloneElement, isValidElement } from 'react';\n/**\n * Given `this.props.children`, return an object mapping key to child.\n *\n * @param {*} children `this.props.children`\n * @return {object} Mapping of key to child\n */\n\nexport function getChildMapping(children, mapFn) {\n  var mapper = function mapper(child) {\n    return mapFn && isValidElement(child) ? mapFn(child) : child;\n  };\n\n  var result = Object.create(null);\n  if (children) Children.map(children, function (c) {\n    return c;\n  }).forEach(function (child) {\n    // run the map function here instead so that the key is the computed one\n    result[child.key] = mapper(child);\n  });\n  return result;\n}\n/**\n * When you're adding or removing children some may be added or removed in the\n * same render pass. We want to show *both* since we want to simultaneously\n * animate elements in and out. This function takes a previous set of keys\n * and a new set of keys and merges them with its best guess of the correct\n * ordering. In the future we may expose some of the utilities in\n * ReactMultiChild to make this easy, but for now React itself does not\n * directly have this concept of the union of prevChildren and nextChildren\n * so we implement it here.\n *\n * @param {object} prev prev children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @param {object} next next children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @return {object} a key set that contains all keys in `prev` and all keys\n * in `next` in a reasonable order.\n */\n\nexport function mergeChildMappings(prev, next) {\n  prev = prev || {};\n  next = next || {};\n\n  function getValueForKey(key) {\n    return key in next ? next[key] : prev[key];\n  } // For each key of `next`, the list of keys to insert before that key in\n  // the combined list\n\n\n  var nextKeysPending = Object.create(null);\n  var pendingKeys = [];\n\n  for (var prevKey in prev) {\n    if (prevKey in next) {\n      if (pendingKeys.length) {\n        nextKeysPending[prevKey] = pendingKeys;\n        pendingKeys = [];\n      }\n    } else {\n      pendingKeys.push(prevKey);\n    }\n  }\n\n  var i;\n  var childMapping = {};\n\n  for (var nextKey in next) {\n    if (nextKeysPending[nextKey]) {\n      for (i = 0; i < nextKeysPending[nextKey].length; i++) {\n        var pendingNextKey = nextKeysPending[nextKey][i];\n        childMapping[nextKeysPending[nextKey][i]] = getValueForKey(pendingNextKey);\n      }\n    }\n\n    childMapping[nextKey] = getValueForKey(nextKey);\n  } // Finally, add the keys which didn't appear before any key in `next`\n\n\n  for (i = 0; i < pendingKeys.length; i++) {\n    childMapping[pendingKeys[i]] = getValueForKey(pendingKeys[i]);\n  }\n\n  return childMapping;\n}\n\nfunction getProp(child, prop, props) {\n  return props[prop] != null ? props[prop] : child.props[prop];\n}\n\nexport function getInitialChildMapping(props, onExited) {\n  return getChildMapping(props.children, function (child) {\n    return cloneElement(child, {\n      onExited: onExited.bind(null, child),\n      in: true,\n      appear: getProp(child, 'appear', props),\n      enter: getProp(child, 'enter', props),\n      exit: getProp(child, 'exit', props)\n    });\n  });\n}\nexport function getNextChildMapping(nextProps, prevChildMapping, onExited) {\n  var nextChildMapping = getChildMapping(nextProps.children);\n  var children = mergeChildMappings(prevChildMapping, nextChildMapping);\n  Object.keys(children).forEach(function (key) {\n    var child = children[key];\n    if (!isValidElement(child)) return;\n    var hasPrev = (key in prevChildMapping);\n    var hasNext = (key in nextChildMapping);\n    var prevChild = prevChildMapping[key];\n    var isLeaving = isValidElement(prevChild) && !prevChild.props.in; // item is new (entering)\n\n    if (hasNext && (!hasPrev || isLeaving)) {\n      // console.log('entering', key)\n      children[key] = cloneElement(child, {\n        onExited: onExited.bind(null, child),\n        in: true,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    } else if (!hasNext && hasPrev && !isLeaving) {\n      // item is old (exiting)\n      // console.log('leaving', key)\n      children[key] = cloneElement(child, {\n        in: false\n      });\n    } else if (hasNext && hasPrev && isValidElement(prevChild)) {\n      // item hasn't changed transition states\n      // copy over the last transition props;\n      // console.log('unchanged', key)\n      children[key] = cloneElement(child, {\n        onExited: onExited.bind(null, child),\n        in: prevChild.props.in,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    }\n  });\n  return children;\n}"], "names": [], "mappings": ";;;;;;AAAA;;AAQO,SAAS,gBAAgB,QAAQ,EAAE,KAAK;IAC7C,IAAI,SAAS,SAAS,OAAO,KAAK;QAChC,OAAO,SAAS,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,MAAM,SAAS;IACzD;IAEA,IAAI,SAAS,OAAO,MAAM,CAAC;IAC3B,IAAI,UAAU,6JAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,UAAU,SAAU,CAAC;QAC9C,OAAO;IACT,GAAG,OAAO,CAAC,SAAU,KAAK;QACxB,wEAAwE;QACxE,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,OAAO;IAC7B;IACA,OAAO;AACT;AAmBO,SAAS,mBAAmB,IAAI,EAAE,IAAI;IAC3C,OAAO,QAAQ,CAAC;IAChB,OAAO,QAAQ,CAAC;IAEhB,SAAS,eAAe,GAAG;QACzB,OAAO,OAAO,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;IAC5C,EAAE,wEAAwE;IAC1E,oBAAoB;IAGpB,IAAI,kBAAkB,OAAO,MAAM,CAAC;IACpC,IAAI,cAAc,EAAE;IAEpB,IAAK,IAAI,WAAW,KAAM;QACxB,IAAI,WAAW,MAAM;YACnB,IAAI,YAAY,MAAM,EAAE;gBACtB,eAAe,CAAC,QAAQ,GAAG;gBAC3B,cAAc,EAAE;YAClB;QACF,OAAO;YACL,YAAY,IAAI,CAAC;QACnB;IACF;IAEA,IAAI;IACJ,IAAI,eAAe,CAAC;IAEpB,IAAK,IAAI,WAAW,KAAM;QACxB,IAAI,eAAe,CAAC,QAAQ,EAAE;YAC5B,IAAK,IAAI,GAAG,IAAI,eAAe,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAK;gBACpD,IAAI,iBAAiB,eAAe,CAAC,QAAQ,CAAC,EAAE;gBAChD,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,eAAe;YAC7D;QACF;QAEA,YAAY,CAAC,QAAQ,GAAG,eAAe;IACzC,EAAE,qEAAqE;IAGvE,IAAK,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;QACvC,YAAY,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,eAAe,WAAW,CAAC,EAAE;IAC9D;IAEA,OAAO;AACT;AAEA,SAAS,QAAQ,KAAK,EAAE,IAAI,EAAE,KAAK;IACjC,OAAO,KAAK,CAAC,KAAK,IAAI,OAAO,KAAK,CAAC,KAAK,GAAG,MAAM,KAAK,CAAC,KAAK;AAC9D;AAEO,SAAS,uBAAuB,KAAK,EAAE,QAAQ;IACpD,OAAO,gBAAgB,MAAM,QAAQ,EAAE,SAAU,KAAK;QACpD,OAAO,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,OAAO;YACzB,UAAU,SAAS,IAAI,CAAC,MAAM;YAC9B,IAAI;YACJ,QAAQ,QAAQ,OAAO,UAAU;YACjC,OAAO,QAAQ,OAAO,SAAS;YAC/B,MAAM,QAAQ,OAAO,QAAQ;QAC/B;IACF;AACF;AACO,SAAS,oBAAoB,SAAS,EAAE,gBAAgB,EAAE,QAAQ;IACvE,IAAI,mBAAmB,gBAAgB,UAAU,QAAQ;IACzD,IAAI,WAAW,mBAAmB,kBAAkB;IACpD,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,SAAU,GAAG;QACzC,IAAI,QAAQ,QAAQ,CAAC,IAAI;QACzB,IAAI,CAAC,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;QAC5B,IAAI,UAAW,OAAO;QACtB,IAAI,UAAW,OAAO;QACtB,IAAI,YAAY,gBAAgB,CAAC,IAAI;QACrC,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,CAAC,UAAU,KAAK,CAAC,EAAE,EAAE,yBAAyB;QAE3F,IAAI,WAAW,CAAC,CAAC,WAAW,SAAS,GAAG;YACtC,+BAA+B;YAC/B,QAAQ,CAAC,IAAI,GAAG,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBAClC,UAAU,SAAS,IAAI,CAAC,MAAM;gBAC9B,IAAI;gBACJ,MAAM,QAAQ,OAAO,QAAQ;gBAC7B,OAAO,QAAQ,OAAO,SAAS;YACjC;QACF,OAAO,IAAI,CAAC,WAAW,WAAW,CAAC,WAAW;YAC5C,wBAAwB;YACxB,8BAA8B;YAC9B,QAAQ,CAAC,IAAI,GAAG,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBAClC,IAAI;YACN;QACF,OAAO,IAAI,WAAW,WAAW,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE,YAAY;YAC1D,wCAAwC;YACxC,uCAAuC;YACvC,gCAAgC;YAChC,QAAQ,CAAC,IAAI,GAAG,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBAClC,UAAU,SAAS,IAAI,CAAC,MAAM;gBAC9B,IAAI,UAAU,KAAK,CAAC,EAAE;gBACtB,MAAM,QAAQ,OAAO,QAAQ;gBAC7B,OAAO,QAAQ,OAAO,SAAS;YACjC;QACF;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2978, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/react-transition-group/esm/TransitionGroup.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport TransitionGroupContext from './TransitionGroupContext';\nimport { getChildMapping, getInitialChildMapping, getNextChildMapping } from './utils/ChildMapping';\n\nvar values = Object.values || function (obj) {\n  return Object.keys(obj).map(function (k) {\n    return obj[k];\n  });\n};\n\nvar defaultProps = {\n  component: 'div',\n  childFactory: function childFactory(child) {\n    return child;\n  }\n};\n/**\n * The `<TransitionGroup>` component manages a set of transition components\n * (`<Transition>` and `<CSSTransition>`) in a list. Like with the transition\n * components, `<TransitionGroup>` is a state machine for managing the mounting\n * and unmounting of components over time.\n *\n * Consider the example below. As items are removed or added to the TodoList the\n * `in` prop is toggled automatically by the `<TransitionGroup>`.\n *\n * Note that `<TransitionGroup>`  does not define any animation behavior!\n * Exactly _how_ a list item animates is up to the individual transition\n * component. This means you can mix and match animations across different list\n * items.\n */\n\nvar TransitionGroup = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(TransitionGroup, _React$Component);\n\n  function TransitionGroup(props, context) {\n    var _this;\n\n    _this = _React$Component.call(this, props, context) || this;\n\n    var handleExited = _this.handleExited.bind(_assertThisInitialized(_this)); // Initial children should all be entering, dependent on appear\n\n\n    _this.state = {\n      contextValue: {\n        isMounting: true\n      },\n      handleExited: handleExited,\n      firstRender: true\n    };\n    return _this;\n  }\n\n  var _proto = TransitionGroup.prototype;\n\n  _proto.componentDidMount = function componentDidMount() {\n    this.mounted = true;\n    this.setState({\n      contextValue: {\n        isMounting: false\n      }\n    });\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.mounted = false;\n  };\n\n  TransitionGroup.getDerivedStateFromProps = function getDerivedStateFromProps(nextProps, _ref) {\n    var prevChildMapping = _ref.children,\n        handleExited = _ref.handleExited,\n        firstRender = _ref.firstRender;\n    return {\n      children: firstRender ? getInitialChildMapping(nextProps, handleExited) : getNextChildMapping(nextProps, prevChildMapping, handleExited),\n      firstRender: false\n    };\n  } // node is `undefined` when user provided `nodeRef` prop\n  ;\n\n  _proto.handleExited = function handleExited(child, node) {\n    var currentChildMapping = getChildMapping(this.props.children);\n    if (child.key in currentChildMapping) return;\n\n    if (child.props.onExited) {\n      child.props.onExited(node);\n    }\n\n    if (this.mounted) {\n      this.setState(function (state) {\n        var children = _extends({}, state.children);\n\n        delete children[child.key];\n        return {\n          children: children\n        };\n      });\n    }\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        Component = _this$props.component,\n        childFactory = _this$props.childFactory,\n        props = _objectWithoutPropertiesLoose(_this$props, [\"component\", \"childFactory\"]);\n\n    var contextValue = this.state.contextValue;\n    var children = values(this.state.children).map(childFactory);\n    delete props.appear;\n    delete props.enter;\n    delete props.exit;\n\n    if (Component === null) {\n      return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\n        value: contextValue\n      }, children);\n    }\n\n    return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\n      value: contextValue\n    }, /*#__PURE__*/React.createElement(Component, props, children));\n  };\n\n  return TransitionGroup;\n}(React.Component);\n\nTransitionGroup.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * `<TransitionGroup>` renders a `<div>` by default. You can change this\n   * behavior by providing a `component` prop.\n   * If you use React v16+ and would like to avoid a wrapping `<div>` element\n   * you can pass in `component={null}`. This is useful if the wrapping div\n   * borks your css styles.\n   */\n  component: PropTypes.any,\n\n  /**\n   * A set of `<Transition>` components, that are toggled `in` and out as they\n   * leave. the `<TransitionGroup>` will inject specific transition props, so\n   * remember to spread them through if you are wrapping the `<Transition>` as\n   * with our `<Fade>` example.\n   *\n   * While this component is meant for multiple `Transition` or `CSSTransition`\n   * children, sometimes you may want to have a single transition child with\n   * content that you want to be transitioned out and in when you change it\n   * (e.g. routes, images etc.) In that case you can change the `key` prop of\n   * the transition child as you change its content, this will cause\n   * `TransitionGroup` to transition the child out and back in.\n   */\n  children: PropTypes.node,\n\n  /**\n   * A convenience prop that enables or disables appear animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  appear: PropTypes.bool,\n\n  /**\n   * A convenience prop that enables or disables enter animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  enter: PropTypes.bool,\n\n  /**\n   * A convenience prop that enables or disables exit animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  exit: PropTypes.bool,\n\n  /**\n   * You may need to apply reactive updates to a child as it is exiting.\n   * This is generally done by using `cloneElement` however in the case of an exiting\n   * child the element has already been removed and not accessible to the consumer.\n   *\n   * If you do need to update a child as it leaves you can provide a `childFactory`\n   * to wrap every child, even the ones that are leaving.\n   *\n   * @type Function(child: ReactElement) -> ReactElement\n   */\n  childFactory: PropTypes.func\n} : {};\nTransitionGroup.defaultProps = defaultProps;\nexport default TransitionGroup;"], "names": [], "mappings": ";;;AAiI4B;AAjI5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,IAAI,SAAS,OAAO,MAAM,IAAI,SAAU,GAAG;IACzC,OAAO,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,SAAU,CAAC;QACrC,OAAO,GAAG,CAAC,EAAE;IACf;AACF;AAEA,IAAI,eAAe;IACjB,WAAW;IACX,cAAc,SAAS,aAAa,KAAK;QACvC,OAAO;IACT;AACF;AACA;;;;;;;;;;;;;CAaC,GAED,IAAI,kBAAkB,WAAW,GAAE,SAAU,gBAAgB;IAC3D,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB;IAEhC,SAAS,gBAAgB,KAAK,EAAE,OAAO;QACrC,IAAI;QAEJ,QAAQ,iBAAiB,IAAI,CAAC,IAAI,EAAE,OAAO,YAAY,IAAI;QAE3D,IAAI,eAAe,MAAM,YAAY,CAAC,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,UAAsB,AAAD,EAAE,SAAS,+DAA+D;QAG1I,MAAM,KAAK,GAAG;YACZ,cAAc;gBACZ,YAAY;YACd;YACA,cAAc;YACd,aAAa;QACf;QACA,OAAO;IACT;IAEA,IAAI,SAAS,gBAAgB,SAAS;IAEtC,OAAO,iBAAiB,GAAG,SAAS;QAClC,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,CAAC;YACZ,cAAc;gBACZ,YAAY;YACd;QACF;IACF;IAEA,OAAO,oBAAoB,GAAG,SAAS;QACrC,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,gBAAgB,wBAAwB,GAAG,SAAS,yBAAyB,SAAS,EAAE,IAAI;QAC1F,IAAI,mBAAmB,KAAK,QAAQ,EAChC,eAAe,KAAK,YAAY,EAChC,cAAc,KAAK,WAAW;QAClC,OAAO;YACL,UAAU,cAAc,CAAA,GAAA,+KAAA,CAAA,yBAAsB,AAAD,EAAE,WAAW,gBAAgB,CAAA,GAAA,+KAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW,kBAAkB;YAC3H,aAAa;QACf;IACF,EAAE,wDAAwD;;IAG1D,OAAO,YAAY,GAAG,SAAS,aAAa,KAAK,EAAE,IAAI;QACrD,IAAI,sBAAsB,CAAA,GAAA,+KAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ;QAC7D,IAAI,MAAM,GAAG,IAAI,qBAAqB;QAEtC,IAAI,MAAM,KAAK,CAAC,QAAQ,EAAE;YACxB,MAAM,KAAK,CAAC,QAAQ,CAAC;QACvB;QAEA,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,QAAQ,CAAC,SAAU,KAAK;gBAC3B,IAAI,WAAW,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,MAAM,QAAQ;gBAE1C,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC;gBAC1B,OAAO;oBACL,UAAU;gBACZ;YACF;QACF;IACF;IAEA,OAAO,MAAM,GAAG,SAAS;QACvB,IAAI,cAAc,IAAI,CAAC,KAAK,EACxB,YAAY,YAAY,SAAS,EACjC,eAAe,YAAY,YAAY,EACvC,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,aAAa;YAAC;YAAa;SAAe;QAEpF,IAAI,eAAe,IAAI,CAAC,KAAK,CAAC,YAAY;QAC1C,IAAI,WAAW,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC;QAC/C,OAAO,MAAM,MAAM;QACnB,OAAO,MAAM,KAAK;QAClB,OAAO,MAAM,IAAI;QAEjB,IAAI,cAAc,MAAM;YACtB,OAAO,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,gLAAA,CAAA,UAAsB,CAAC,QAAQ,EAAE;gBACvE,OAAO;YACT,GAAG;QACL;QAEA,OAAO,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,gLAAA,CAAA,UAAsB,CAAC,QAAQ,EAAE;YACvE,OAAO;QACT,GAAG,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,WAAW,OAAO;IACxD;IAEA,OAAO;AACT,EAAE,6JAAA,CAAA,UAAK,CAAC,SAAS;AAEjB,gBAAgB,SAAS,GAAG,uCAAwC;IAClE;;;;;;GAMC,GACD,WAAW,yIAAA,CAAA,UAAS,CAAC,GAAG;IAExB;;;;;;;;;;;;GAYC,GACD,UAAU,yIAAA,CAAA,UAAS,CAAC,IAAI;IAExB;;;;GAIC,GACD,QAAQ,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEtB;;;;GAIC,GACD,OAAO,yIAAA,CAAA,UAAS,CAAC,IAAI;IAErB;;;;GAIC,GACD,MAAM,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEpB;;;;;;;;;GASC,GACD,cAAc,yIAAA,CAAA,UAAS,CAAC,IAAI;AAC9B;AACA,gBAAgB,YAAY,GAAG;uCAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/react-transition-group/esm/config.js"], "sourcesContent": ["export default {\n  disabled: false\n};"], "names": [], "mappings": ";;;uCAAe;IACb,UAAU;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/react-transition-group/esm/utils/PropTypes.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nexport var timeoutsShape = process.env.NODE_ENV !== 'production' ? PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n  enter: PropTypes.number,\n  exit: PropTypes.number,\n  appear: PropTypes.number\n}).isRequired]) : null;\nexport var classNamesShape = process.env.NODE_ENV !== 'production' ? PropTypes.oneOfType([PropTypes.string, PropTypes.shape({\n  enter: PropTypes.string,\n  exit: PropTypes.string,\n  active: PropTypes.string\n}), PropTypes.shape({\n  enter: PropTypes.string,\n  enterDone: PropTypes.string,\n  enterActive: PropTypes.string,\n  exit: PropTypes.string,\n  exitDone: PropTypes.string,\n  exitActive: PropTypes.string\n})]) : null;"], "names": [], "mappings": ";;;;AAC2B;AAD3B;;AACO,IAAI,gBAAgB,uCAAwC,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;IAAC,yIAAA,CAAA,UAAS,CAAC,MAAM;IAAE,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;QACxH,OAAO,yIAAA,CAAA,UAAS,CAAC,MAAM;QACvB,MAAM,yIAAA,CAAA,UAAS,CAAC,MAAM;QACtB,QAAQ,yIAAA,CAAA,UAAS,CAAC,MAAM;IAC1B,GAAG,UAAU;CAAC;AACP,IAAI,kBAAkB,uCAAwC,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;IAAC,yIAAA,CAAA,UAAS,CAAC,MAAM;IAAE,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;QAC1H,OAAO,yIAAA,CAAA,UAAS,CAAC,MAAM;QACvB,MAAM,yIAAA,CAAA,UAAS,CAAC,MAAM;QACtB,QAAQ,yIAAA,CAAA,UAAS,CAAC,MAAM;IAC1B;IAAI,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;QAClB,OAAO,yIAAA,CAAA,UAAS,CAAC,MAAM;QACvB,WAAW,yIAAA,CAAA,UAAS,CAAC,MAAM;QAC3B,aAAa,yIAAA,CAAA,UAAS,CAAC,MAAM;QAC7B,MAAM,yIAAA,CAAA,UAAS,CAAC,MAAM;QACtB,UAAU,yIAAA,CAAA,UAAS,CAAC,MAAM;QAC1B,YAAY,yIAAA,CAAA,UAAS,CAAC,MAAM;IAC9B;CAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/react-transition-group/esm/utils/reflow.js"], "sourcesContent": ["export var forceReflow = function forceReflow(node) {\n  return node.scrollTop;\n};"], "names": [], "mappings": ";;;AAAO,IAAI,cAAc,SAAS,YAAY,IAAI;IAChD,OAAO,KAAK,SAAS;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3220, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/react-transition-group/esm/Transition.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nimport config from './config';\nimport { timeoutsShape } from './utils/PropTypes';\nimport TransitionGroupContext from './TransitionGroupContext';\nimport { forceReflow } from './utils/reflow';\nexport var UNMOUNTED = 'unmounted';\nexport var EXITED = 'exited';\nexport var ENTERING = 'entering';\nexport var ENTERED = 'entered';\nexport var EXITING = 'exiting';\n/**\n * The Transition component lets you describe a transition from one component\n * state to another _over time_ with a simple declarative API. Most commonly\n * it's used to animate the mounting and unmounting of a component, but can also\n * be used to describe in-place transition states as well.\n *\n * ---\n *\n * **Note**: `Transition` is a platform-agnostic base component. If you're using\n * transitions in CSS, you'll probably want to use\n * [`CSSTransition`](https://reactcommunity.org/react-transition-group/css-transition)\n * instead. It inherits all the features of `Transition`, but contains\n * additional features necessary to play nice with CSS transitions (hence the\n * name of the component).\n *\n * ---\n *\n * By default the `Transition` component does not alter the behavior of the\n * component it renders, it only tracks \"enter\" and \"exit\" states for the\n * components. It's up to you to give meaning and effect to those states. For\n * example we can add styles to a component when it enters or exits:\n *\n * ```jsx\n * import { Transition } from 'react-transition-group';\n *\n * const duration = 300;\n *\n * const defaultStyle = {\n *   transition: `opacity ${duration}ms ease-in-out`,\n *   opacity: 0,\n * }\n *\n * const transitionStyles = {\n *   entering: { opacity: 1 },\n *   entered:  { opacity: 1 },\n *   exiting:  { opacity: 0 },\n *   exited:  { opacity: 0 },\n * };\n *\n * const Fade = ({ in: inProp }) => (\n *   <Transition in={inProp} timeout={duration}>\n *     {state => (\n *       <div style={{\n *         ...defaultStyle,\n *         ...transitionStyles[state]\n *       }}>\n *         I'm a fade Transition!\n *       </div>\n *     )}\n *   </Transition>\n * );\n * ```\n *\n * There are 4 main states a Transition can be in:\n *  - `'entering'`\n *  - `'entered'`\n *  - `'exiting'`\n *  - `'exited'`\n *\n * Transition state is toggled via the `in` prop. When `true` the component\n * begins the \"Enter\" stage. During this stage, the component will shift from\n * its current transition state, to `'entering'` for the duration of the\n * transition and then to the `'entered'` stage once it's complete. Let's take\n * the following example (we'll use the\n * [useState](https://reactjs.org/docs/hooks-reference.html#usestate) hook):\n *\n * ```jsx\n * function App() {\n *   const [inProp, setInProp] = useState(false);\n *   return (\n *     <div>\n *       <Transition in={inProp} timeout={500}>\n *         {state => (\n *           // ...\n *         )}\n *       </Transition>\n *       <button onClick={() => setInProp(true)}>\n *         Click to Enter\n *       </button>\n *     </div>\n *   );\n * }\n * ```\n *\n * When the button is clicked the component will shift to the `'entering'` state\n * and stay there for 500ms (the value of `timeout`) before it finally switches\n * to `'entered'`.\n *\n * When `in` is `false` the same thing happens except the state moves from\n * `'exiting'` to `'exited'`.\n */\n\nvar Transition = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Transition, _React$Component);\n\n  function Transition(props, context) {\n    var _this;\n\n    _this = _React$Component.call(this, props, context) || this;\n    var parentGroup = context; // In the context of a TransitionGroup all enters are really appears\n\n    var appear = parentGroup && !parentGroup.isMounting ? props.enter : props.appear;\n    var initialStatus;\n    _this.appearStatus = null;\n\n    if (props.in) {\n      if (appear) {\n        initialStatus = EXITED;\n        _this.appearStatus = ENTERING;\n      } else {\n        initialStatus = ENTERED;\n      }\n    } else {\n      if (props.unmountOnExit || props.mountOnEnter) {\n        initialStatus = UNMOUNTED;\n      } else {\n        initialStatus = EXITED;\n      }\n    }\n\n    _this.state = {\n      status: initialStatus\n    };\n    _this.nextCallback = null;\n    return _this;\n  }\n\n  Transition.getDerivedStateFromProps = function getDerivedStateFromProps(_ref, prevState) {\n    var nextIn = _ref.in;\n\n    if (nextIn && prevState.status === UNMOUNTED) {\n      return {\n        status: EXITED\n      };\n    }\n\n    return null;\n  } // getSnapshotBeforeUpdate(prevProps) {\n  //   let nextStatus = null\n  //   if (prevProps !== this.props) {\n  //     const { status } = this.state\n  //     if (this.props.in) {\n  //       if (status !== ENTERING && status !== ENTERED) {\n  //         nextStatus = ENTERING\n  //       }\n  //     } else {\n  //       if (status === ENTERING || status === ENTERED) {\n  //         nextStatus = EXITING\n  //       }\n  //     }\n  //   }\n  //   return { nextStatus }\n  // }\n  ;\n\n  var _proto = Transition.prototype;\n\n  _proto.componentDidMount = function componentDidMount() {\n    this.updateStatus(true, this.appearStatus);\n  };\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    var nextStatus = null;\n\n    if (prevProps !== this.props) {\n      var status = this.state.status;\n\n      if (this.props.in) {\n        if (status !== ENTERING && status !== ENTERED) {\n          nextStatus = ENTERING;\n        }\n      } else {\n        if (status === ENTERING || status === ENTERED) {\n          nextStatus = EXITING;\n        }\n      }\n    }\n\n    this.updateStatus(false, nextStatus);\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.cancelNextCallback();\n  };\n\n  _proto.getTimeouts = function getTimeouts() {\n    var timeout = this.props.timeout;\n    var exit, enter, appear;\n    exit = enter = appear = timeout;\n\n    if (timeout != null && typeof timeout !== 'number') {\n      exit = timeout.exit;\n      enter = timeout.enter; // TODO: remove fallback for next major\n\n      appear = timeout.appear !== undefined ? timeout.appear : enter;\n    }\n\n    return {\n      exit: exit,\n      enter: enter,\n      appear: appear\n    };\n  };\n\n  _proto.updateStatus = function updateStatus(mounting, nextStatus) {\n    if (mounting === void 0) {\n      mounting = false;\n    }\n\n    if (nextStatus !== null) {\n      // nextStatus will always be ENTERING or EXITING.\n      this.cancelNextCallback();\n\n      if (nextStatus === ENTERING) {\n        if (this.props.unmountOnExit || this.props.mountOnEnter) {\n          var node = this.props.nodeRef ? this.props.nodeRef.current : ReactDOM.findDOMNode(this); // https://github.com/reactjs/react-transition-group/pull/749\n          // With unmountOnExit or mountOnEnter, the enter animation should happen at the transition between `exited` and `entering`.\n          // To make the animation happen,  we have to separate each rendering and avoid being processed as batched.\n\n          if (node) forceReflow(node);\n        }\n\n        this.performEnter(mounting);\n      } else {\n        this.performExit();\n      }\n    } else if (this.props.unmountOnExit && this.state.status === EXITED) {\n      this.setState({\n        status: UNMOUNTED\n      });\n    }\n  };\n\n  _proto.performEnter = function performEnter(mounting) {\n    var _this2 = this;\n\n    var enter = this.props.enter;\n    var appearing = this.context ? this.context.isMounting : mounting;\n\n    var _ref2 = this.props.nodeRef ? [appearing] : [ReactDOM.findDOMNode(this), appearing],\n        maybeNode = _ref2[0],\n        maybeAppearing = _ref2[1];\n\n    var timeouts = this.getTimeouts();\n    var enterTimeout = appearing ? timeouts.appear : timeouts.enter; // no enter animation skip right to ENTERED\n    // if we are mounting and running this it means appear _must_ be set\n\n    if (!mounting && !enter || config.disabled) {\n      this.safeSetState({\n        status: ENTERED\n      }, function () {\n        _this2.props.onEntered(maybeNode);\n      });\n      return;\n    }\n\n    this.props.onEnter(maybeNode, maybeAppearing);\n    this.safeSetState({\n      status: ENTERING\n    }, function () {\n      _this2.props.onEntering(maybeNode, maybeAppearing);\n\n      _this2.onTransitionEnd(enterTimeout, function () {\n        _this2.safeSetState({\n          status: ENTERED\n        }, function () {\n          _this2.props.onEntered(maybeNode, maybeAppearing);\n        });\n      });\n    });\n  };\n\n  _proto.performExit = function performExit() {\n    var _this3 = this;\n\n    var exit = this.props.exit;\n    var timeouts = this.getTimeouts();\n    var maybeNode = this.props.nodeRef ? undefined : ReactDOM.findDOMNode(this); // no exit animation skip right to EXITED\n\n    if (!exit || config.disabled) {\n      this.safeSetState({\n        status: EXITED\n      }, function () {\n        _this3.props.onExited(maybeNode);\n      });\n      return;\n    }\n\n    this.props.onExit(maybeNode);\n    this.safeSetState({\n      status: EXITING\n    }, function () {\n      _this3.props.onExiting(maybeNode);\n\n      _this3.onTransitionEnd(timeouts.exit, function () {\n        _this3.safeSetState({\n          status: EXITED\n        }, function () {\n          _this3.props.onExited(maybeNode);\n        });\n      });\n    });\n  };\n\n  _proto.cancelNextCallback = function cancelNextCallback() {\n    if (this.nextCallback !== null) {\n      this.nextCallback.cancel();\n      this.nextCallback = null;\n    }\n  };\n\n  _proto.safeSetState = function safeSetState(nextState, callback) {\n    // This shouldn't be necessary, but there are weird race conditions with\n    // setState callbacks and unmounting in testing, so always make sure that\n    // we can cancel any pending setState callbacks after we unmount.\n    callback = this.setNextCallback(callback);\n    this.setState(nextState, callback);\n  };\n\n  _proto.setNextCallback = function setNextCallback(callback) {\n    var _this4 = this;\n\n    var active = true;\n\n    this.nextCallback = function (event) {\n      if (active) {\n        active = false;\n        _this4.nextCallback = null;\n        callback(event);\n      }\n    };\n\n    this.nextCallback.cancel = function () {\n      active = false;\n    };\n\n    return this.nextCallback;\n  };\n\n  _proto.onTransitionEnd = function onTransitionEnd(timeout, handler) {\n    this.setNextCallback(handler);\n    var node = this.props.nodeRef ? this.props.nodeRef.current : ReactDOM.findDOMNode(this);\n    var doesNotHaveTimeoutOrListener = timeout == null && !this.props.addEndListener;\n\n    if (!node || doesNotHaveTimeoutOrListener) {\n      setTimeout(this.nextCallback, 0);\n      return;\n    }\n\n    if (this.props.addEndListener) {\n      var _ref3 = this.props.nodeRef ? [this.nextCallback] : [node, this.nextCallback],\n          maybeNode = _ref3[0],\n          maybeNextCallback = _ref3[1];\n\n      this.props.addEndListener(maybeNode, maybeNextCallback);\n    }\n\n    if (timeout != null) {\n      setTimeout(this.nextCallback, timeout);\n    }\n  };\n\n  _proto.render = function render() {\n    var status = this.state.status;\n\n    if (status === UNMOUNTED) {\n      return null;\n    }\n\n    var _this$props = this.props,\n        children = _this$props.children,\n        _in = _this$props.in,\n        _mountOnEnter = _this$props.mountOnEnter,\n        _unmountOnExit = _this$props.unmountOnExit,\n        _appear = _this$props.appear,\n        _enter = _this$props.enter,\n        _exit = _this$props.exit,\n        _timeout = _this$props.timeout,\n        _addEndListener = _this$props.addEndListener,\n        _onEnter = _this$props.onEnter,\n        _onEntering = _this$props.onEntering,\n        _onEntered = _this$props.onEntered,\n        _onExit = _this$props.onExit,\n        _onExiting = _this$props.onExiting,\n        _onExited = _this$props.onExited,\n        _nodeRef = _this$props.nodeRef,\n        childProps = _objectWithoutPropertiesLoose(_this$props, [\"children\", \"in\", \"mountOnEnter\", \"unmountOnExit\", \"appear\", \"enter\", \"exit\", \"timeout\", \"addEndListener\", \"onEnter\", \"onEntering\", \"onEntered\", \"onExit\", \"onExiting\", \"onExited\", \"nodeRef\"]);\n\n    return (\n      /*#__PURE__*/\n      // allows for nested Transitions\n      React.createElement(TransitionGroupContext.Provider, {\n        value: null\n      }, typeof children === 'function' ? children(status, childProps) : React.cloneElement(React.Children.only(children), childProps))\n    );\n  };\n\n  return Transition;\n}(React.Component);\n\nTransition.contextType = TransitionGroupContext;\nTransition.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * A React reference to DOM element that need to transition:\n   * https://stackoverflow.com/a/51127130/4671932\n   *\n   *   - When `nodeRef` prop is used, `node` is not passed to callback functions\n   *      (e.g. `onEnter`) because user already has direct access to the node.\n   *   - When changing `key` prop of `Transition` in a `TransitionGroup` a new\n   *     `nodeRef` need to be provided to `Transition` with changed `key` prop\n   *     (see\n   *     [test/CSSTransition-test.js](https://github.com/reactjs/react-transition-group/blob/13435f897b3ab71f6e19d724f145596f5910581c/test/CSSTransition-test.js#L362-L437)).\n   */\n  nodeRef: PropTypes.shape({\n    current: typeof Element === 'undefined' ? PropTypes.any : function (propValue, key, componentName, location, propFullName, secret) {\n      var value = propValue[key];\n      return PropTypes.instanceOf(value && 'ownerDocument' in value ? value.ownerDocument.defaultView.Element : Element)(propValue, key, componentName, location, propFullName, secret);\n    }\n  }),\n\n  /**\n   * A `function` child can be used instead of a React element. This function is\n   * called with the current transition status (`'entering'`, `'entered'`,\n   * `'exiting'`, `'exited'`), which can be used to apply context\n   * specific props to a component.\n   *\n   * ```jsx\n   * <Transition in={this.state.in} timeout={150}>\n   *   {state => (\n   *     <MyComponent className={`fade fade-${state}`} />\n   *   )}\n   * </Transition>\n   * ```\n   */\n  children: PropTypes.oneOfType([PropTypes.func.isRequired, PropTypes.element.isRequired]).isRequired,\n\n  /**\n   * Show the component; triggers the enter or exit states\n   */\n  in: PropTypes.bool,\n\n  /**\n   * By default the child component is mounted immediately along with\n   * the parent `Transition` component. If you want to \"lazy mount\" the component on the\n   * first `in={true}` you can set `mountOnEnter`. After the first enter transition the component will stay\n   * mounted, even on \"exited\", unless you also specify `unmountOnExit`.\n   */\n  mountOnEnter: PropTypes.bool,\n\n  /**\n   * By default the child component stays mounted after it reaches the `'exited'` state.\n   * Set `unmountOnExit` if you'd prefer to unmount the component after it finishes exiting.\n   */\n  unmountOnExit: PropTypes.bool,\n\n  /**\n   * By default the child component does not perform the enter transition when\n   * it first mounts, regardless of the value of `in`. If you want this\n   * behavior, set both `appear` and `in` to `true`.\n   *\n   * > **Note**: there are no special appear states like `appearing`/`appeared`, this prop\n   * > only adds an additional enter transition. However, in the\n   * > `<CSSTransition>` component that first enter transition does result in\n   * > additional `.appear-*` classes, that way you can choose to style it\n   * > differently.\n   */\n  appear: PropTypes.bool,\n\n  /**\n   * Enable or disable enter transitions.\n   */\n  enter: PropTypes.bool,\n\n  /**\n   * Enable or disable exit transitions.\n   */\n  exit: PropTypes.bool,\n\n  /**\n   * The duration of the transition, in milliseconds.\n   * Required unless `addEndListener` is provided.\n   *\n   * You may specify a single timeout for all transitions:\n   *\n   * ```jsx\n   * timeout={500}\n   * ```\n   *\n   * or individually:\n   *\n   * ```jsx\n   * timeout={{\n   *  appear: 500,\n   *  enter: 300,\n   *  exit: 500,\n   * }}\n   * ```\n   *\n   * - `appear` defaults to the value of `enter`\n   * - `enter` defaults to `0`\n   * - `exit` defaults to `0`\n   *\n   * @type {number | { enter?: number, exit?: number, appear?: number }}\n   */\n  timeout: function timeout(props) {\n    var pt = timeoutsShape;\n    if (!props.addEndListener) pt = pt.isRequired;\n\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return pt.apply(void 0, [props].concat(args));\n  },\n\n  /**\n   * Add a custom transition end trigger. Called with the transitioning\n   * DOM node and a `done` callback. Allows for more fine grained transition end\n   * logic. Timeouts are still used as a fallback if provided.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * ```jsx\n   * addEndListener={(node, done) => {\n   *   // use the css transitionend event to mark the finish of a transition\n   *   node.addEventListener('transitionend', done, false);\n   * }}\n   * ```\n   */\n  addEndListener: PropTypes.func,\n\n  /**\n   * Callback fired before the \"entering\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool) -> void\n   */\n  onEnter: PropTypes.func,\n\n  /**\n   * Callback fired after the \"entering\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntering: PropTypes.func,\n\n  /**\n   * Callback fired after the \"entered\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool) -> void\n   */\n  onEntered: PropTypes.func,\n\n  /**\n   * Callback fired before the \"exiting\" status is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExit: PropTypes.func,\n\n  /**\n   * Callback fired after the \"exiting\" status is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExiting: PropTypes.func,\n\n  /**\n   * Callback fired after the \"exited\" status is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExited: PropTypes.func\n} : {}; // Name the function so it is clearer in the documentation\n\nfunction noop() {}\n\nTransition.defaultProps = {\n  in: false,\n  mountOnEnter: false,\n  unmountOnExit: false,\n  appear: false,\n  enter: true,\n  exit: true,\n  onEnter: noop,\n  onEntering: noop,\n  onEntered: noop,\n  onExit: noop,\n  onExiting: noop,\n  onExited: noop\n};\nTransition.UNMOUNTED = UNMOUNTED;\nTransition.EXITED = EXITED;\nTransition.ENTERING = ENTERING;\nTransition.ENTERED = ENTERED;\nTransition.EXITING = EXITING;\nexport default Transition;"], "names": [], "mappings": ";;;;;;;;AA+ZuB;AA/ZvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AACO,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,UAAU;AACrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA0FC,GAED,IAAI,aAAa,WAAW,GAAE,SAAU,gBAAgB;IACtD,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,YAAY;IAE3B,SAAS,WAAW,KAAK,EAAE,OAAO;QAChC,IAAI;QAEJ,QAAQ,iBAAiB,IAAI,CAAC,IAAI,EAAE,OAAO,YAAY,IAAI;QAC3D,IAAI,cAAc,SAAS,oEAAoE;QAE/F,IAAI,SAAS,eAAe,CAAC,YAAY,UAAU,GAAG,MAAM,KAAK,GAAG,MAAM,MAAM;QAChF,IAAI;QACJ,MAAM,YAAY,GAAG;QAErB,IAAI,MAAM,EAAE,EAAE;YACZ,IAAI,QAAQ;gBACV,gBAAgB;gBAChB,MAAM,YAAY,GAAG;YACvB,OAAO;gBACL,gBAAgB;YAClB;QACF,OAAO;YACL,IAAI,MAAM,aAAa,IAAI,MAAM,YAAY,EAAE;gBAC7C,gBAAgB;YAClB,OAAO;gBACL,gBAAgB;YAClB;QACF;QAEA,MAAM,KAAK,GAAG;YACZ,QAAQ;QACV;QACA,MAAM,YAAY,GAAG;QACrB,OAAO;IACT;IAEA,WAAW,wBAAwB,GAAG,SAAS,yBAAyB,IAAI,EAAE,SAAS;QACrF,IAAI,SAAS,KAAK,EAAE;QAEpB,IAAI,UAAU,UAAU,MAAM,KAAK,WAAW;YAC5C,OAAO;gBACL,QAAQ;YACV;QACF;QAEA,OAAO;IACT,EAAE,uCAAuC;;IAkBzC,IAAI,SAAS,WAAW,SAAS;IAEjC,OAAO,iBAAiB,GAAG,SAAS;QAClC,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,YAAY;IAC3C;IAEA,OAAO,kBAAkB,GAAG,SAAS,mBAAmB,SAAS;QAC/D,IAAI,aAAa;QAEjB,IAAI,cAAc,IAAI,CAAC,KAAK,EAAE;YAC5B,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM;YAE9B,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE;gBACjB,IAAI,WAAW,YAAY,WAAW,SAAS;oBAC7C,aAAa;gBACf;YACF,OAAO;gBACL,IAAI,WAAW,YAAY,WAAW,SAAS;oBAC7C,aAAa;gBACf;YACF;QACF;QAEA,IAAI,CAAC,YAAY,CAAC,OAAO;IAC3B;IAEA,OAAO,oBAAoB,GAAG,SAAS;QACrC,IAAI,CAAC,kBAAkB;IACzB;IAEA,OAAO,WAAW,GAAG,SAAS;QAC5B,IAAI,UAAU,IAAI,CAAC,KAAK,CAAC,OAAO;QAChC,IAAI,MAAM,OAAO;QACjB,OAAO,QAAQ,SAAS;QAExB,IAAI,WAAW,QAAQ,OAAO,YAAY,UAAU;YAClD,OAAO,QAAQ,IAAI;YACnB,QAAQ,QAAQ,KAAK,EAAE,uCAAuC;YAE9D,SAAS,QAAQ,MAAM,KAAK,YAAY,QAAQ,MAAM,GAAG;QAC3D;QAEA,OAAO;YACL,MAAM;YACN,OAAO;YACP,QAAQ;QACV;IACF;IAEA,OAAO,YAAY,GAAG,SAAS,aAAa,QAAQ,EAAE,UAAU;QAC9D,IAAI,aAAa,KAAK,GAAG;YACvB,WAAW;QACb;QAEA,IAAI,eAAe,MAAM;YACvB,iDAAiD;YACjD,IAAI,CAAC,kBAAkB;YAEvB,IAAI,eAAe,UAAU;gBAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;oBACvD,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,GAAG,oKAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,IAAI,GAAG,6DAA6D;oBACtJ,2HAA2H;oBAC3H,0GAA0G;oBAE1G,IAAI,MAAM,CAAA,GAAA,yKAAA,CAAA,cAAW,AAAD,EAAE;gBACxB;gBAEA,IAAI,CAAC,YAAY,CAAC;YACpB,OAAO;gBACL,IAAI,CAAC,WAAW;YAClB;QACF,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,QAAQ;YACnE,IAAI,CAAC,QAAQ,CAAC;gBACZ,QAAQ;YACV;QACF;IACF;IAEA,OAAO,YAAY,GAAG,SAAS,aAAa,QAAQ;QAClD,IAAI,SAAS,IAAI;QAEjB,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK;QAC5B,IAAI,YAAY,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG;QAEzD,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;YAAC;SAAU,GAAG;YAAC,oKAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,IAAI;YAAG;SAAU,EAClF,YAAY,KAAK,CAAC,EAAE,EACpB,iBAAiB,KAAK,CAAC,EAAE;QAE7B,IAAI,WAAW,IAAI,CAAC,WAAW;QAC/B,IAAI,eAAe,YAAY,SAAS,MAAM,GAAG,SAAS,KAAK,EAAE,2CAA2C;QAC5G,oEAAoE;QAEpE,IAAI,CAAC,YAAY,CAAC,SAAS,gKAAA,CAAA,UAAM,CAAC,QAAQ,EAAE;YAC1C,IAAI,CAAC,YAAY,CAAC;gBAChB,QAAQ;YACV,GAAG;gBACD,OAAO,KAAK,CAAC,SAAS,CAAC;YACzB;YACA;QACF;QAEA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW;QAC9B,IAAI,CAAC,YAAY,CAAC;YAChB,QAAQ;QACV,GAAG;YACD,OAAO,KAAK,CAAC,UAAU,CAAC,WAAW;YAEnC,OAAO,eAAe,CAAC,cAAc;gBACnC,OAAO,YAAY,CAAC;oBAClB,QAAQ;gBACV,GAAG;oBACD,OAAO,KAAK,CAAC,SAAS,CAAC,WAAW;gBACpC;YACF;QACF;IACF;IAEA,OAAO,WAAW,GAAG,SAAS;QAC5B,IAAI,SAAS,IAAI;QAEjB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;QAC1B,IAAI,WAAW,IAAI,CAAC,WAAW;QAC/B,IAAI,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,YAAY,oKAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,IAAI,GAAG,yCAAyC;QAEtH,IAAI,CAAC,QAAQ,gKAAA,CAAA,UAAM,CAAC,QAAQ,EAAE;YAC5B,IAAI,CAAC,YAAY,CAAC;gBAChB,QAAQ;YACV,GAAG;gBACD,OAAO,KAAK,CAAC,QAAQ,CAAC;YACxB;YACA;QACF;QAEA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QAClB,IAAI,CAAC,YAAY,CAAC;YAChB,QAAQ;QACV,GAAG;YACD,OAAO,KAAK,CAAC,SAAS,CAAC;YAEvB,OAAO,eAAe,CAAC,SAAS,IAAI,EAAE;gBACpC,OAAO,YAAY,CAAC;oBAClB,QAAQ;gBACV,GAAG;oBACD,OAAO,KAAK,CAAC,QAAQ,CAAC;gBACxB;YACF;QACF;IACF;IAEA,OAAO,kBAAkB,GAAG,SAAS;QACnC,IAAI,IAAI,CAAC,YAAY,KAAK,MAAM;YAC9B,IAAI,CAAC,YAAY,CAAC,MAAM;YACxB,IAAI,CAAC,YAAY,GAAG;QACtB;IACF;IAEA,OAAO,YAAY,GAAG,SAAS,aAAa,SAAS,EAAE,QAAQ;QAC7D,wEAAwE;QACxE,yEAAyE;QACzE,iEAAiE;QACjE,WAAW,IAAI,CAAC,eAAe,CAAC;QAChC,IAAI,CAAC,QAAQ,CAAC,WAAW;IAC3B;IAEA,OAAO,eAAe,GAAG,SAAS,gBAAgB,QAAQ;QACxD,IAAI,SAAS,IAAI;QAEjB,IAAI,SAAS;QAEb,IAAI,CAAC,YAAY,GAAG,SAAU,KAAK;YACjC,IAAI,QAAQ;gBACV,SAAS;gBACT,OAAO,YAAY,GAAG;gBACtB,SAAS;YACX;QACF;QAEA,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;YACzB,SAAS;QACX;QAEA,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA,OAAO,eAAe,GAAG,SAAS,gBAAgB,OAAO,EAAE,OAAO;QAChE,IAAI,CAAC,eAAe,CAAC;QACrB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,GAAG,oKAAA,CAAA,UAAQ,CAAC,WAAW,CAAC,IAAI;QACtF,IAAI,+BAA+B,WAAW,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc;QAEhF,IAAI,CAAC,QAAQ,8BAA8B;YACzC,WAAW,IAAI,CAAC,YAAY,EAAE;YAC9B;QACF;QAEA,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;YAC7B,IAAI,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;gBAAC,IAAI,CAAC,YAAY;aAAC,GAAG;gBAAC;gBAAM,IAAI,CAAC,YAAY;aAAC,EAC5E,YAAY,KAAK,CAAC,EAAE,EACpB,oBAAoB,KAAK,CAAC,EAAE;YAEhC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,WAAW;QACvC;QAEA,IAAI,WAAW,MAAM;YACnB,WAAW,IAAI,CAAC,YAAY,EAAE;QAChC;IACF;IAEA,OAAO,MAAM,GAAG,SAAS;QACvB,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM;QAE9B,IAAI,WAAW,WAAW;YACxB,OAAO;QACT;QAEA,IAAI,cAAc,IAAI,CAAC,KAAK,EACxB,WAAW,YAAY,QAAQ,EAC/B,MAAM,YAAY,EAAE,EACpB,gBAAgB,YAAY,YAAY,EACxC,iBAAiB,YAAY,aAAa,EAC1C,UAAU,YAAY,MAAM,EAC5B,SAAS,YAAY,KAAK,EAC1B,QAAQ,YAAY,IAAI,EACxB,WAAW,YAAY,OAAO,EAC9B,kBAAkB,YAAY,cAAc,EAC5C,WAAW,YAAY,OAAO,EAC9B,cAAc,YAAY,UAAU,EACpC,aAAa,YAAY,SAAS,EAClC,UAAU,YAAY,MAAM,EAC5B,aAAa,YAAY,SAAS,EAClC,YAAY,YAAY,QAAQ,EAChC,WAAW,YAAY,OAAO,EAC9B,aAAa,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,aAAa;YAAC;YAAY;YAAM;YAAgB;YAAiB;YAAU;YAAS;YAAQ;YAAW;YAAkB;YAAW;YAAc;YAAa;YAAU;YAAa;YAAY;SAAU;QAE3P,OACE,WAAW,GACX,gCAAgC;QAChC,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,gLAAA,CAAA,UAAsB,CAAC,QAAQ,EAAE;YACnD,OAAO;QACT,GAAG,OAAO,aAAa,aAAa,SAAS,QAAQ,cAAc,6JAAA,CAAA,UAAK,CAAC,YAAY,CAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;IAEzH;IAEA,OAAO;AACT,EAAE,6JAAA,CAAA,UAAK,CAAC,SAAS;AAEjB,WAAW,WAAW,GAAG,gLAAA,CAAA,UAAsB;AAC/C,WAAW,SAAS,GAAG,uCAAwC;IAC7D;;;;;;;;;;GAUC,GACD,SAAS,yIAAA,CAAA,UAAS,CAAC,KAAK,CAAC;QACvB,SAAS,OAAO,YAAY,cAAc,yIAAA,CAAA,UAAS,CAAC,GAAG,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM;YAC/H,IAAI,QAAQ,SAAS,CAAC,IAAI;YAC1B,OAAO,yIAAA,CAAA,UAAS,CAAC,UAAU,CAAC,SAAS,mBAAmB,QAAQ,MAAM,aAAa,CAAC,WAAW,CAAC,OAAO,GAAG,SAAS,WAAW,KAAK,eAAe,UAAU,cAAc;QAC5K;IACF;IAEA;;;;;;;;;;;;;GAaC,GACD,UAAU,yIAAA,CAAA,UAAS,CAAC,SAAS,CAAC;QAAC,yIAAA,CAAA,UAAS,CAAC,IAAI,CAAC,UAAU;QAAE,yIAAA,CAAA,UAAS,CAAC,OAAO,CAAC,UAAU;KAAC,EAAE,UAAU;IAEnG;;GAEC,GACD,IAAI,yIAAA,CAAA,UAAS,CAAC,IAAI;IAElB;;;;;GAKC,GACD,cAAc,yIAAA,CAAA,UAAS,CAAC,IAAI;IAE5B;;;GAGC,GACD,eAAe,yIAAA,CAAA,UAAS,CAAC,IAAI;IAE7B;;;;;;;;;;GAUC,GACD,QAAQ,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEtB;;GAEC,GACD,OAAO,yIAAA,CAAA,UAAS,CAAC,IAAI;IAErB;;GAEC,GACD,MAAM,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEpB;;;;;;;;;;;;;;;;;;;;;;;;;GAyBC,GACD,SAAS,SAAS,QAAQ,KAAK;QAC7B,IAAI,KAAK,4KAAA,CAAA,gBAAa;QACtB,IAAI,CAAC,MAAM,cAAc,EAAE,KAAK,GAAG,UAAU;QAE7C,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,GAAG,OAAO,MAAM,OAAQ;YAC1G,IAAI,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,KAAK;QAClC;QAEA,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG;YAAC;SAAM,CAAC,MAAM,CAAC;IACzC;IAEA;;;;;;;;;;;;;GAaC,GACD,gBAAgB,yIAAA,CAAA,UAAS,CAAC,IAAI;IAE9B;;;;;;;GAOC,GACD,SAAS,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEvB;;;;;;;GAOC,GACD,YAAY,yIAAA,CAAA,UAAS,CAAC,IAAI;IAE1B;;;;;;;GAOC,GACD,WAAW,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEzB;;;;;;GAMC,GACD,QAAQ,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEtB;;;;;;GAMC,GACD,WAAW,yIAAA,CAAA,UAAS,CAAC,IAAI;IAEzB;;;;;;GAMC,GACD,UAAU,yIAAA,CAAA,UAAS,CAAC,IAAI;AAC1B,0CAAQ,0DAA0D;AAElE,SAAS,QAAQ;AAEjB,WAAW,YAAY,GAAG;IACxB,IAAI;IACJ,cAAc;IACd,eAAe;IACf,QAAQ;IACR,OAAO;IACP,MAAM;IACN,SAAS;IACT,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,WAAW;IACX,UAAU;AACZ;AACA,WAAW,SAAS,GAAG;AACvB,WAAW,MAAM,GAAG;AACpB,WAAW,QAAQ,GAAG;AACtB,WAAW,OAAO,GAAG;AACrB,WAAW,OAAO,GAAG;uCACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3780, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/material/node_modules/react-is/cjs/react-is.development.js"], "sourcesContent": ["/**\n * @license React\n * react-is.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function typeOf(object) {\n      if (\"object\" === typeof object && null !== object) {\n        var $$typeof = object.$$typeof;\n        switch ($$typeof) {\n          case REACT_ELEMENT_TYPE:\n            switch (((object = object.type), object)) {\n              case REACT_FRAGMENT_TYPE:\n              case REACT_PROFILER_TYPE:\n              case REACT_STRICT_MODE_TYPE:\n              case REACT_SUSPENSE_TYPE:\n              case REACT_SUSPENSE_LIST_TYPE:\n              case REACT_VIEW_TRANSITION_TYPE:\n                return object;\n              default:\n                switch (((object = object && object.$$typeof), object)) {\n                  case REACT_CONTEXT_TYPE:\n                  case REACT_FORWARD_REF_TYPE:\n                  case REACT_LAZY_TYPE:\n                  case REACT_MEMO_TYPE:\n                    return object;\n                  case REACT_CONSUMER_TYPE:\n                    return object;\n                  default:\n                    return $$typeof;\n                }\n            }\n          case REACT_PORTAL_TYPE:\n            return $$typeof;\n        }\n      }\n    }\n    var REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_VIEW_TRANSITION_TYPE = Symbol.for(\"react.view_transition\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\");\n    exports.ContextConsumer = REACT_CONSUMER_TYPE;\n    exports.ContextProvider = REACT_CONTEXT_TYPE;\n    exports.Element = REACT_ELEMENT_TYPE;\n    exports.ForwardRef = REACT_FORWARD_REF_TYPE;\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.Lazy = REACT_LAZY_TYPE;\n    exports.Memo = REACT_MEMO_TYPE;\n    exports.Portal = REACT_PORTAL_TYPE;\n    exports.Profiler = REACT_PROFILER_TYPE;\n    exports.StrictMode = REACT_STRICT_MODE_TYPE;\n    exports.Suspense = REACT_SUSPENSE_TYPE;\n    exports.SuspenseList = REACT_SUSPENSE_LIST_TYPE;\n    exports.isContextConsumer = function (object) {\n      return typeOf(object) === REACT_CONSUMER_TYPE;\n    };\n    exports.isContextProvider = function (object) {\n      return typeOf(object) === REACT_CONTEXT_TYPE;\n    };\n    exports.isElement = function (object) {\n      return (\n        \"object\" === typeof object &&\n        null !== object &&\n        object.$$typeof === REACT_ELEMENT_TYPE\n      );\n    };\n    exports.isForwardRef = function (object) {\n      return typeOf(object) === REACT_FORWARD_REF_TYPE;\n    };\n    exports.isFragment = function (object) {\n      return typeOf(object) === REACT_FRAGMENT_TYPE;\n    };\n    exports.isLazy = function (object) {\n      return typeOf(object) === REACT_LAZY_TYPE;\n    };\n    exports.isMemo = function (object) {\n      return typeOf(object) === REACT_MEMO_TYPE;\n    };\n    exports.isPortal = function (object) {\n      return typeOf(object) === REACT_PORTAL_TYPE;\n    };\n    exports.isProfiler = function (object) {\n      return typeOf(object) === REACT_PROFILER_TYPE;\n    };\n    exports.isStrictMode = function (object) {\n      return typeOf(object) === REACT_STRICT_MODE_TYPE;\n    };\n    exports.isSuspense = function (object) {\n      return typeOf(object) === REACT_SUSPENSE_TYPE;\n    };\n    exports.isSuspenseList = function (object) {\n      return typeOf(object) === REACT_SUSPENSE_LIST_TYPE;\n    };\n    exports.isValidElementType = function (type) {\n      return \"string\" === typeof type ||\n        \"function\" === typeof type ||\n        type === REACT_FRAGMENT_TYPE ||\n        type === REACT_PROFILER_TYPE ||\n        type === REACT_STRICT_MODE_TYPE ||\n        type === REACT_SUSPENSE_TYPE ||\n        type === REACT_SUSPENSE_LIST_TYPE ||\n        (\"object\" === typeof type &&\n          null !== type &&\n          (type.$$typeof === REACT_LAZY_TYPE ||\n            type.$$typeof === REACT_MEMO_TYPE ||\n            type.$$typeof === REACT_CONTEXT_TYPE ||\n            type.$$typeof === REACT_CONSUMER_TYPE ||\n            type.$$typeof === REACT_FORWARD_REF_TYPE ||\n            type.$$typeof === REACT_CLIENT_REFERENCE ||\n            void 0 !== type.getModuleId))\n        ? !0\n        : !1;\n    };\n    exports.typeOf = typeOf;\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,OAAO,MAAM;QACpB,IAAI,aAAa,OAAO,UAAU,SAAS,QAAQ;YACjD,IAAI,WAAW,OAAO,QAAQ;YAC9B,OAAQ;gBACN,KAAK;oBACH,OAAS,AAAC,SAAS,OAAO,IAAI,EAAG;wBAC/B,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;4BACH,OAAO;wBACT;4BACE,OAAS,AAAC,SAAS,UAAU,OAAO,QAAQ,EAAG;gCAC7C,KAAK;gCACL,KAAK;gCACL,KAAK;gCACL,KAAK;oCACH,OAAO;gCACT,KAAK;oCACH,OAAO;gCACT;oCACE,OAAO;4BACX;oBACJ;gBACF,KAAK;oBA<PERSON>,OAAO;YACX;QACF;IACF;IACA,IAAI,qBAAqB,OAAO,GAAG,CAAC,+BAClC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,6BAA6B,OAAO,GAAG,CAAC,0BACxC,yBAAyB,OAAO,GAAG,CAAC;IACtC,QAAQ,eAAe,GAAG;IAC1B,QAAQ,eAAe,GAAG;IAC1B,QAAQ,OAAO,GAAG;IAClB,QAAQ,UAAU,GAAG;IACrB,QAAQ,QAAQ,GAAG;IACnB,QAAQ,IAAI,GAAG;IACf,QAAQ,IAAI,GAAG;IACf,QAAQ,MAAM,GAAG;IACjB,QAAQ,QAAQ,GAAG;IACnB,QAAQ,UAAU,GAAG;IACrB,QAAQ,QAAQ,GAAG;IACnB,QAAQ,YAAY,GAAG;IACvB,QAAQ,iBAAiB,GAAG,SAAU,MAAM;QAC1C,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,iBAAiB,GAAG,SAAU,MAAM;QAC1C,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,SAAS,GAAG,SAAU,MAAM;QAClC,OACE,aAAa,OAAO,UACpB,SAAS,UACT,OAAO,QAAQ,KAAK;IAExB;IACA,QAAQ,YAAY,GAAG,SAAU,MAAM;QACrC,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,UAAU,GAAG,SAAU,MAAM;QACnC,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,MAAM,GAAG,SAAU,MAAM;QAC/B,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,MAAM,GAAG,SAAU,MAAM;QAC/B,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,QAAQ,GAAG,SAAU,MAAM;QACjC,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,UAAU,GAAG,SAAU,MAAM;QACnC,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,YAAY,GAAG,SAAU,MAAM;QACrC,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,UAAU,GAAG,SAAU,MAAM;QACnC,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,cAAc,GAAG,SAAU,MAAM;QACvC,OAAO,OAAO,YAAY;IAC5B;IACA,QAAQ,kBAAkB,GAAG,SAAU,IAAI;QACzC,OAAO,aAAa,OAAO,QACzB,eAAe,OAAO,QACtB,SAAS,uBACT,SAAS,uBACT,SAAS,0BACT,SAAS,uBACT,SAAS,4BACR,aAAa,OAAO,QACnB,SAAS,QACT,CAAC,KAAK,QAAQ,KAAK,mBACjB,KAAK,QAAQ,KAAK,mBAClB,KAAK,QAAQ,KAAK,sBAClB,KAAK,QAAQ,KAAK,uBAClB,KAAK,QAAQ,KAAK,0BAClB,KAAK,QAAQ,KAAK,0BAClB,KAAK,MAAM,KAAK,WAAW,IAC7B,CAAC,IACD,CAAC;IACP;IACA,QAAQ,MAAM,GAAG;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3883, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/material/node_modules/react-is/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3896, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/Add.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z\"\n}), 'Add');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3913, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/Business.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 7V3H2v18h20V7zM6 19H4v-2h2zm0-4H4v-2h2zm0-4H4V9h2zm0-4H4V5h2zm4 12H8v-2h2zm0-4H8v-2h2zm0-4H8V9h2zm0-4H8V5h2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8zm-2-8h-2v2h2zm0 4h-2v2h2z\"\n}), 'Business');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3930, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/Edit.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z\"\n}), 'Edit');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3947, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/CalendarToday.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M20 3h-1V1h-2v2H7V1H5v2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m0 18H4V8h16z\"\n}), 'CalendarToday');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3964, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/Menu.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M3 18h18v-2H3zm0-5h18v-2H3zm0-7v2h18V6z\"\n}), 'Menu');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3981, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/ChevronLeft.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M15.41 7.41 14 6l-6 6 6 6 1.41-1.41L10.83 12z\"\n}), 'ChevronLeft');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/Dashboard.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M3 13h8V3H3zm0 8h8v-6H3zm10 0h8V11h-8zm0-18v6h8V3z\"\n}), 'Dashboard');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4015, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/AccountBalance.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M4 10h3v7H4zm6.5 0h3v7h-3zM2 19h20v3H2zm15-9h3v7h-3zm-5-9L2 6v2h20V6z\"\n}), 'AccountBalance');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4032, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/Receipt.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M18 17H6v-2h12zm0-4H6v-2h12zm0-4H6V7h12zM3 22l1.5-1.5L6 22l1.5-1.5L9 22l1.5-1.5L12 22l1.5-1.5L15 22l1.5-1.5L18 22l1.5-1.5L21 22V2l-1.5 1.5L18 2l-1.5 1.5L15 2l-1.5 1.5L12 2l-1.5 1.5L9 2 7.5 3.5 6 2 4.5 3.5 3 2z\"\n}), 'Receipt');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4049, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/Assessment.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2M9 17H7v-7h2zm4 0h-2V7h2zm4 0h-2v-4h2z\"\n}), 'Assessment');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4066, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/Settings.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6\"\n}), 'Settings');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4083, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/ExpandLess.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"m12 8-6 6 1.41 1.41L12 10.83l4.59 4.58L18 14z\"\n}), 'ExpandLess');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/ExpandMore.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z\"\n}), 'ExpandMore');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/Notifications.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2m6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1z\"\n}), 'Notifications');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/AccountCircle.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6m0 14c-2.03 0-4.43-.82-6.14-2.88C7.55 15.8 9.68 15 12 15s4.45.8 6.14 2.12C16.43 19.18 14.03 20 12 20\"\n}), 'AccountCircle');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/Logout.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"m17 7-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4z\"\n}), 'Logout');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/Home.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\"\n}), 'Home');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/%40mui/icons-material/esm/NavigateNext.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M10 6 8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"\n}), 'NavigateNext');"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;uCAIe,CAAA,GAAA,qKAAA,CAAA,UAAa,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;IACrD,GAAG;AACL,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/node_projects/accounting-app/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}