{"version": 3, "sources": ["../../src/tidb-serverless/session.ts"], "sourcesContent": ["import type { Connection, ExecuteOptions, FullResult, Tx } from '@tidbcloud/serverless';\nimport { Column } from '~/column.ts';\n\nimport { entityKind, is } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { MySqlDialect } from '~/mysql-core/dialect.ts';\nimport type { SelectedFieldsOrdered } from '~/mysql-core/query-builders/select.types.ts';\nimport {\n\tMySqlPreparedQuery,\n\ttype MySqlPreparedQueryConfig,\n\ttype MySqlPreparedQueryHKT,\n\ttype MySqlQueryResultHKT,\n\tMySqlSession,\n\tMySqlTransaction,\n} from '~/mysql-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, type SQL, sql } from '~/sql/sql.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\n\nconst executeRawConfig = { fullResult: true } satisfies ExecuteOptions;\nconst queryConfig = { arrayMode: true } satisfies ExecuteOptions;\n\nexport class TiDBServerlessPreparedQuery<T extends MySqlPreparedQueryConfig> extends MySqlPreparedQuery<T> {\n\tstatic override readonly [entityKind]: string = 'TiDBPreparedQuery';\n\n\tconstructor(\n\t\tprivate client: Tx | Connection,\n\t\tprivate queryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\t// Keys that were used in $default and the value that was generated for them\n\t\tprivate generatedIds?: Record<string, unknown>[],\n\t\t// Keys that should be returned, it has the column with all properries + key from object\n\t\tprivate returningIds?: SelectedFieldsOrdered,\n\t) {\n\t\tsuper();\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\tthis.logger.logQuery(this.queryString, params);\n\n\t\tconst { fields, client, queryString, joinsNotNullableMap, customResultMapper, returningIds, generatedIds } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst res = await client.execute(queryString, params, executeRawConfig) as FullResult;\n\t\t\tconst insertId = res.lastInsertId ?? 0;\n\t\t\tconst affectedRows = res.rowsAffected ?? 0;\n\t\t\t// for each row, I need to check keys from\n\t\t\tif (returningIds) {\n\t\t\t\tconst returningResponse = [];\n\t\t\t\tlet j = 0;\n\t\t\t\tfor (let i = insertId; i < insertId + affectedRows; i++) {\n\t\t\t\t\tfor (const column of returningIds) {\n\t\t\t\t\t\tconst key = returningIds[0]!.path[0]!;\n\t\t\t\t\t\tif (is(column.field, Column)) {\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\tif (column.field.primary && column.field.autoIncrement) {\n\t\t\t\t\t\t\t\treturningResponse.push({ [key]: i });\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (column.field.defaultFn && generatedIds) {\n\t\t\t\t\t\t\t\t// generatedIds[rowIdx][key]\n\t\t\t\t\t\t\t\treturningResponse.push({ [key]: generatedIds[j]![key] });\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tj++;\n\t\t\t\t}\n\n\t\t\t\treturn returningResponse;\n\t\t\t}\n\t\t\treturn res;\n\t\t}\n\n\t\tconst rows = await client.execute(queryString, params, queryConfig) as unknown[][];\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows);\n\t\t}\n\n\t\treturn rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t}\n\n\toverride iterator(_placeholderValues?: Record<string, unknown>): AsyncGenerator<T['iterator']> {\n\t\tthrow new Error('Streaming is not supported by the TiDB Cloud Serverless driver');\n\t}\n}\n\nexport interface TiDBServerlessSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class TiDBServerlessSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends MySqlSession<TiDBServerlessQueryResultHKT, TiDBServerlessPreparedQueryHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'TiDBServerlessSession';\n\n\tprivate logger: Logger;\n\tprivate client: Tx | Connection;\n\n\tconstructor(\n\t\tprivate baseClient: Connection,\n\t\tdialect: MySqlDialect,\n\t\ttx: Tx | undefined,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: TiDBServerlessSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.client = tx ?? baseClient;\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends MySqlPreparedQueryConfig = MySqlPreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\tgeneratedIds?: Record<string, unknown>[],\n\t\treturningIds?: SelectedFieldsOrdered,\n\t): MySqlPreparedQuery<T> {\n\t\treturn new TiDBServerlessPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\tcustomResultMapper,\n\t\t\tgeneratedIds,\n\t\t\treturningIds,\n\t\t);\n\t}\n\n\toverride all<T = unknown>(query: SQL): Promise<T[]> {\n\t\tconst querySql = this.dialect.sqlToQuery(query);\n\t\tthis.logger.logQuery(querySql.sql, querySql.params);\n\t\treturn this.client.execute(querySql.sql, querySql.params) as Promise<T[]>;\n\t}\n\n\toverride async count(sql: SQL): Promise<number> {\n\t\tconst res = await this.execute<{ rows: [{ count: string }] }>(sql);\n\n\t\treturn Number(\n\t\t\tres['rows'][0]['count'],\n\t\t);\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: TiDBServerlessTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\tconst nativeTx = await this.baseClient.begin();\n\t\ttry {\n\t\t\tconst session = new TiDBServerlessSession(this.baseClient, this.dialect, nativeTx, this.schema, this.options);\n\t\t\tconst tx = new TiDBServerlessTransaction<TFullSchema, TSchema>(\n\t\t\t\tthis.dialect,\n\t\t\t\tsession as MySqlSession<any, any, any, any>,\n\t\t\t\tthis.schema,\n\t\t\t);\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait nativeTx.commit();\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait nativeTx.rollback();\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport class TiDBServerlessTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends MySqlTransaction<TiDBServerlessQueryResultHKT, TiDBServerlessPreparedQueryHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'TiDBServerlessTransaction';\n\n\tconstructor(\n\t\tdialect: MySqlDialect,\n\t\tsession: MySqlSession,\n\t\tschema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tnestedIndex = 0,\n\t) {\n\t\tsuper(dialect, session, schema, nestedIndex, 'default');\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: TiDBServerlessTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\tconst savepointName = `sp${this.nestedIndex + 1}`;\n\t\tconst tx = new TiDBServerlessTransaction<TFullSchema, TSchema>(\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tthis.schema,\n\t\t\tthis.nestedIndex + 1,\n\t\t);\n\t\tawait tx.execute(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait tx.execute(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait tx.execute(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport interface TiDBServerlessQueryResultHKT extends MySqlQueryResultHKT {\n\ttype: FullResult;\n}\n\nexport interface TiDBServerlessPreparedQueryHKT extends MySqlPreparedQueryHKT {\n\ttype: TiDBServerlessPreparedQuery<Assume<this['config'], MySqlPreparedQueryConfig>>;\n}\n"], "mappings": "AACA,SAAS,cAAc;AAEvB,SAAS,YAAY,UAAU;AAE/B,SAAS,kBAAkB;AAG3B;AAAA,EACC;AAAA,EAIA;AAAA,EACA;AAAA,OACM;AAEP,SAAS,kBAAwC,WAAW;AAC5D,SAAsB,oBAAoB;AAE1C,MAAM,mBAAmB,EAAE,YAAY,KAAK;AAC5C,MAAM,cAAc,EAAE,WAAW,KAAK;AAE/B,MAAM,oCAAwE,mBAAsB;AAAA,EAG1G,YACS,QACA,aACA,QACA,QACA,QACA,oBAEA,cAEA,cACP;AACD,UAAM;AAXE;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAAA,EAGT;AAAA,EAfA,QAA0B,UAAU,IAAY;AAAA,EAiBhD,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,UAAM,SAAS,iBAAiB,KAAK,QAAQ,iBAAiB;AAE9D,SAAK,OAAO,SAAS,KAAK,aAAa,MAAM;AAE7C,UAAM,EAAE,QAAQ,QAAQ,aAAa,qBAAqB,oBAAoB,cAAc,aAAa,IAAI;AAC7G,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,MAAM,MAAM,OAAO,QAAQ,aAAa,QAAQ,gBAAgB;AACtE,YAAM,WAAW,IAAI,gBAAgB;AACrC,YAAM,eAAe,IAAI,gBAAgB;AAEzC,UAAI,cAAc;AACjB,cAAM,oBAAoB,CAAC;AAC3B,YAAI,IAAI;AACR,iBAAS,IAAI,UAAU,IAAI,WAAW,cAAc,KAAK;AACxD,qBAAW,UAAU,cAAc;AAClC,kBAAM,MAAM,aAAa,CAAC,EAAG,KAAK,CAAC;AACnC,gBAAI,GAAG,OAAO,OAAO,MAAM,GAAG;AAE7B,kBAAI,OAAO,MAAM,WAAW,OAAO,MAAM,eAAe;AACvD,kCAAkB,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC;AAAA,cACpC;AACA,kBAAI,OAAO,MAAM,aAAa,cAAc;AAE3C,kCAAkB,KAAK,EAAE,CAAC,GAAG,GAAG,aAAa,CAAC,EAAG,GAAG,EAAE,CAAC;AAAA,cACxD;AAAA,YACD;AAAA,UACD;AACA;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AACA,aAAO;AAAA,IACR;AAEA,UAAM,OAAO,MAAM,OAAO,QAAQ,aAAa,QAAQ,WAAW;AAElE,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAI;AAAA,IAC/B;AAEA,WAAO,KAAK,IAAI,CAAC,QAAQ,aAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,EACvF;AAAA,EAES,SAAS,oBAA6E;AAC9F,UAAM,IAAI,MAAM,gEAAgE;AAAA,EACjF;AACD;AAMO,MAAM,8BAGH,aAAiG;AAAA,EAM1G,YACS,YACR,SACA,IACQ,QACA,UAAwC,CAAC,GAChD;AACD,UAAM,OAAO;AANL;AAGA;AACA;AAGR,SAAK,SAAS,MAAM;AACpB,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;AAAA,EAChD;AAAA,EAfA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EAcR,aACC,OACA,QACA,oBACA,cACA,cACwB;AACxB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAES,IAAiB,OAA0B;AACnD,UAAM,WAAW,KAAK,QAAQ,WAAW,KAAK;AAC9C,SAAK,OAAO,SAAS,SAAS,KAAK,SAAS,MAAM;AAClD,WAAO,KAAK,OAAO,QAAQ,SAAS,KAAK,SAAS,MAAM;AAAA,EACzD;AAAA,EAEA,MAAe,MAAMA,MAA2B;AAC/C,UAAM,MAAM,MAAM,KAAK,QAAuCA,IAAG;AAEjE,WAAO;AAAA,MACN,IAAI,MAAM,EAAE,CAAC,EAAE,OAAO;AAAA,IACvB;AAAA,EACD;AAAA,EAEA,MAAe,YACd,aACa;AACb,UAAM,WAAW,MAAM,KAAK,WAAW,MAAM;AAC7C,QAAI;AACH,YAAM,UAAU,IAAI,sBAAsB,KAAK,YAAY,KAAK,SAAS,UAAU,KAAK,QAAQ,KAAK,OAAO;AAC5G,YAAM,KAAK,IAAI;AAAA,QACd,KAAK;AAAA,QACL;AAAA,QACA,KAAK;AAAA,MACN;AACA,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,SAAS,OAAO;AACtB,aAAO;AAAA,IACR,SAAS,KAAK;AACb,YAAM,SAAS,SAAS;AACxB,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEO,MAAM,kCAGH,iBAAqG;AAAA,EAC9G,QAA0B,UAAU,IAAY;AAAA,EAEhD,YACC,SACA,SACA,QACA,cAAc,GACb;AACD,UAAM,SAAS,SAAS,QAAQ,aAAa,SAAS;AAAA,EACvD;AAAA,EAEA,MAAe,YACd,aACa;AACb,UAAM,gBAAgB,KAAK,KAAK,cAAc,CAAC;AAC/C,UAAM,KAAK,IAAI;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,cAAc;AAAA,IACpB;AACA,UAAM,GAAG,QAAQ,IAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AACtD,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,GAAG,QAAQ,IAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AAC9D,aAAO;AAAA,IACR,SAAS,KAAK;AACb,YAAM,GAAG,QAAQ,IAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AAClE,YAAM;AAAA,IACP;AAAA,EACD;AACD;", "names": ["sql"]}