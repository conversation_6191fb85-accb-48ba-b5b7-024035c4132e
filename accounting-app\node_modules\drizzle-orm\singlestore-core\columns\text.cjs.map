{"version": 3, "sources": ["../../../src/singlestore-core/columns/text.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { getColumnNameAndConfig, type Writable } from '~/utils.ts';\nimport { SingleStoreColumn, SingleStoreColumnBuilder } from './common.ts';\n\nexport type SingleStoreTextColumnType = 'tinytext' | 'text' | 'mediumtext' | 'longtext';\n\nexport type SingleStoreTextBuilderInitial<TName extends string, TEnum extends [string, ...string[]]> =\n\tSingleStoreTextBuilder<{\n\t\tname: TName;\n\t\tdataType: 'string';\n\t\tcolumnType: 'SingleStoreText';\n\t\tdata: TEnum[number];\n\t\tdriverParam: string;\n\t\tenumValues: TEnum;\n\t\tgenerated: undefined;\n\t}>;\n\nexport class SingleStoreTextBuilder<T extends ColumnBuilderBaseConfig<'string', 'SingleStoreText'>>\n\textends SingleStoreColumnBuilder<\n\t\tT,\n\t\t{ textType: SingleStoreTextColumnType; enumValues: T['enumValues'] }\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreTextBuilder';\n\n\tconstructor(name: T['name'], textType: SingleStoreTextColumnType, config: SingleStoreTextConfig<T['enumValues']>) {\n\t\tsuper(name, 'string', 'SingleStoreText');\n\t\tthis.config.textType = textType;\n\t\tthis.config.enumValues = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreText<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreText<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreText<T extends ColumnBaseConfig<'string', 'SingleStoreText'>>\n\textends SingleStoreColumn<T, { textType: SingleStoreTextColumnType; enumValues: T['enumValues'] }>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreText';\n\n\treadonly textType: SingleStoreTextColumnType = this.config.textType;\n\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn this.textType;\n\t}\n}\n\nexport interface SingleStoreTextConfig<\n\tTEnum extends readonly string[] | string[] | undefined = readonly string[] | string[] | undefined,\n> {\n\tenum?: TEnum;\n}\n\nexport function text(): SingleStoreTextBuilderInitial<'', [string, ...string[]]>;\nexport function text<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tconfig?: SingleStoreTextConfig<T | Writable<T>>,\n): SingleStoreTextBuilderInitial<'', Writable<T>>;\nexport function text<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig?: SingleStoreTextConfig<T | Writable<T>>,\n): SingleStoreTextBuilderInitial<TName, Writable<T>>;\nexport function text(a?: string | SingleStoreTextConfig, b: SingleStoreTextConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreTextConfig>(a, b);\n\treturn new SingleStoreTextBuilder(name, 'text', config as any);\n}\n\nexport function tinytext(): SingleStoreTextBuilderInitial<'', [string, ...string[]]>;\nexport function tinytext<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tconfig?: SingleStoreTextConfig<T | Writable<T>>,\n): SingleStoreTextBuilderInitial<'', Writable<T>>;\nexport function tinytext<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig?: SingleStoreTextConfig<T | Writable<T>>,\n): SingleStoreTextBuilderInitial<TName, Writable<T>>;\nexport function tinytext(a?: string | SingleStoreTextConfig, b: SingleStoreTextConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreTextConfig>(a, b);\n\treturn new SingleStoreTextBuilder(name, 'tinytext', config as any);\n}\n\nexport function mediumtext(): SingleStoreTextBuilderInitial<'', [string, ...string[]]>;\nexport function mediumtext<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tconfig?: SingleStoreTextConfig<T | Writable<T>>,\n): SingleStoreTextBuilderInitial<'', Writable<T>>;\nexport function mediumtext<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig?: SingleStoreTextConfig<T | Writable<T>>,\n): SingleStoreTextBuilderInitial<TName, Writable<T>>;\nexport function mediumtext(a?: string | SingleStoreTextConfig, b: SingleStoreTextConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreTextConfig>(a, b);\n\treturn new SingleStoreTextBuilder(name, 'mediumtext', config as any);\n}\n\nexport function longtext(): SingleStoreTextBuilderInitial<'', [string, ...string[]]>;\nexport function longtext<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tconfig?: SingleStoreTextConfig<T | Writable<T>>,\n): SingleStoreTextBuilderInitial<'', Writable<T>>;\nexport function longtext<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig?: SingleStoreTextConfig<T | Writable<T>>,\n): SingleStoreTextBuilderInitial<TName, Writable<T>>;\nexport function longtext(a?: string | SingleStoreTextConfig, b: SingleStoreTextConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreTextConfig>(a, b);\n\treturn new SingleStoreTextBuilder(name, 'longtext', config as any);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAsD;AACtD,oBAA4D;AAerD,MAAM,+BACJ,uCAIT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,UAAqC,QAAgD;AACjH,UAAM,MAAM,UAAU,iBAAiB;AACvC,SAAK,OAAO,WAAW;AACvB,SAAK,OAAO,aAAa,OAAO;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OACmD;AACnD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,wBACJ,gCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,WAAsC,KAAK,OAAO;AAAA,EAEzC,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,KAAK;AAAA,EACb;AACD;AAgBO,SAAS,KAAK,GAAoC,IAA2B,CAAC,GAAQ;AAC5F,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA8C,GAAG,CAAC;AAC3E,SAAO,IAAI,uBAAuB,MAAM,QAAQ,MAAa;AAC9D;AAUO,SAAS,SAAS,GAAoC,IAA2B,CAAC,GAAQ;AAChG,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA8C,GAAG,CAAC;AAC3E,SAAO,IAAI,uBAAuB,MAAM,YAAY,MAAa;AAClE;AAUO,SAAS,WAAW,GAAoC,IAA2B,CAAC,GAAQ;AAClG,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA8C,GAAG,CAAC;AAC3E,SAAO,IAAI,uBAAuB,MAAM,cAAc,MAAa;AACpE;AAUO,SAAS,SAAS,GAAoC,IAA2B,CAAC,GAAQ;AAChG,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA8C,GAAG,CAAC;AAC3E,SAAO,IAAI,uBAAuB,MAAM,YAAY,MAAa;AAClE;", "names": []}