/**
 * Application Constants
 * 
 * This file contains all constant values used throughout the application.
 * Centralizing constants here makes the application easier to maintain and configure.
 * It also provides a single source of truth for configuration values.
 */

// ============================================================================
// APPLICATION CONFIGURATION
// ============================================================================

/**
 * Application metadata and branding constants
 */
export const APP_CONFIG = {
  /** Application name displayed in UI */
  NAME: 'AccountingApp',
  /** Application version */
  VERSION: '1.0.0',
  /** Application description */
  DESCRIPTION: 'Professional Accounting Management System',
  /** Company/Organization name */
  COMPANY: 'Your Company Name',
  /** Support email address */
  SUPPORT_EMAIL: '<EMAIL>',
} as const;

/**
 * API configuration constants
 */
export const API_CONFIG = {
  /** Base URL for API requests */
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || '/api',
  /** Request timeout in milliseconds */
  TIMEOUT: 30000,
  /** Maximum retry attempts for failed requests */
  MAX_RETRIES: 3,
} as const;

// ============================================================================
// UI/UX CONSTANTS
// ============================================================================

/**
 * Layout dimensions and breakpoints
 */
export const LAYOUT = {
  /** Sidebar width when expanded (in pixels) */
  SIDEBAR_WIDTH: 280,
  /** Sidebar width when collapsed (in pixels) */
  SIDEBAR_COLLAPSED_WIDTH: 64,
  /** Header height (in pixels) */
  HEADER_HEIGHT: 64,
  /** Footer height (in pixels) */
  FOOTER_HEIGHT: 48,
  /** Default page padding (in pixels) */
  PAGE_PADDING: 24,
} as const;

/**
 * Color palette following AdminLTE design principles
 */
export const COLORS = {
  /** Primary brand color (blue) */
  PRIMARY: '#3498db',
  /** Primary color hover state */
  PRIMARY_HOVER: '#2980b9',
  /** Primary color dark variant */
  PRIMARY_DARK: '#1f5f8b',
  
  /** Secondary accent color (red) */
  SECONDARY: '#dc004e',
  
  /** Success color (green) */
  SUCCESS: '#2ecc71',
  /** Success color hover state */
  SUCCESS_HOVER: '#27ae60',
  
  /** Warning color (orange) */
  WARNING: '#f39c12',
  /** Warning color hover state */
  WARNING_HOVER: '#e67e22',
  
  /** Error/Danger color (red) */
  ERROR: '#e74c3c',
  /** Error color hover state */
  ERROR_HOVER: '#c0392b',
  
  /** Info color (light blue) */
  INFO: '#3498db',
  
  /** Sidebar background color (dark blue-gray) */
  SIDEBAR_BG: '#2c3e50',
  /** Sidebar text color */
  SIDEBAR_TEXT: '#ecf0f1',
  /** Sidebar hover color */
  SIDEBAR_HOVER: '#34495e',
  
  /** Main content background */
  CONTENT_BG: '#f5f5f5',
  /** Card/Paper background */
  CARD_BG: '#ffffff',
  
  /** Text colors */
  TEXT_PRIMARY: '#2c3e50',
  TEXT_SECONDARY: '#7f8c8d',
  TEXT_MUTED: '#bdc3c7',
  
  /** Border colors */
  BORDER_LIGHT: '#e0e0e0',
  BORDER_MEDIUM: '#bdc3c7',
  BORDER_DARK: '#95a5a6',
} as const;

/**
 * Animation and transition constants
 */
export const ANIMATIONS = {
  /** Standard transition duration (in milliseconds) */
  DURATION_STANDARD: 300,
  /** Fast transition duration (in milliseconds) */
  DURATION_FAST: 150,
  /** Slow transition duration (in milliseconds) */
  DURATION_SLOW: 500,
  
  /** Standard easing function */
  EASING_STANDARD: 'cubic-bezier(0.4, 0, 0.2, 1)',
  /** Ease-in function */
  EASING_EASE_IN: 'cubic-bezier(0.4, 0, 1, 1)',
  /** Ease-out function */
  EASING_EASE_OUT: 'cubic-bezier(0, 0, 0.2, 1)',
} as const;

// ============================================================================
// BUSINESS LOGIC CONSTANTS
// ============================================================================

/**
 * Account categories following standard accounting principles
 */
export const ACCOUNT_CATEGORIES = {
  ASSET: 'ASSET',
  LIABILITY: 'LIABILITY',
  EQUITY: 'EQUITY',
  REVENUE: 'REVENUE',
  EXPENSE: 'EXPENSE',
} as const;

/**
 * Journal entry status options
 */
export const JOURNAL_ENTRY_STATUS = {
  /** Entry is being created/edited */
  DRAFT: 'DRAFT',
  /** Entry has been finalized and posted to accounts */
  POSTED: 'POSTED',
  /** Entry has been reversed/cancelled */
  REVERSED: 'REVERSED',
} as const;

/**
 * Default account types for new companies
 * These will be automatically created when a new company is set up
 */
export const DEFAULT_ACCOUNT_TYPES = [
  {
    name: 'Current Assets',
    category: ACCOUNT_CATEGORIES.ASSET,
    description: 'Assets that can be converted to cash within one year',
  },
  {
    name: 'Fixed Assets',
    category: ACCOUNT_CATEGORIES.ASSET,
    description: 'Long-term assets used in business operations',
  },
  {
    name: 'Current Liabilities',
    category: ACCOUNT_CATEGORIES.LIABILITY,
    description: 'Debts due within one year',
  },
  {
    name: 'Long-term Liabilities',
    category: ACCOUNT_CATEGORIES.LIABILITY,
    description: 'Debts due after one year',
  },
  {
    name: 'Owner\'s Equity',
    category: ACCOUNT_CATEGORIES.EQUITY,
    description: 'Owner\'s stake in the business',
  },
  {
    name: 'Revenue',
    category: ACCOUNT_CATEGORIES.REVENUE,
    description: 'Income from business operations',
  },
  {
    name: 'Operating Expenses',
    category: ACCOUNT_CATEGORIES.EXPENSE,
    description: 'Costs of running the business',
  },
  {
    name: 'Other Income',
    category: ACCOUNT_CATEGORIES.REVENUE,
    description: 'Non-operating income',
  },
  {
    name: 'Other Expenses',
    category: ACCOUNT_CATEGORIES.EXPENSE,
    description: 'Non-operating expenses',
  },
] as const;

// ============================================================================
// VALIDATION CONSTANTS
// ============================================================================

/**
 * Input validation rules and limits
 */
export const VALIDATION = {
  /** Minimum password length */
  PASSWORD_MIN_LENGTH: 6,
  /** Maximum password length */
  PASSWORD_MAX_LENGTH: 128,
  
  /** Maximum length for user names */
  USER_NAME_MAX_LENGTH: 100,
  
  /** Maximum length for company names */
  COMPANY_NAME_MAX_LENGTH: 200,
  /** Maximum length for company descriptions */
  COMPANY_DESCRIPTION_MAX_LENGTH: 1000,
  
  /** Maximum length for account names */
  ACCOUNT_NAME_MAX_LENGTH: 100,
  /** Maximum length for account codes */
  ACCOUNT_CODE_MAX_LENGTH: 20,
  
  /** Maximum length for journal entry descriptions */
  JOURNAL_DESCRIPTION_MAX_LENGTH: 500,
  /** Maximum length for journal entry references */
  JOURNAL_REFERENCE_MAX_LENGTH: 100,
  
  /** Maximum decimal places for monetary amounts */
  CURRENCY_DECIMAL_PLACES: 2,
  /** Maximum monetary amount */
  MAX_AMOUNT: *********.99,
} as const;

/**
 * Regular expressions for validation
 */
export const REGEX = {
  /** Email validation pattern */
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  /** Account code pattern (alphanumeric with optional dashes/dots) */
  ACCOUNT_CODE: /^[A-Za-z0-9.-]+$/,
  /** Phone number pattern (basic international format) */
  PHONE: /^\+?[\d\s-()]+$/,
} as const;

// ============================================================================
// PAGINATION AND LIMITS
// ============================================================================

/**
 * Pagination and data loading constants
 */
export const PAGINATION = {
  /** Default number of items per page */
  DEFAULT_PAGE_SIZE: 20,
  /** Available page size options */
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  /** Maximum number of items per page */
  MAX_PAGE_SIZE: 100,
} as const;

// ============================================================================
// DATE AND TIME CONSTANTS
// ============================================================================

/**
 * Date and time formatting constants
 */
export const DATE_FORMATS = {
  /** Standard date format for display (e.g., "Jan 15, 2024") */
  DISPLAY: 'MMM dd, yyyy',
  /** Short date format (e.g., "01/15/2024") */
  SHORT: 'MM/dd/yyyy',
  /** ISO date format for API (e.g., "2024-01-15") */
  ISO: 'yyyy-MM-dd',
  /** Date and time format (e.g., "Jan 15, 2024 2:30 PM") */
  DATETIME: 'MMM dd, yyyy h:mm a',
  /** Time only format (e.g., "2:30 PM") */
  TIME: 'h:mm a',
} as const;

/**
 * Common financial year patterns
 */
export const FINANCIAL_YEAR_PATTERNS = {
  /** April to March (common in India) */
  APRIL_MARCH: { start: '04-01', end: '03-31' },
  /** January to December (calendar year) */
  JANUARY_DECEMBER: { start: '01-01', end: '12-31' },
  /** July to June (common in Australia) */
  JULY_JUNE: { start: '07-01', end: '06-30' },
} as const;

// ============================================================================
// ERROR MESSAGES
// ============================================================================

/**
 * Standard error messages used throughout the application
 */
export const ERROR_MESSAGES = {
  /** Generic error message */
  GENERIC: 'An unexpected error occurred. Please try again.',
  /** Network/connection error */
  NETWORK: 'Network error. Please check your connection and try again.',
  /** Authentication required */
  AUTH_REQUIRED: 'Please log in to continue.',
  /** Insufficient permissions */
  PERMISSION_DENIED: 'You do not have permission to perform this action.',
  /** Resource not found */
  NOT_FOUND: 'The requested resource was not found.',
  /** Validation failed */
  VALIDATION_FAILED: 'Please check your input and try again.',
  /** Server error */
  SERVER_ERROR: 'Server error. Please try again later.',
} as const;

// ============================================================================
// SUCCESS MESSAGES
// ============================================================================

/**
 * Standard success messages used throughout the application
 */
export const SUCCESS_MESSAGES = {
  /** Generic success message */
  GENERIC: 'Operation completed successfully.',
  /** User authentication */
  LOGIN_SUCCESS: 'Welcome back! You have been logged in successfully.',
  LOGOUT_SUCCESS: 'You have been logged out successfully.',
  REGISTER_SUCCESS: 'Account created successfully. Welcome to AccountingApp!',
  
  /** Company operations */
  COMPANY_CREATED: 'Company created successfully.',
  COMPANY_UPDATED: 'Company updated successfully.',
  COMPANY_DELETED: 'Company deleted successfully.',
  
  /** Financial year operations */
  FINANCIAL_YEAR_CREATED: 'Financial year created successfully.',
  FINANCIAL_YEAR_UPDATED: 'Financial year updated successfully.',
  FINANCIAL_YEAR_DELETED: 'Financial year deleted successfully.',
  
  /** Account operations */
  ACCOUNT_CREATED: 'Account created successfully.',
  ACCOUNT_UPDATED: 'Account updated successfully.',
  ACCOUNT_DELETED: 'Account deleted successfully.',
  
  /** Journal entry operations */
  JOURNAL_ENTRY_CREATED: 'Journal entry created successfully.',
  JOURNAL_ENTRY_UPDATED: 'Journal entry updated successfully.',
  JOURNAL_ENTRY_POSTED: 'Journal entry posted successfully.',
  JOURNAL_ENTRY_REVERSED: 'Journal entry reversed successfully.',
} as const;
