/**
 * Admin Layout Component
 *
 * Beautiful AdminLTE-style layout with MUI Pro components including
 * sidebar navigation, header with user menu, and responsive design.
 *
 * Following DRY principle: Single layout component
 * Following YAGNI principle: Only essential layout features
 */

'use client';

import React, { useState } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  Divider,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Badge,
  useTheme,
  useMediaQuery,
  alpha,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard,
  Business,
  AccountBalance,
  Receipt,
  Assessment,
  Settings,
  Logout,
  Person,
  Notifications,
  ExpandLess,
  ExpandMore,
  CalendarToday,
  AccountTree,
} from '@mui/icons-material';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

/**
 * Sidebar width constants
 */
const DRAWER_WIDTH = 280;
const DRAWER_WIDTH_COLLAPSED = 64;

/**
 * Navigation item interface
 */
interface NavItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path?: string;
  children?: NavItem[];
  badge?: number;
}

/**
 * Navigation configuration
 */
const navigationItems: NavItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: <Dashboard />,
    path: '/dashboard',
  },
  {
    id: 'companies',
    label: 'Companies',
    icon: <Business />,
    children: [
      {
        id: 'companies-list',
        label: 'All Companies',
        icon: <Business />,
        path: '/companies',
      },
      {
        id: 'companies-create',
        label: 'Create Company',
        icon: <Business />,
        path: '/companies/create',
      },
    ],
  },
  {
    id: 'accounting',
    label: 'Accounting',
    icon: <AccountBalance />,
    children: [
      {
        id: 'financial-years',
        label: 'Financial Years',
        icon: <CalendarToday />,
        path: '/financial-years',
      },
      {
        id: 'chart-of-accounts',
        label: 'Chart of Accounts',
        icon: <AccountTree />,
        path: '/accounts',
      },
      {
        id: 'journal-entries',
        label: 'Journal Entries',
        icon: <Receipt />,
        path: '/journal-entries',
        badge: 3,
      },
    ],
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: <Assessment />,
    children: [
      {
        id: 'balance-sheet',
        label: 'Balance Sheet',
        icon: <Assessment />,
        path: '/reports/balance-sheet',
      },
      {
        id: 'profit-loss',
        label: 'Profit & Loss',
        icon: <Assessment />,
        path: '/reports/profit-loss',
      },
    ],
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: <Settings />,
    path: '/settings',
  },
];

/**
 * Admin layout props interface
 */
interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
}

/**
 * AdminLTE-style layout component
 */
export default function AdminLayout({ children, title = 'Dashboard' }: AdminLayoutProps) {
  const theme = useTheme();
  const router = useRouter();
  const { user, signOut } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State management
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(null);
  const [expandedItems, setExpandedItems] = useState<string[]>(['accounting']);

  /**
   * Handle sidebar toggle
   */
  const handleSidebarToggle = () => {
    if (isMobile) {
      setSidebarOpen(!sidebarOpen);
    } else {
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  /**
   * Handle user menu open
   */
  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchor(event.currentTarget);
  };

  /**
   * Handle user menu close
   */
  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  /**
   * Handle navigation item click
   */
  const handleNavItemClick = (item: NavItem) => {
    if (item.children) {
      // Toggle expansion for items with children
      setExpandedItems(prev =>
        prev.includes(item.id)
          ? prev.filter(id => id !== item.id)
          : [...prev, item.id]
      );
    } else if (item.path) {
      // Navigate to path
      router.push(item.path);

      // Close sidebar on mobile
      if (isMobile) {
        setSidebarOpen(false);
      }
    }
  };

  /**
   * Handle logout
   */
  const handleLogout = async () => {
    handleUserMenuClose();
    await signOut();
    router.push('/auth/login');
  };

  /**
   * Render navigation item
   */
  const renderNavItem = (item: NavItem, level = 0) => {
    const isExpanded = expandedItems.includes(item.id);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <React.Fragment key={item.id}>
        <ListItem disablePadding sx={{ display: 'block' }}>
          <ListItemButton
            onClick={() => handleNavItemClick(item)}
            sx={{
              minHeight: 48,
              justifyContent: sidebarCollapsed ? 'center' : 'initial',
              px: 2.5,
              pl: level > 0 ? 4 : 2.5,
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.1),
              },
            }}
          >
            <ListItemIcon
              sx={{
                minWidth: 0,
                mr: sidebarCollapsed ? 0 : 3,
                justifyContent: 'center',
                color: theme.palette.primary.main,
              }}
            >
              {item.badge ? (
                <Badge badgeContent={item.badge} color="error">
                  {item.icon}
                </Badge>
              ) : (
                item.icon
              )}
            </ListItemIcon>

            {!sidebarCollapsed && (
              <>
                <ListItemText
                  primary={item.label}
                  sx={{
                    opacity: 1,
                    '& .MuiListItemText-primary': {
                      fontSize: '0.875rem',
                      fontWeight: 500,
                    },
                  }}
                />
                {hasChildren && (
                  <IconButton size="small">
                    {isExpanded ? <ExpandLess /> : <ExpandMore />}
                  </IconButton>
                )}
              </>
            )}
          </ListItemButton>
        </ListItem>

        {/* Render children */}
        {hasChildren && !sidebarCollapsed && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children!.map(child => renderNavItem(child, level + 1))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  /**
   * Sidebar content
   */
  const sidebarContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo/Brand */}
      <Box
        sx={{
          p: 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: sidebarCollapsed ? 'center' : 'flex-start',
          minHeight: 64,
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <AccountBalance
          sx={{
            fontSize: 32,
            color: theme.palette.primary.main,
            mr: sidebarCollapsed ? 0 : 2,
          }}
        />
        {!sidebarCollapsed && (
          <Typography variant="h6" fontWeight="bold" color="primary">
            Accounting Pro
          </Typography>
        )}
      </Box>

      {/* Navigation */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <List sx={{ pt: 1 }}>
          {navigationItems.map(item => renderNavItem(item))}
        </List>
      </Box>

      {/* User info at bottom */}
      {!sidebarCollapsed && user && (
        <Box
          sx={{
            p: 2,
            borderTop: `1px solid ${theme.palette.divider}`,
            backgroundColor: alpha(theme.palette.primary.main, 0.05),
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              sx={{
                width: 32,
                height: 32,
                mr: 2,
                backgroundColor: theme.palette.primary.main,
              }}
            >
              {user.name.charAt(0).toUpperCase()}
            </Avatar>
            <Box sx={{ flexGrow: 1, minWidth: 0 }}>
              <Typography variant="body2" fontWeight="bold" noWrap>
                {user.name}
              </Typography>
              <Typography variant="caption" color="text.secondary" noWrap>
                {user.email}
              </Typography>
            </Box>
          </Box>
        </Box>
      )}
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        sx={{
          width: {
            md: sidebarCollapsed
              ? `calc(100% - ${DRAWER_WIDTH_COLLAPSED}px)`
              : `calc(100% - ${DRAWER_WIDTH}px)`,
          },
          ml: {
            md: sidebarCollapsed ? `${DRAWER_WIDTH_COLLAPSED}px` : `${DRAWER_WIDTH}px`,
          },
          backgroundColor: theme.palette.background.paper,
          color: theme.palette.text.primary,
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="toggle sidebar"
            edge="start"
            onClick={handleSidebarToggle}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {title}
          </Typography>

          {/* Notifications */}
          <IconButton color="inherit" sx={{ mr: 1 }}>
            <Badge badgeContent={4} color="error">
              <Notifications />
            </Badge>
          </IconButton>

          {/* User Menu */}
          <IconButton
            color="inherit"
            onClick={handleUserMenuOpen}
            sx={{ ml: 1 }}
          >
            <Avatar
              sx={{
                width: 32,
                height: 32,
                backgroundColor: theme.palette.primary.main,
              }}
            >
              {user?.name.charAt(0).toUpperCase()}
            </Avatar>
          </IconButton>

          <Menu
            anchorEl={userMenuAnchor}
            open={Boolean(userMenuAnchor)}
            onClose={handleUserMenuClose}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          >
            <MenuItem onClick={handleUserMenuClose}>
              <ListItemIcon>
                <Person fontSize="small" />
              </ListItemIcon>
              Profile
            </MenuItem>
            <MenuItem onClick={handleUserMenuClose}>
              <ListItemIcon>
                <Settings fontSize="small" />
              </ListItemIcon>
              Settings
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleLogout}>
              <ListItemIcon>
                <Logout fontSize="small" />
              </ListItemIcon>
              Logout
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Box
        component="nav"
        sx={{
          width: { md: sidebarCollapsed ? DRAWER_WIDTH_COLLAPSED : DRAWER_WIDTH },
          flexShrink: { md: 0 },
        }}
      >
        <Drawer
          variant={isMobile ? 'temporary' : 'permanent'}
          open={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile
          }}
          sx={{
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: sidebarCollapsed ? DRAWER_WIDTH_COLLAPSED : DRAWER_WIDTH,
              borderRight: `1px solid ${theme.palette.divider}`,
              transition: theme.transitions.create('width', {
                easing: theme.transitions.easing.sharp,
                duration: theme.transitions.duration.enteringScreen,
              }),
            },
          }}
        >
          {sidebarContent}
        </Drawer>
      </Box>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          mt: 8, // Account for AppBar height
          backgroundColor: theme.palette.grey[50],
          minHeight: 'calc(100vh - 64px)',
        }}
      >
        {children}
      </Box>
    </Box>
  );
}
