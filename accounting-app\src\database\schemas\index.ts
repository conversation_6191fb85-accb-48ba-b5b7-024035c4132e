/**
 * Database Schemas Index
 * 
 * This file exports all database schemas and types for easy importing.
 * It provides a centralized access point for all schema definitions.
 * 
 * Following DRY principle: Single export point for all schemas
 * Following YAGNI principle: Only essential exports
 */

// Export all table schemas
export * from './users';
export * from './companies';
export * from './financialYears';
export * from './accountTypes';
export * from './accounts';
export * from './journalEntries';

// Re-export specific tables for convenience
export { users } from './users';
export { companies } from './companies';
export { financialYears } from './financialYears';
export { accountTypes } from './accountTypes';
export { accounts } from './accounts';
export { journalEntries, journalEntryLines } from './journalEntries';

// Export all type definitions
export type {
  // User types
  InsertUser,
  SelectUser,
  PublicUser,
  
  // Company types
  InsertCompany,
  SelectCompany,
  UpdateCompany,
  CompanyWithUser,
  CompanyStats,
  
  // Financial Year types
  InsertFinancialYear,
  SelectFinancialYear,
  UpdateFinancialYear,
  FinancialYearWithCompany,
  FinancialYearStats,
  CreateFinancialYearInput,
  UpdateFinancialYearInput,
  
  // Account Type types
  InsertAccountType,
  SelectAccountType,
  UpdateAccountType,
  AccountTypesByCategory,
  AccountTypeStats,
  CreateAccountTypeInput,
  AccountTypeFilters,
  
  // Account types
  InsertAccount,
  SelectAccount,
  UpdateAccount,
  AccountWithType,
  AccountWithParent,
  AccountWithChildren,
  AccountWithRelations,
  AccountBalance,
  AccountStats,
  CreateAccountInput,
  UpdateAccountInput,
  AccountFilters,
  AccountHierarchyNode,
  ChartOfAccounts,
  
  // Journal Entry types
  InsertJournalEntry,
  SelectJournalEntry,
  UpdateJournalEntry,
  InsertJournalEntryLine,
  SelectJournalEntryLine,
  UpdateJournalEntryLine,
  JournalEntryWithLines,
  JournalEntryWithRelations,
  JournalEntryStats,
  CreateJournalEntryInput,
  CreateJournalEntryLineInput,
  UpdateJournalEntryInput,
  UpdateJournalEntryLineInput,
  JournalEntryFilters,
  JournalEntryBalance,
} from './users';

export type {
  InsertCompany,
  SelectCompany,
  UpdateCompany,
  CompanyWithUser,
  CompanyStats,
} from './companies';

export type {
  InsertFinancialYear,
  SelectFinancialYear,
  UpdateFinancialYear,
  FinancialYearWithCompany,
  FinancialYearStats,
  CreateFinancialYearInput,
  UpdateFinancialYearInput,
} from './financialYears';

export type {
  InsertAccountType,
  SelectAccountType,
  UpdateAccountType,
  AccountCategoryType,
  AccountTypesByCategory,
  AccountTypeStats,
  CreateAccountTypeInput,
  AccountTypeFilters,
} from './accountTypes';

export type {
  InsertAccount,
  SelectAccount,
  UpdateAccount,
  AccountWithType,
  AccountWithParent,
  AccountWithChildren,
  AccountWithRelations,
  AccountBalance,
  AccountStats,
  CreateAccountInput,
  UpdateAccountInput,
  AccountFilters,
  AccountHierarchyNode,
  ChartOfAccounts,
} from './accounts';

export type {
  InsertJournalEntry,
  SelectJournalEntry,
  UpdateJournalEntry,
  InsertJournalEntryLine,
  SelectJournalEntryLine,
  UpdateJournalEntryLine,
  JournalEntryStatusType,
  JournalEntryWithLines,
  JournalEntryWithRelations,
  JournalEntryStats,
  CreateJournalEntryInput,
  CreateJournalEntryLineInput,
  UpdateJournalEntryInput,
  UpdateJournalEntryLineInput,
  JournalEntryFilters,
  JournalEntryBalance,
} from './journalEntries';

// Export enums
export { AccountCategory } from './accountTypes';
export { JournalEntryStatus } from './journalEntries';
export { FinancialYearPatterns } from './financialYears';

// Export constraints
export { UserConstraints } from './users';
export { CompanyConstraints } from './companies';
export { FinancialYearConstraints } from './financialYears';
export { AccountTypeConstraints } from './accountTypes';
export { AccountConstraints } from './accounts';
export { JournalEntryConstraints } from './journalEntries';

// Export default data
export { defaultAccountTypes } from './accountTypes';

/**
 * All database tables in a single object for convenience
 * Following DRY principle: Single reference for all tables
 */
export const dbSchema = {
  users,
  companies,
  financialYears,
  accountTypes,
  accounts,
  journalEntries,
  journalEntryLines,
} as const;

/**
 * Database schema metadata
 * Following proper ORM patterns: Schema information
 */
export const schemaInfo = {
  version: '1.0.0',
  tables: [
    'users',
    'companies',
    'financial_years',
    'account_types',
    'accounts',
    'journal_entries',
    'journal_entry_lines',
  ],
  relationships: {
    'users -> companies': 'one-to-many',
    'companies -> financial_years': 'one-to-many',
    'companies -> accounts': 'one-to-many',
    'financial_years -> accounts': 'one-to-many',
    'account_types -> accounts': 'one-to-many',
    'accounts -> accounts': 'self-referencing (parent-child)',
    'companies -> journal_entries': 'one-to-many',
    'financial_years -> journal_entries': 'one-to-many',
    'users -> journal_entries': 'one-to-many (created_by)',
    'journal_entries -> journal_entry_lines': 'one-to-many',
    'accounts -> journal_entry_lines': 'one-to-many',
  },
} as const;
